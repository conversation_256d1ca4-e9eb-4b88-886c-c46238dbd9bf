<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="足球游戏 - 文字风格的足球游戏模拟游戏" />
    <meta name="keywords" content="足球游戏,游戏,文字游戏,足球,管理" />
    <meta name="author" content="Football Manager Team" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 字体 -->
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;700&display=swap" rel="stylesheet">
    
    <title>足球游戏</title>
    
    <!-- 内联关键CSS -->
    <style>
      /* 加载页面样式 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #001529 0%, #002140 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        font-family: 'JetBrains Mono', 'Courier New', monospace;
        color: #52c41a;
      }
      
      .loading-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 2rem;
        text-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(82, 196, 26, 0.3);
        border-top: 3px solid #52c41a;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        margin-top: 1rem;
        font-size: 0.9rem;
        opacity: 0.8;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 隐藏加载页面 */
      .loaded #loading {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
      }
    </style>
  </head>
  <body>
    <!-- 加载页面 -->
    <div id="loading">
      <div class="loading-title">足球游戏</div>
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载游戏...</div>
    </div>
    
    <!-- 应用根节点 -->
    <div id="app"></div>
    
    <!-- 应用脚本 -->
    <script type="module" src="/src/main.ts"></script>
    
    <!-- 加载完成后隐藏加载页面 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('loaded');
          setTimeout(function() {
            const loading = document.getElementById('loading');
            if (loading) {
              loading.remove();
            }
          }, 500);
        }, 1000);
      });
    </script>
  </body>
</html>
