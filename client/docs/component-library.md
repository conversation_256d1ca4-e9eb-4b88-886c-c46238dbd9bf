# 🎮 足球经理游戏组件库

## 📋 概述

这是一个专为足球经理游戏设计的完整Vue 3组件库，提供了丰富的UI组件和游戏专用组件，支持现代化的游戏界面开发。

## 🏗️ 架构设计

### 组件分层
```
components/
├── common/          # 基础UI组件
│   ├── GameButton.vue
│   ├── GameCard.vue
│   ├── GameModal.vue
│   ├── GameTooltip.vue
│   └── GameProgress.vue
├── game/           # 游戏专用组件
│   ├── HeroCard.vue
│   ├── FormationField.vue
│   ├── TrainingCenter.vue
│   ├── MatchSimulator.vue
│   └── MarketPlace.vue
└── index.ts        # 统一导出
```

### 技术栈
- **Vue 3** - 组合式API + TypeScript
- **Ant Design Vue** - 基础UI框架
- **Less** - CSS预处理器
- **TypeScript** - 类型安全

## 🧩 基础组件

### GameCard - 游戏卡片
通用的卡片组件，支持多种样式和状态。

```vue
<GameCard
  title="球员信息"
  :hoverable="true"
  :selected="false"
  size="default"
  type="primary"
  @click="handleClick"
>
  <template #header>
    <div>自定义头部</div>
  </template>
  
  <div>卡片内容</div>
  
  <template #footer>
    <div>自定义底部</div>
  </template>
</GameCard>
```

**Props:**
- `title?: string` - 卡片标题
- `hoverable?: boolean` - 是否可悬停
- `selected?: boolean` - 是否选中
- `disabled?: boolean` - 是否禁用
- `loading?: boolean` - 加载状态
- `size?: 'small' | 'default' | 'large'` - 尺寸
- `type?: 'default' | 'primary' | 'success' | 'warning' | 'error'` - 类型

### GameModal - 游戏模态框
增强的模态框组件，支持游戏风格的样式。

```vue
<GameModal
  v-model:open="visible"
  title="确认操作"
  :width="600"
  :centered="true"
  @ok="handleOk"
  @cancel="handleCancel"
>
  <div>模态框内容</div>
</GameModal>
```

### GameTooltip - 游戏提示框
功能丰富的提示框组件，支持属性展示和快捷键提示。

```vue
<GameTooltip
  title="球员属性"
  description="查看球员的详细属性信息"
  :attributes="[
    { label: '速度', value: 85, type: 'success' },
    { label: '射门', value: 78, type: 'normal' }
  ]"
  shortcut="Ctrl+I"
>
  <a-button>查看详情</a-button>
</GameTooltip>
```

### GameProgress - 游戏进度条
多功能进度条组件，支持分段标记和动画效果。

```vue
<GameProgress
  :current="1500"
  :total="2000"
  label="经验值"
  type="success"
  :animated="true"
  :show-percent="true"
  :segments="[
    { value: 1000, label: '中级' },
    { value: 2000, label: '高级' }
  ]"
/>
```

## 🎮 游戏组件

### HeroCard - 英雄卡片
专门用于展示球员信息的卡片组件。

```vue
<HeroCard
  :hero="heroData"
  :compact="false"
  :selectable="true"
  :selected="false"
  :show-actions="true"
  @click="selectHero"
  @view-detail="viewDetail"
  @train="startTraining"
/>
```

**特性:**
- 支持紧凑模式和完整模式
- 显示球员属性、技能、状态
- 可选择、可操作
- 稀有度和位置标识

### FormationField - 阵型场地
交互式的足球场阵型配置组件。

```vue
<FormationField
  :formation="formationData"
  :heroes="heroList"
  :interactive="true"
  @hero-assigned="onHeroAssigned"
  @hero-removed="onHeroRemoved"
  @formation-updated="onFormationUpdated"
/>
```

**特性:**
- 真实的足球场背景
- 拖拽式球员配置
- 位置兼容性检查
- 实时阵型评分计算

### TrainingCenter - 训练中心
完整的球员训练管理组件。

```vue
<TrainingCenter />
```

**功能:**
- 训练类型选择
- 球员筛选和批量选择
- 训练配置和成本计算
- 训练队列管理

### MatchSimulator - 比赛模拟器
实时比赛模拟和观看组件。

```vue
<MatchSimulator
  :match="matchData"
  :can-control="true"
  :show-lineups="true"
  :auto-update="true"
  @match-start="onMatchStart"
  @match-end="onMatchEnd"
/>
```

**功能:**
- 实时比赛进度
- 比赛统计展示
- 事件时间线
- 阵容对比
- 比赛控制

### MarketPlace - 转会市场
球员交易市场组件。

```vue
<MarketPlace />
```

**功能:**
- 球员筛选和搜索
- 拍卖和一口价交易
- 出售球员管理
- 价格建议系统

## 🎨 主题系统

### 颜色规范
```less
// 主色调
@primary-color: #52c41a;
@success-color: #52c41a;
@warning-color: #fa8c16;
@error-color: #f5222d;
@info-color: #1890ff;

// 稀有度颜色
@rarity-common: #8c8c8c;
@rarity-uncommon: #52c41a;
@rarity-rare: #1890ff;
@rarity-epic: #722ed1;
@rarity-legendary: #fa8c16;
@rarity-mythic: #f5222d;

// 位置颜色
@position-gk: #f5222d;    // 门将
@position-def: #52c41a;   // 后卫
@position-mid: #1890ff;   // 中场
@position-att: #fa8c16;   // 前锋
```

### 间距规范
```less
@padding-xs: 4px;
@padding-sm: 8px;
@padding-base: 16px;
@padding-lg: 24px;
@padding-xl: 32px;

@margin-xs: 4px;
@margin-sm: 8px;
@margin-base: 16px;
@margin-lg: 24px;
@margin-xl: 32px;
```

## 🛠️ 工具函数

### 货币格式化
```typescript
import { componentUtils } from '@/components'

const formatted = componentUtils.formatCurrency(1500000)
// 输出: "1.5M"
```

### 时间格式化
```typescript
const duration = componentUtils.formatDuration(3600000)
// 输出: "1:00:00"
```

### 稀有度颜色
```typescript
const color = componentUtils.getRarityColor('legendary')
// 输出: "#fa8c16"
```

### 属性评级
```typescript
const grade = componentUtils.getAttributeGrade(85)
// 输出: { grade: 'A', color: '#fa8c16' }
```

## 📦 使用方式

### 全局注册
```typescript
// main.ts
import { createApp } from 'vue'
import { registerGameComponents } from '@/components'
import App from './App.vue'

const app = createApp(App)
registerGameComponents(app)
app.mount('#app')
```

### 按需导入
```vue
<script setup lang="ts">
import { GameCard, HeroCard } from '@/components'
</script>

<template>
  <GameCard title="球员列表">
    <HeroCard
      v-for="hero in heroes"
      :key="hero.id"
      :hero="hero"
    />
  </GameCard>
</template>
```

## 🎯 最佳实践

### 1. 组件命名
- 使用 `Game` 前缀区分游戏组件
- 采用 PascalCase 命名规范
- 名称要具有描述性

### 2. Props设计
- 提供合理的默认值
- 使用TypeScript类型定义
- 支持响应式数据绑定

### 3. 事件处理
- 使用语义化的事件名称
- 传递必要的数据参数
- 支持事件冒泡和阻止

### 4. 样式规范
- 使用Less变量保持一致性
- 支持主题定制
- 响应式设计

### 5. 性能优化
- 合理使用计算属性
- 避免不必要的重渲染
- 懒加载大型组件

## 🔧 开发指南

### 添加新组件
1. 在对应目录创建Vue文件
2. 实现组件逻辑和样式
3. 添加TypeScript类型定义
4. 更新index.ts导出
5. 编写使用文档

### 组件测试
```typescript
// 推荐使用Vue Test Utils
import { mount } from '@vue/test-utils'
import GameCard from '@/components/common/GameCard.vue'

describe('GameCard', () => {
  it('renders correctly', () => {
    const wrapper = mount(GameCard, {
      props: { title: 'Test Card' }
    })
    expect(wrapper.text()).toContain('Test Card')
  })
})
```

## 📈 性能指标

### 构建优化
- **组件懒加载**: 支持按需加载
- **Tree Shaking**: 自动移除未使用代码
- **代码分割**: 合理的chunk分割策略

### 运行时性能
- **虚拟滚动**: 大列表性能优化
- **防抖节流**: 用户交互优化
- **缓存策略**: 数据缓存和复用

## 🚀 未来规划

### Phase 1: 功能完善
- [ ] 添加更多游戏组件
- [ ] 完善主题系统
- [ ] 增强无障碍支持

### Phase 2: 性能优化
- [ ] 虚拟滚动优化
- [ ] 组件懒加载
- [ ] 内存使用优化

### Phase 3: 开发体验
- [ ] Storybook集成
- [ ] 自动化测试
- [ ] 文档生成工具

## 📞 支持

如有问题或建议，请联系开发团队或提交Issue。

---

**版本**: v1.0.0  
**更新时间**: 2024-08-16  
**维护者**: 足球经理开发团队
