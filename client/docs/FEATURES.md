# 🎮 功能特性详解

## 📋 功能概览

本足球经理游戏客户端提供了完整的文字风格游戏体验，包含以下核心功能模块：

## 🏠 **游戏主页 (Dashboard)**

### 核心功能
- **欢迎横幅**: 显示角色名称、当前时间问候语
- **球队统计**: 球队总评、胜率、联赛排名等关键数据
- **快速操作**: 一键进入各个功能模块
- **球队状态**: 当前阵型、主力球员展示
- **最近比赛**: 最近5场比赛结果和详情
- **每日任务**: 任务进度、奖励领取
- **资源状态**: 金币、钻石、体力等资源监控

### 技术实现
```typescript
// 实时数据更新
const teamOverall = computed(() => {
  const mainTeam = heroStore.mainTeamHeroes
  return Math.round(mainTeam.reduce((sum, hero) => sum + hero.overall, 0) / mainTeam.length)
})
```

## ⚽ **球员管理 (Heroes)**

### 核心功能
- **球员列表**: 支持卡片视图和表格视图切换
- **多维筛选**: 按位置、品质、状态、稀有度筛选
- **智能搜索**: 球员姓名模糊搜索
- **排序功能**: 按总评、等级、身价、年龄排序
- **球员详情**: 完整的球员属性、技能、状态展示
- **球员操作**: 训练、出售、租借、释放等操作

### 球员卡片组件
```vue
<HeroCard
  :hero="hero"
  :show-actions="true"
  @view-detail="handleViewDetail"
  @train="handleTrain"
  @sell="handleSell"
/>
```

### 数据结构
```typescript
interface Hero {
  heroId: string
  name: string
  position: string
  overall: number
  attributes: HeroAttributes
  quality: number // 1-5品质等级
  status: 'active' | 'injured' | 'suspended'
  isInFormation: boolean
}
```

## 🎒 **背包系统 (Inventory)**

### 核心功能
- **多标签页**: 按道具类型分类管理
- **道具展示**: 64x64像素的道具槽位
- **道具信息**: 名称、数量、稀有度、绑定状态
- **道具操作**: 使用、出售、丢弃、分割
- **批量操作**: 选择多个道具进行批量处理
- **背包扩展**: 增加背包容量
- **自动整理**: 按类型和稀有度自动排序

### 道具系统
```typescript
interface InventoryItem {
  itemId: string
  configId: number
  name: string
  type: string
  rarity: string // common, uncommon, rare, epic, legendary
  quantity: number
  bind: boolean
  cooldown?: number
}
```

### 使用效果
- **装备道具**: 提升球员属性
- **消耗道具**: 恢复体力、增加经验
- **材料道具**: 用于球员进化、装备强化

## 🏆 **比赛系统 (Matches)**

### 比赛类型
1. **快速比赛**: 
   - PVE挑战: 对战AI，获得经验和奖励
   - PVP对战: 与其他玩家实时对战
   - 友谊赛: 与好友进行友谊比赛

2. **联赛系统**:
   - 联赛排名和积分
   - 联赛奖励系统
   - 升级降级机制

3. **锦标赛**:
   - 报名参赛
   - 淘汰赛制
   - 丰厚奖池

### 实时比赛界面
```vue
<LiveMatchModal
  v-model:visible="showLiveMatch"
  :match-data="currentMatch"
  @match-finished="handleMatchFinished"
/>
```

### 比赛特色
- **实时事件流**: 进球、犯规、换人等事件实时显示
- **比赛统计**: 控球率、射门次数、射正次数等
- **比赛控制**: 暂停、继续、调整速度
- **结果展示**: 比分、奖励、经验获得

## 🎯 **战术配置 (Formations)**

### 核心功能
- **阵型选择**: 4-4-2, 4-3-3, 3-5-2等经典阵型
- **球员位置**: 拖拽调整球员位置
- **战术参数**: 进攻/防守倾向、传球风格
- **阵容保存**: 多套阵容方案保存和切换
- **球员适应性**: 根据位置适应性显示球员评分

### 阵型系统
```typescript
interface Formation {
  id: string
  name: string
  formation: string // "4-4-2"
  positions: FormationPosition[]
  tactics: TacticsConfig
}
```

## 🏋️ **训练系统 (Training)**

### 训练类型
- **基础训练**: 提升基础属性
- **专项训练**: 针对特定位置的专业训练
- **体能训练**: 提升体力和耐力
- **技术训练**: 提升技术和战术理解

### 训练机制
```typescript
interface TrainResultDto {
  success: boolean
  attributeChanges: Record<string, {
    oldValue: number
    newValue: number
    change: number
  }>
  expGained: number
}
```

### 训练特色
- **资源消耗**: 消耗金币和体力
- **成功率**: 根据球员天赋和训练强度
- **批量训练**: 同时训练多个球员
- **训练道具**: 使用道具提升训练效果

## 🏪 **转会市场 (Market)**

### 核心功能
- **球员搜索**: 按位置、价格、属性搜索
- **市场分析**: 价格趋势、热门球员
- **竞价系统**: 实时竞价购买球员
- **出售管理**: 设置球员出售价格和期限
- **交易历史**: 查看历史交易记录

### 市场机制
- **动态定价**: 根据供需关系调整价格
- **税收系统**: 交易手续费
- **冷却时间**: 防止频繁交易

## 👥 **公会系统 (Guild)**

### 核心功能
- **公会创建**: 创建或加入公会
- **成员管理**: 邀请、踢出、权限管理
- **公会活动**: 公会比赛、团队任务
- **公会商店**: 专属道具和奖励
- **聊天系统**: 公会内部交流

## 🔐 **认证系统 (Auth)**

### 登录流程
1. **用户登录**: 用户名/密码验证
2. **服务器选择**: 选择游戏区服
3. **角色选择**: 选择或创建游戏角色
4. **进入游戏**: 建立WebSocket连接

### 安全机制
- **JWT Token**: 访问令牌 + 刷新令牌
- **自动刷新**: Token过期自动刷新
- **权限控制**: 路由级别的权限验证

## 🌐 **实时通信**

### WebSocket功能
- **实时比赛**: 比赛事件实时推送
- **消息通知**: 系统消息、好友消息
- **状态同步**: 球员状态、资源变化
- **在线状态**: 好友在线状态

### 通信协议
```typescript
// 发送消息格式
interface WSMessage {
  id: string
  command: string // 'service.action'
  payload: any
  timestamp: number
}

// 响应消息格式
interface WSResponse {
  id: string
  success: boolean
  data?: any
  error?: string
}
```

## 📱 **响应式设计**

### 适配策略
- **桌面端**: 完整功能，多列布局
- **平板端**: 适配触摸操作，简化布局
- **手机端**: 单列布局，关键功能优先

### 断点设置
```less
@screen-xs: 480px;   // 手机
@screen-sm: 576px;   // 小平板
@screen-md: 768px;   // 平板
@screen-lg: 992px;   // 小桌面
@screen-xl: 1200px;  // 大桌面
```

## 🎨 **UI/UX设计**

### 设计理念
- **文字优先**: 纯文字界面，无图片依赖
- **终端美学**: 深色背景，绿色高亮
- **信息密度**: 在有限空间内展示最多信息
- **操作效率**: 减少点击次数，提升操作效率

### 交互特色
- **悬停效果**: 鼠标悬停显示详细信息
- **快捷键**: 支持键盘快捷操作
- **拖拽操作**: 球员位置调整、道具移动
- **批量选择**: Ctrl+点击多选操作

## 🔧 **性能优化**

### 前端优化
- **代码分割**: 路由级别的懒加载
- **组件缓存**: keep-alive缓存页面状态
- **虚拟滚动**: 大列表性能优化
- **防抖节流**: 搜索和筛选操作优化

### 网络优化
- **请求合并**: 批量API调用
- **缓存策略**: HTTP缓存和本地缓存
- **WebSocket**: 减少HTTP请求频率
- **数据压缩**: gzip压缩传输数据

## 📊 **数据统计**

### 统计维度
- **球员统计**: 属性分布、成长轨迹
- **比赛统计**: 胜率、进球数、失球数
- **经济统计**: 收入支出、资产变化
- **活跃统计**: 登录天数、游戏时长

### 可视化展示
- **图表组件**: 使用Ant Design Charts
- **数据面板**: 关键指标仪表盘
- **趋势分析**: 时间序列数据展示
