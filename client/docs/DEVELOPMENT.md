# 开发指南

## 🛠️ 开发环境设置

### 前置要求
- Node.js >= 18.0.0
- npm >= 9.0.0
- 现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）

### 安装依赖
```bash
cd client
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看应用

## 📁 项目结构说明

```
client/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 资源文件
│   ├── components/        # 组件
│   │   ├── common/        # 通用组件
│   │   ├── game/          # 游戏组件
│   │   └── ui/            # UI组件
│   ├── views/             # 页面组件
│   ├── stores/            # 状态管理
│   ├── services/          # API服务
│   ├── utils/             # 工具函数
│   ├── types/             # 类型定义
│   ├── router/            # 路由配置
│   └── styles/            # 样式文件
├── docs/                  # 文档
└── scripts/               # 脚本
```

## 🎨 设计系统

### 颜色规范
- **主色调**: `#52c41a` (绿色，经典终端风格)
- **背景色**: `#001529` (深蓝色)
- **文字色**: `#ffffff` (白色)
- **边框色**: `#434343` (灰色)

### 字体规范
- **主字体**: 系统默认字体栈
- **等宽字体**: `'Courier New', 'Monaco', 'Menlo'` (用于游戏文本)

### 组件规范
- 所有游戏相关组件使用 `game-` 前缀
- 通用组件放在 `components/common/`
- 游戏特定组件放在 `components/game/`

## 🔧 开发规范

### 代码风格
- 使用 TypeScript
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 组件使用 Composition API

### 命名规范
- 组件名使用 PascalCase
- 文件名使用 kebab-case
- 变量名使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### Git 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🧪 测试

### 运行测试
```bash
npm run test
```

### 测试覆盖率
```bash
npm run test:coverage
```

### 测试规范
- 组件测试使用 Vue Test Utils
- 工具函数测试使用 Vitest
- 测试文件命名: `*.test.ts` 或 `*.spec.ts`

## 📦 构建部署

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

### 部署
构建后的文件在 `dist/` 目录，可以部署到任何静态文件服务器。

## 🔍 调试技巧

### Vue DevTools
安装 Vue DevTools 浏览器扩展进行调试

### 网络调试
- HTTP请求在浏览器开发者工具的 Network 标签查看
- WebSocket连接在 Console 中有详细日志

### 状态调试
- Pinia状态在 Vue DevTools 中可以查看和修改
- 使用 `console.log` 进行简单调试

## 🚀 性能优化

### 代码分割
- 路由级别的代码分割已配置
- 大型组件使用动态导入

### 资源优化
- 图片使用 WebP 格式
- 字体使用 font-display: swap
- CSS 使用 Tree Shaking

### 缓存策略
- HTTP请求使用适当的缓存头
- 静态资源使用长期缓存

## 🐛 常见问题

### 开发服务器启动失败
1. 检查端口 5173 是否被占用
2. 清除 node_modules 重新安装依赖
3. 检查 Node.js 版本是否符合要求

### WebSocket连接失败
1. 确保后端服务器正在运行
2. 检查防火墙设置
3. 查看浏览器控制台错误信息

### 样式不生效
1. 检查 Less 语法是否正确
2. 确保变量文件正确导入
3. 清除浏览器缓存

## 📚 相关文档

- [Vue 3 官方文档](https://vuejs.org/)
- [Ant Design Vue 文档](https://antdv.com/)
- [Vite 官方文档](https://vitejs.dev/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建 Pull Request
