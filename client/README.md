# 足球游戏 - Web客户端

## 🎮 项目概述

基于Vue 3 + Ant Design Vue的文字风格足球游戏客户端，采用现代Web技术栈，提供沉浸式的文字游戏体验。

## 🛠️ 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5+
- **UI库**: Ant Design Vue 4.x
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite 5+
- **网络通信**: Socket.IO Client 4.x + Axios
- **样式**: Less + CSS Variables

## 📁 项目结构

```
client/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 资源文件
│   ├── components/        # 通用组件
│   │   ├── common/        # 基础组件
│   │   ├── game/          # 游戏组件
│   │   └── ui/            # UI组件
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证页面
│   │   ├── game/          # 游戏页面
│   │   └── system/        # 系统页面
│   ├── stores/            # Pinia状态管理
│   ├── services/          # API服务
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript类型定义
│   ├── router/            # 路由配置
│   ├── styles/            # 全局样式
│   └── main.ts            # 应用入口
├── package.json
├── vite.config.ts
├── tsconfig.json
└── README.md
```

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🎯 核心特性

- 📱 **响应式设计**: 支持桌面和移动设备
- 🎨 **文字风格UI**: 纯文字界面，无图片资源
- ⚡ **实时通信**: WebSocket实时数据同步
- 🔐 **安全认证**: JWT + 双层Token机制
- 🌐 **多区服支持**: 完整的分区分服架构
- 📊 **数据可视化**: 表格、图表展示游戏数据
- 🎮 **游戏功能**: 球员管理、战术配置、比赛系统

## 📖 开发指南

详细的开发文档请参考 `docs/` 目录下的相关文档。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
