{"name": "football-game-client", "version": "1.0.0", "description": "足球游戏Web客户端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit", "check-compile": "node scripts/check-compile.js", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.0.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "glob": "^11.0.3", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "socket.io-client": "^4.7.4", "vue": "^3.4.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "less": "^4.2.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vitest": "^1.1.0", "vue-tsc": "^3.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}