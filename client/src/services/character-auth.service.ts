/**
 * 角色认证服务
 * 
 * 基于服务端真实接口实现：
 * - HTTP接口：账号登录、角色Token生成
 * - WebSocket接口：角色登录、游戏业务
 * - 双层Token认证机制
 */

import { BaseHttpService } from './base.service'
import { wsService } from './websocket'
import type { 
  LoginCredentials, 
  RegisterData, 
  Character, 
  CreateCharacterDto,
  ApiResponse,
  ServiceResponse
} from '@/types'

export class CharacterAuthService extends BaseHttpService {
  constructor() {
    super('/api/auth') // 认证服务基础路径
  }

  /**
   * 账号登录 - HTTP接口
   * POST /api/auth/auth/login
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<{
    user: any
    accessToken: string
    refreshToken: string
  }>> {
    const response = await this.post<{
      user: any
      accessToken: string
      refreshToken: string
    }>('/auth/login', credentials)

    // 保存账号Token
    if (response.success && response.data) {
      localStorage.setItem('accessToken', response.data.accessToken)
      localStorage.setItem('refreshToken', response.data.refreshToken)
      localStorage.setItem('user', JSON.stringify(response.data.user))
    }

    return response
  }

  /**
   * 账号注册 - HTTP接口
   * POST /api/auth/auth/register
   */
  async register(data: RegisterData): Promise<ApiResponse<{
    user: any
  }>> {
    return this.post<{
      user: any
    }>('/auth/register', data)
  }

  /**
   * 获取角色列表 - HTTP接口
   * GET /api/auth/character/list
   */
  async getCharacterList(serverId: string): Promise<ApiResponse<Character[]>> {
    return this.get<Character[]>(`/character/list?serverId=${serverId}`)
  }

  /**
   * 创建角色 - HTTP接口
   * POST /api/auth/character/create
   */
  async createCharacter(data: CreateCharacterDto): Promise<ApiResponse<Character>> {
    return this.post<Character>('/character/create', data)
  }

  /**
   * 生成角色Token - HTTP接口
   * POST /api/auth/character-auth/generate-token
   */
  async generateCharacterToken(data: {
    userId: string
    characterId: string
    serverId: string
  }): Promise<ApiResponse<{
    characterToken: string
    tokenType: string
    expiresIn: number
    character: Character
  }>> {
    const response = await this.post<{
      characterToken: string
      tokenType: string
      expiresIn: number
      character: Character
    }>('/character-auth/generate-token', data)

    // 保存角色Token
    if (response.success && response.data) {
      localStorage.setItem('characterToken', response.data.characterToken)
      localStorage.setItem('currentCharacter', JSON.stringify(response.data.character))
    }

    return response
  }

  /**
   * 角色登录 - WebSocket接口
   * 使用角色Token进行WebSocket认证
   */
  async characterLogin(characterId: string): Promise<ServiceResponse<any>> {
    // 确保WebSocket已连接
    await wsService.connect()

    // 发送角色登录消息
    return wsService.sendMessage('character.login', {
      characterId: characterId
    })
  }

  /**
   * 完整的角色选择和登录流程
   */
  async selectAndLoginCharacter(characterId: string, serverId: string): Promise<{
    character: Character
    token: string
  }> {
    try {
      // 1. 获取用户信息
      const userStr = localStorage.getItem('user')
      if (!userStr) {
        throw new Error('用户未登录')
      }
      const user = JSON.parse(userStr)

      // 2. 生成角色Token
      const tokenResponse = await this.generateCharacterToken({
        userId: user.id,
        characterId: characterId,
        serverId: serverId
      })

      if (!tokenResponse.success || !tokenResponse.data) {
        throw new Error(tokenResponse.error?.message || '生成角色Token失败')
      }

      // 3. 使用角色Token进行WebSocket登录
      const loginResponse = await this.characterLogin(characterId)

      if (loginResponse.code !== 0) {
        throw new Error(loginResponse.message || '角色登录失败')
      }

      return {
        character: tokenResponse.data.character,
        token: tokenResponse.data.characterToken
      }

    } catch (error: any) {
      console.error('[CharacterAuth] 角色登录失败:', error)
      throw error
    }
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    try {
      // 1. 断开WebSocket连接
      wsService.disconnect()

      // 2. 清除本地存储
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('characterToken')
      localStorage.removeItem('user')
      localStorage.removeItem('currentCharacter')

      // 3. 调用服务端登出接口（可选）
      const refreshToken = localStorage.getItem('refreshToken')
      if (refreshToken) {
        await this.post('/auth/logout', { refreshToken })
      }

    } catch (error) {
      console.error('[CharacterAuth] 登出失败:', error)
      // 即使服务端调用失败，也要清除本地数据
    }
  }

  /**
   * 检查认证状态
   */
  isAuthenticated(): boolean {
    const accessToken = localStorage.getItem('accessToken')
    const characterToken = localStorage.getItem('characterToken')
    return !!(accessToken && characterToken)
  }

  /**
   * 获取当前角色
   */
  getCurrentCharacter(): Character | null {
    const characterStr = localStorage.getItem('currentCharacter')
    return characterStr ? JSON.parse(characterStr) : null
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): any | null {
    const userStr = localStorage.getItem('user')
    return userStr ? JSON.parse(userStr) : null
  }
}

// 导出单例
export const characterAuthService = new CharacterAuthService()
