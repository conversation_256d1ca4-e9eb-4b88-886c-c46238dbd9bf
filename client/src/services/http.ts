import axios, { 
  type AxiosInstance, 
  type AxiosRequestConfig, 
  type AxiosResponse,
  type InternalAxiosRequestConfig
} from 'axios'
import type { ApiResponse } from '@/types'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'

/**
 * HTTP客户端服务
 * 封装axios，提供统一的HTTP请求接口
 */
class HttpService {
  private instance: AxiosInstance
  private isRefreshing = false
  private failedQueue: Array<{
    resolve: (value: any) => void
    reject: (error: any) => void
  }> = []

  constructor() {
    this.instance = axios.create({
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  /**
   * 设置拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // 添加认证token
        const authStore = useAuthStore()
        if (authStore.isAuthenticated) {
          config.headers.Authorization = `Bearer ${authStore.accessToken}`
        }

        // 添加基础URL
        const globalStore = useGlobalStore()
        if (!config.baseURL) {
          config.baseURL = globalStore.appConfig.apiBaseUrl
        }

        // 添加请求ID用于追踪
        config.headers['X-Request-ID'] = this.generateRequestId()

        console.log(`[HTTP] ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data
        })

        return config
      },
      (error) => {
        console.error('[HTTP] 请求拦截器错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`[HTTP] ${response.status} ${response.config.url}`, response.data)

        // 检查业务状态码
        if (response.data && !response.data.success) {
          const error = new Error(response.data.error?.message || '请求失败')
          error.name = response.data.error?.code || 'API_ERROR'
          return Promise.reject(error)
        }

        return response
      },
      async (error) => {
        const originalRequest = error.config

        console.error(`[HTTP] ${error.response?.status || 'NETWORK'} ${originalRequest?.url}`, {
          error: error.message,
          response: error.response?.data
        })

        // 处理401未授权错误
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // 如果正在刷新token，将请求加入队列
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject })
            }).then(token => {
              originalRequest.headers.Authorization = `Bearer ${token}`
              return this.instance(originalRequest)
            }).catch(err => {
              return Promise.reject(err)
            })
          }

          originalRequest._retry = true
          this.isRefreshing = true

          try {
            const authStore = useAuthStore()
            const newToken = await authStore.refreshAccessToken()
            
            // 处理队列中的请求
            this.processQueue(null, newToken)
            
            // 重试原始请求
            originalRequest.headers.Authorization = `Bearer ${newToken}`
            return this.instance(originalRequest)
          } catch (refreshError) {
            // 刷新失败，处理队列并登出
            this.processQueue(refreshError, null)
            const authStore = useAuthStore()
            await authStore.logout()
            return Promise.reject(refreshError)
          } finally {
            this.isRefreshing = false
          }
        }

        // 处理其他HTTP错误
        const globalStore = useGlobalStore()
        let errorMessage = '网络请求失败'

        if (error.response) {
          // 服务器响应错误
          switch (error.response.status) {
            case 400:
              errorMessage = '请求参数错误'
              break
            case 403:
              errorMessage = '没有权限访问'
              break
            case 404:
              errorMessage = '请求的资源不存在'
              break
            case 500:
              errorMessage = '服务器内部错误'
              break
            case 502:
              errorMessage = '网关错误'
              break
            case 503:
              errorMessage = '服务暂时不可用'
              break
            default:
              errorMessage = error.response.data?.error?.message || `请求失败 (${error.response.status})`
          }
        } else if (error.request) {
          // 网络错误
          errorMessage = '网络连接失败，请检查网络设置'
        }

        // 显示错误通知
        globalStore.addNotification({
          type: 'error',
          title: 'HTTP请求错误',
          message: errorMessage
        })

        return Promise.reject(error)
      }
    )
  }

  /**
   * 处理token刷新队列
   */
  private processQueue(error: any, token: string | null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve(token)
      }
    })
    
    this.failedQueue = []
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get<ApiResponse<T>>(url, config)
    return response.data.data
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config)
    return response.data.data
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })

    return response.data.data
  }

  /**
   * 下载文件
   */
  async download(url: string, filename?: string): Promise<void> {
    const response = await this.instance.get(url, {
      responseType: 'blob'
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  /**
   * 获取原始axios实例
   */
  getInstance(): AxiosInstance {
    return this.instance
  }
}

// 导出单例实例
export const httpService = new HttpService()
export default httpService
