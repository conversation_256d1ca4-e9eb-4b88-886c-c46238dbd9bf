import type { 
  User, 
  LoginCredentials, 
  RegisterData, 
  ServerInfo, 
  Character 
} from '@/types'
import { httpService } from './http'

/**
 * 认证服务API
 * 处理用户认证、服务器选择、角色管理等相关API调用
 */
class AuthService {
  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials) {
    return await httpService.post<{
      user: User
      accessToken: string
      refreshToken: string
    }>('/api/auth/auth/login', credentials)
  }

  /**
   * 用户注册
   */
  async register(data: RegisterData) {
    return await httpService.post<{
      user: User
    }>('/api/auth/auth/register', data)
  }

  /**
   * 用户登出
   */
  async logout(refreshToken: string) {
    return await httpService.post('/api/auth/auth/logout', {
      refreshToken
    })
  }

  /**
   * 验证Token有效性
   */
  async verifyToken(token: string) {
    return await httpService.post<{
      valid: boolean
      user?: User
    }>('/api/auth/auth/verify', { token })
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshToken: string) {
    return await httpService.post<{
      accessToken: string
      refreshToken?: string
    }>('/api/auth/auth/refresh', {
      refreshToken
    })
  }

  /**
   * 获取可用服务器列表
   */
  async getServers(): Promise<ServerInfo[]> {
    return await httpService.get<ServerInfo[]>('/api/auth/server/list')
  }

  /**
   * 选择服务器
   */
  async selectServer(serverId: string) {
    return await httpService.post('/api/auth/server/select', {
      serverId
    })
  }

  /**
   * 获取角色列表
   */
  async getCharacters(serverId: string): Promise<Character[]> {
    return await httpService.get<Character[]>(`/api/character/character/list?serverId=${serverId}`)
  }

  /**
   * 选择角色
   */
  async selectCharacter(characterId: string) {
    return await httpService.post('/api/character/character/select', {
      characterId
    })
  }

  /**
   * 创建新角色
   */
  async createCharacter(data: { name: string; serverId: string }): Promise<Character> {
    return await httpService.post<Character>('/api/character/character/create', data)
  }

  /**
   * 删除角色
   */
  async deleteCharacter(characterId: string) {
    return await httpService.delete(`/api/character/character/${characterId}`)
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<User> {
    return await httpService.get<User>('/api/auth/auth/profile')
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(data: Partial<User>): Promise<User> {
    return await httpService.put<User>('/api/auth/auth/profile', data)
  }

  /**
   * 修改密码
   */
  async changePassword(data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }) {
    return await httpService.post('/api/auth/auth/change-password', data)
  }

  /**
   * 忘记密码
   */
  async forgotPassword(email: string) {
    return await httpService.post('/api/auth/auth/forgot-password', {
      email
    })
  }

  /**
   * 重置密码
   */
  async resetPassword(data: {
    token: string
    newPassword: string
    confirmPassword: string
  }) {
    return await httpService.post('/api/auth/auth/reset-password', data)
  }
}

// 导出单例实例
export const authService = new AuthService()
export default authService
