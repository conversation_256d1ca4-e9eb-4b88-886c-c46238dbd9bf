import { BaseHttpService } from './base.service'
import type {
  User,
  LoginCredentials,
  RegisterData,
  ServerInfo,
  Character,
  ApiResponse
} from '@/types'
import httpService from "@services/http";

/**
 * 认证服务API
 * 处理用户认证、服务器选择、角色管理等相关API调用
 *
 * 基于服务端真实接口架构：
 * - 使用网关HTTP代理 (/api/auth/auth/*)
 * - 双层Token认证机制
 * - 标准响应格式处理
 * - 统一错误处理和重试
 */
class AuthService extends BaseHttpService {
  constructor() {
    super('/api/auth/auth') // 网关代理基础路径
  }
  /**
   * 用户登录
   * POST /login
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<{
    user: User
    accessToken: string
    refreshToken: string
  }>> {
    return this.post<{
      user: User
      accessToken: string
      refreshToken: string
    }>('/login', credentials)
  }

  /**
   * 用户注册
   * POST /register
   */
  async register(data: RegisterData): Promise<ApiResponse<{
    user: User
  }>> {
    return this.post<{
      user: User
    }>('/register', data)
  }

  /**
   * 用户登出
   * POST /logout
   */
  async logout(refreshToken: string): Promise<ApiResponse<void>> {
    return this.post<void>('/logout', {
      refreshToken
    })
  }

  /**
   * 验证Token有效性
   * POST /verify
   */
  async verifyToken(token: string): Promise<ApiResponse<{
    valid: boolean
    user?: User
  }>> {
    return this.post<{
      valid: boolean
      user?: User
    }>('/verify', { token })
  }

  /**
   * 刷新访问令牌
   * POST /refresh
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<{
    accessToken: string
    refreshToken?: string
  }>> {
    return this.post<{
      accessToken: string
      refreshToken?: string
    }>('/refresh', {
      refreshToken
    })
  }

  /**
   * 获取可用服务器列表
   * GET /api/auth/server/list (通过网关代理)
   */
  async getServers(): Promise<ApiResponse<ServerInfo[]>> {
    // 服务器列表接口在auth服务的server模块
    return this.get<ServerInfo[]>('/server/list')
  }

  /**
   * 选择服务器
   * POST /api/auth/server/select (通过网关代理)
   */
  async selectServer(serverId: string): Promise<ApiResponse<void>> {
    return this.post<void>('/server/select', {
      serverId
    })
  }

  /**
   * 获取角色列表
   * GET /api/character/character/list (通过网关代理到character服务)
   */
  async getCharacters(serverId: string): Promise<ApiResponse<Character[]>> {
    // 角色相关接口通过网关代理到character服务
    const baseService = new BaseHttpService('/api/character/character')
    return baseService.get<Character[]>(`/list?serverId=${serverId}`)
  }

  /**
   * 选择角色
   * POST /api/character/character/select (通过网关代理到character服务)
   */
  async selectCharacter(characterId: string): Promise<ApiResponse<void>> {
    const baseService = new BaseHttpService('/api/character/character')
    return baseService.post<void>('/select', {
      characterId
    })
  }

  /**
   * 创建新角色
   * POST /api/character/character/create (通过网关代理到character服务)
   */
  async createCharacter(data: { name: string; serverId: string }): Promise<ApiResponse<Character>> {
    const baseService = new BaseHttpService('/api/character/character')
    return baseService.post<Character>('/create', data)
  }

  /**
   * 删除角色
   */
  async deleteCharacter(characterId: string) {
    return await httpService.delete(`/api/character/character/${characterId}`)
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<User> {
    return await httpService.get<User>('/api/auth/auth/profile')
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(data: Partial<User>): Promise<User> {
    return await httpService.put<User>('/api/auth/auth/profile', data)
  }

  /**
   * 修改密码
   */
  async changePassword(data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }) {
    return await httpService.post('/api/auth/auth/change-password', data)
  }

  /**
   * 忘记密码
   */
  async forgotPassword(email: string) {
    return await httpService.post('/api/auth/auth/forgot-password', {
      email
    })
  }

  /**
   * 重置密码
   */
  async resetPassword(data: {
    token: string
    newPassword: string
    confirmPassword: string
  }) {
    return await httpService.post('/api/auth/auth/reset-password', data)
  }
}

// 导出单例实例
export const authService = new AuthService()
export default authService
