/**
 * 统一服务基类
 * 
 * 提供与服务端完全对齐的通信能力：
 * - HTTP请求统一处理
 * - WebSocket消息统一格式
 * - 错误处理和重试机制
 * - 响应格式标准化
 */

import type { 
  ServiceResponse, 
  ApiResponse, 
  WSMessage, 
  WSResponse 
} from '@/types'

/**
 * HTTP服务基类
 * 用于认证相关的HTTP请求（通过网关代理）
 */
export abstract class BaseHttpService {
  protected baseURL: string
  protected timeout: number = 30000

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL
  }

  /**
   * 统一HTTP请求方法
   * 自动处理认证头、错误处理、重试机制
   */
  protected async request<T = any>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    url: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const { 
      headers = {}, 
      timeout = this.timeout,
      retries = 3,
      retryDelay = 1000 
    } = options

    // 构建完整URL
    const fullUrl = `${this.baseURL}${url}`
    
    // 准备请求头
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers
    }

    // 添加认证头
    const token = this.getAuthToken()
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`
    }

    // 执行请求（带重试机制）
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), timeout)

        const response = await fetch(fullUrl, {
          method,
          headers: requestHeaders,
          body: data ? JSON.stringify(data) : undefined,
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        // 解析响应
        const result = await this.parseResponse<T>(response)
        
        // 成功则返回
        if (result.success || attempt === retries) {
          return result
        }

      } catch (error) {
        // 最后一次尝试失败，抛出错误
        if (attempt === retries) {
          throw this.createError('NETWORK_ERROR', '网络请求失败', error)
        }
        
        // 等待后重试
        await this.delay(retryDelay * Math.pow(2, attempt))
      }
    }

    throw this.createError('MAX_RETRIES_EXCEEDED', '请求重试次数超限')
  }

  /**
   * 解析HTTP响应
   */
  private async parseResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json()
      
      if (!response.ok) {
        return {
          success: false,
          error: {
            code: data.code || 'HTTP_ERROR',
            message: data.message || `HTTP ${response.status}`,
            details: data
          },
          timestamp: new Date().toISOString()
        }
      }

      // 服务端标准响应格式适配
      if (typeof data.success === 'boolean') {
        return {
          success: data.success,
          data: data.data,
          error: data.error,
          timestamp: data.timestamp || new Date().toISOString()
        }
      }

      // 直接数据响应
      return {
        success: true,
        data: data,
        timestamp: new Date().toISOString()
      }

    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PARSE_ERROR',
          message: '响应解析失败',
          details: error
        },
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取认证Token
   */
  protected getAuthToken(): string | null {
    // 从localStorage或store获取token
    return localStorage.getItem('accessToken')
  }

  /**
   * 创建标准错误对象
   */
  protected createError(code: string, message: string, details?: any): Error {
    const error = new Error(message) as any
    error.code = code
    error.details = details
    return error
  }

  /**
   * 延迟工具函数
   */
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 便捷方法
  protected get<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('GET', url, undefined, options)
  }

  protected post<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('POST', url, data, options)
  }

  protected put<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', url, data, options)
  }

  protected patch<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('PATCH', url, data, options)
  }

  protected delete<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', url, undefined, options)
  }
}

/**
 * WebSocket服务基类
 * 用于游戏业务的实时通信
 */
export abstract class BaseWebSocketService {
  protected wsUrl: string
  protected reconnectAttempts: number = 0
  protected maxReconnectAttempts: number = 5
  protected reconnectDelay: number = 1000

  constructor(wsUrl: string) {
    this.wsUrl = wsUrl
  }

  /**
   * 发送WebSocket消息
   * 使用服务端标准格式：{ command: 'service.action', payload: any }
   */
  protected async sendMessage<T = any>(
    command: string, 
    payload: any = {},
    timeout: number = 30000
  ): Promise<ServiceResponse<T>> {
    // 这里会被具体的WebSocket服务实现
    throw new Error('sendMessage must be implemented by subclass')
  }

  /**
   * 获取角色认证Token
   */
  protected getCharacterToken(): string | null {
    return localStorage.getItem('characterToken')
  }
}

/**
 * 请求选项接口
 */
export interface RequestOptions {
  headers?: Record<string, string>
  timeout?: number
  retries?: number
  retryDelay?: number
}

/**
 * 服务错误类
 */
export class ServiceError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message)
    this.name = 'ServiceError'
  }
}
