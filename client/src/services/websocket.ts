import { io, type Socket } from 'socket.io-client'
import { ref, type Ref } from 'vue'
import { BaseWebSocketService } from './base.service'
import type { WSMessage, WSResponse, ServiceResponse } from '@/types'
import {useGlobalStore} from "@stores/global";
import {useAuthStore} from "@stores/auth";

/**
 * WebSocket服务
 * 管理与游戏服务器的实时通信
 *
 * 基于服务端真实架构：
 * - WebSocket网关统一入口
 * - 角色Token认证机制
 * - 标准消息格式：{ command: 'service.action', payload: any }
 * - 标准响应格式：{ code: number, message: string, data?: T }
 * - 自动重连和错误处理
 */
class WebSocketService {
  private socket: Socket | null = null
  private messageHandlers = new Map<string, (data: any) => void>()
  private pendingRequests = new Map<string, {
    resolve: (value: any) => void
    reject: (error: any) => void
    timeout: NodeJS.Timeout
  }>()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private isConnecting = false

  // 连接状态
  public connectionState: Ref<'disconnected' | 'connecting' | 'connected'> = ref('disconnected')

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.socket?.connected || this.isConnecting) {
      return
    }

    this.isConnecting = true
    const globalStore = useGlobalStore()
    const authStore = useAuthStore()

    try {
      // 创建Socket连接
      const wsUrl = globalStore.appConfig.wsUrl || import.meta.env.VITE_WS_URL || 'ws://localhost:3000'

      // 获取认证Token（优先使用角色Token）
      const characterToken = this.getCharacterToken()
      const accessToken = localStorage.getItem('accessToken')

      this.socket = io(wsUrl, {
        auth: {
          // 双层认证：账号Token + 角色Token
          accessToken: accessToken,
          characterToken: characterToken
        },
        transports: ['websocket', 'polling'],
        reconnection: false, // 手动控制重连
        timeout: 10000
      })

      // 设置事件监听器
      this.setupEventListeners()

      // 等待连接建立
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket连接超时'))
        }, 10000)

        this.socket!.on('connect', () => {
          clearTimeout(timeout)
          console.log('[WebSocket] 连接已建立')
          this.reconnectAttempts = 0
          resolve()
        })

        this.socket!.on('connect_error', (error) => {
          clearTimeout(timeout)
          console.error('[WebSocket] 连接失败:', error)
          reject(error)
        })
      })

      globalStore.addNotification({
        type: 'success',
        title: 'WebSocket连接成功',
        message: '实时通信已建立'
      })

    } catch (error) {
      console.error('[WebSocket] 连接失败:', error)
      globalStore.addNotification({
        type: 'error',
        title: 'WebSocket连接失败',
        message: '无法建立实时通信连接'
      })
      throw error
    } finally {
      this.isConnecting = false
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    if (this.socket) {
      console.log('[WebSocket] 主动断开连接')
      this.socket.disconnect()
      this.socket = null
    }

    // 清理待处理的请求
    this.pendingRequests.forEach(({ reject, timeout }) => {
      clearTimeout(timeout)
      reject(new Error('WebSocket连接已断开'))
    })
    this.pendingRequests.clear()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.socket) return

    // 连接事件
    this.socket.on('connect', () => {
      console.log('[WebSocket] 已连接到服务器')
    })

    this.socket.on('disconnect', (reason) => {
      console.log('[WebSocket] 连接断开:', reason)
      this.handleDisconnect(reason)
    })

    this.socket.on('connect_error', (error) => {
      console.error('[WebSocket] 连接错误:', error)
      this.handleConnectionError(error)
    })

    // 消息响应
    this.socket.on('response', (response: WSResponse) => {
      this.handleResponse(response)
    })

    // 游戏事件
    this.socket.on('game.notification', (data) => {
      this.handleGameNotification(data)
    })

    this.socket.on('game.update', (data) => {
      this.handleGameUpdate(data)
    })

    this.socket.on('match.event', (data) => {
      this.handleMatchEvent(data)
    })

    // 系统事件
    this.socket.on('system.maintenance', (data) => {
      this.handleSystemMaintenance(data)
    })
  }

  /**
   * 处理断开连接
   */
  private handleDisconnect(reason: string): void {
    const globalStore = useGlobalStore()

    if (reason === 'io server disconnect') {
      // 服务器主动断开，不重连
      globalStore.addNotification({
        type: 'warning',
        title: '连接已断开',
        message: '服务器主动断开了连接'
      })
    } else {
      // 其他原因断开，尝试重连
      this.attemptReconnect()
    }
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(error: any): void {
    console.error('[WebSocket] 连接错误:', error)
    
    if (error.message?.includes('unauthorized')) {
      // 认证失败，需要重新登录
      const authStore = useAuthStore()
      authStore.logout()
    } else {
      this.attemptReconnect()
    }
  }

  /**
   * 尝试重连
   */
  private async attemptReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      const globalStore = useGlobalStore()
      globalStore.addNotification({
        type: 'error',
        title: '重连失败',
        message: '无法重新连接到服务器，请刷新页面'
      })
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

    console.log(`[WebSocket] ${delay}ms后尝试第${this.reconnectAttempts}次重连`)

    setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        console.error('[WebSocket] 重连失败:', error)
      }
    }, delay)
  }

  /**
   * 处理响应消息 - 基于服务端ServiceResponse格式
   */
  private handleResponse(response: any): void {
    // 服务端返回格式: { code: number, message: string, data?: any }
    const messageId = response.id || this.getLastMessageId()
    const pending = this.pendingRequests.get(messageId)

    if (pending) {
      clearTimeout(pending.timeout)
      this.pendingRequests.delete(messageId)

      // 服务端code=0表示成功
      if (response.code === 0) {
        pending.resolve(response)
      } else {
        pending.reject(new Error(response.message || '请求失败'))
      }
    }
  }

  /**
   * 获取最后一个消息ID（用于没有ID的响应）
   */
  private getLastMessageId(): string {
    const keys = Array.from(this.pendingRequests.keys())
    return keys[keys.length - 1] || ''
  }

  /**
   * 处理游戏通知
   */
  private handleGameNotification(data: any): void {
    const globalStore = useGlobalStore()
    globalStore.addNotification({
      type: data.type || 'info',
      title: data.title || '游戏通知',
      message: data.message
    })
  }

  /**
   * 处理游戏更新
   */
  private handleGameUpdate(data: any): void {
    // 触发相应的消息处理器
    const handler = this.messageHandlers.get('game.update')
    if (handler) {
      handler(data)
    }
  }

  /**
   * 处理比赛事件
   */
  private handleMatchEvent(data: any): void {
    const handler = this.messageHandlers.get('match.event')
    if (handler) {
      handler(data)
    }
  }

  /**
   * 处理系统维护
   */
  private handleSystemMaintenance(data: any): void {
    const globalStore = useGlobalStore()
    globalStore.addNotification({
      type: 'warning',
      title: '系统维护通知',
      message: data.message || '系统即将进行维护'
    })
  }

  /**
   * 发送消息 - 基于服务端真实接口格式
   * 参考 scripts/common/websocket-client.js 的成功实现
   */
  async sendMessage<T = any>(command: string, payload: any = {}, timeout = 30000): Promise<ServiceResponse<T>> {
    if (!this.socket?.connected) {
      throw new Error('WebSocket未连接')
    }

    const messageId = this.generateMessageId()

    // 使用服务端标准消息格式
    const message = {
      id: messageId,
      command: command,  // 格式: 'service.action' 如 'character.getInfo'
      payload: {
        ...payload,
        // 自动添加认证Token
        token: this.getCharacterToken() || localStorage.getItem('accessToken')
      }
    }

    return new Promise<ServiceResponse<T>>((resolve, reject) => {
      // 设置超时
      const timeoutHandle = setTimeout(() => {
        this.pendingRequests.delete(messageId)
        reject(new Error('请求超时'))
      }, timeout)

      // 设置响应监听器
      const responseHandler = (response: any) => {
        if (response.id === messageId) {
          this.socket!.off('message', responseHandler)
          clearTimeout(timeoutHandle)
          this.pendingRequests.delete(messageId)

          // 服务端返回格式: { code: number, message: string, data?: T }
          resolve(response)
        }
      }

      // 保存待处理请求
      this.pendingRequests.set(messageId, {
        resolve: responseHandler,
        reject,
        timeout: timeoutHandle
      })

      // 监听响应
      this.socket!.on('message', responseHandler)

      // 发送消息 - 使用 'message' 事件，这是服务端的标准格式
      this.socket!.emit('message', message)

      console.log(`[WebSocket] 发送消息: ${command}`, message)
    })
  }

  /**
   * 订阅消息
   */
  subscribe(event: string, handler: (data: any) => void): void {
    this.messageHandlers.set(event, handler)
  }

  /**
   * 取消订阅
   */
  unsubscribe(event: string): void {
    this.messageHandlers.delete(event)
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取角色Token
   */
  private getCharacterToken(): string | null {
    return localStorage.getItem('characterToken')
  }

  /**
   * 检查连接状态
   */
  get isConnected(): boolean {
    return this.socket?.connected || false
  }


}

// 导出单例实例
export const wsService = new WebSocketService()
export default wsService
