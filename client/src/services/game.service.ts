/**
 * 游戏业务服务
 * 
 * 基于服务端真实WebSocket接口实现：
 * - 角色信息管理
 * - 英雄管理
 * - 战术配置
 * - 训练系统
 * - 比赛系统
 * 
 * 参考 apps/match/scripts 下的成功测试案例
 */

import { wsService } from './websocket'
import type { 
  Character, 
  Hero, 
  Formation, 
  TrainingResult,
  Match,
  ServiceResponse
} from '@/types'

export class GameService {
  /**
   * 获取角色信息
   * WebSocket: character.getInfo
   */
  async getCharacterInfo(): Promise<ServiceResponse<Character>> {
    return wsService.sendMessage('character.getInfo')
  }

  /**
   * 更新角色信息
   * WebSocket: character.updateInfo
   */
  async updateCharacterInfo(data: Partial<Character>): Promise<ServiceResponse<Character>> {
    return wsService.sendMessage('character.updateInfo', data)
  }

  /**
   * 获取英雄列表
   * WebSocket: hero.getList
   */
  async getHeroList(): Promise<ServiceResponse<Hero[]>> {
    return wsService.sendMessage('hero.getList')
  }

  /**
   * 获取英雄详情
   * WebSocket: hero.getDetail
   */
  async getHeroDetail(heroId: string): Promise<ServiceResponse<Hero>> {
    return wsService.sendMessage('hero.getDetail', { heroId })
  }

  /**
   * 训练英雄
   * WebSocket: hero.train
   */
  async trainHero(data: {
    heroId: string
    trainType: number
    trainCount: number
    useItems?: string[]
  }): Promise<ServiceResponse<TrainingResult>> {
    return wsService.sendMessage('hero.train', data)
  }

  /**
   * 获取阵型配置
   * WebSocket: formation.getFormations
   */
  async getFormations(): Promise<ServiceResponse<Formation[]>> {
    return wsService.sendMessage('formation.getFormations')
  }

  /**
   * 保存阵型配置
   * WebSocket: formation.saveFormation
   */
  async saveFormation(formation: Formation): Promise<ServiceResponse<Formation>> {
    return wsService.sendMessage('formation.saveFormation', formation)
  }

  /**
   * 设置当前阵型
   * WebSocket: formation.setActive
   */
  async setActiveFormation(formationId: string): Promise<ServiceResponse<void>> {
    return wsService.sendMessage('formation.setActive', { formationId })
  }

  /**
   * 获取比赛列表
   * WebSocket: match.getList
   */
  async getMatchList(params?: {
    type?: string
    status?: string
    page?: number
    limit?: number
  }): Promise<ServiceResponse<{
    matches: Match[]
    total: number
    page: number
    limit: number
  }>> {
    return wsService.sendMessage('match.getList', params || {})
  }

  /**
   * 获取比赛详情
   * WebSocket: match.getDetail
   */
  async getMatchDetail(matchId: string): Promise<ServiceResponse<Match>> {
    return wsService.sendMessage('match.getDetail', { matchId })
  }

  /**
   * 创建友谊赛
   * WebSocket: match.createFriendly
   */
  async createFriendlyMatch(data: {
    opponentId: string
    formationId: string
    scheduledTime?: string
  }): Promise<ServiceResponse<Match>> {
    return wsService.sendMessage('match.createFriendly', data)
  }

  /**
   * 参加比赛
   * WebSocket: match.join
   */
  async joinMatch(matchId: string, formationId: string): Promise<ServiceResponse<void>> {
    return wsService.sendMessage('match.join', {
      matchId,
      formationId
    })
  }

  /**
   * 开始比赛
   * WebSocket: match.start
   * 参考 apps/match/scripts/test-battle-engine.js
   */
  async startMatch(matchId: string): Promise<ServiceResponse<{
    matchId: string
    status: string
    message: string
  }>> {
    return wsService.sendMessage('match.start', { matchId })
  }

  /**
   * 获取比赛实时状态
   * WebSocket: match.getStatus
   */
  async getMatchStatus(matchId: string): Promise<ServiceResponse<{
    matchId: string
    status: string
    currentTime: number
    homeScore: number
    awayScore: number
    events: any[]
  }>> {
    return wsService.sendMessage('match.getStatus', { matchId })
  }

  /**
   * 获取库存物品
   * WebSocket: inventory.getItems
   */
  async getInventoryItems(): Promise<ServiceResponse<any[]>> {
    return wsService.sendMessage('inventory.getItems')
  }

  /**
   * 使用物品
   * WebSocket: inventory.useItem
   */
  async useItem(itemId: string, targetId?: string): Promise<ServiceResponse<any>> {
    return wsService.sendMessage('inventory.useItem', {
      itemId,
      targetId
    })
  }

  /**
   * 获取转会市场列表
   * WebSocket: market.getList
   */
  async getMarketList(params?: {
    position?: string
    minPrice?: number
    maxPrice?: number
    page?: number
    limit?: number
  }): Promise<ServiceResponse<{
    listings: any[]
    total: number
    page: number
    limit: number
  }>> {
    return wsService.sendMessage('market.getList', params || {})
  }

  /**
   * 购买球员
   * WebSocket: market.buyPlayer
   */
  async buyPlayer(listingId: string): Promise<ServiceResponse<any>> {
    return wsService.sendMessage('market.buyPlayer', { listingId })
  }

  /**
   * 出售球员
   * WebSocket: market.sellPlayer
   */
  async sellPlayer(data: {
    heroId: string
    price: number
    duration: number
  }): Promise<ServiceResponse<any>> {
    return wsService.sendMessage('market.sellPlayer', data)
  }

  /**
   * 获取公会信息
   * WebSocket: guild.getInfo
   */
  async getGuildInfo(): Promise<ServiceResponse<any>> {
    return wsService.sendMessage('guild.getInfo')
  }

  /**
   * 加入公会
   * WebSocket: guild.join
   */
  async joinGuild(guildId: string): Promise<ServiceResponse<any>> {
    return wsService.sendMessage('guild.join', { guildId })
  }

  /**
   * 创建公会
   * WebSocket: guild.create
   */
  async createGuild(data: {
    name: string
    description?: string
  }): Promise<ServiceResponse<any>> {
    return wsService.sendMessage('guild.create', data)
  }

  /**
   * 获取每日任务
   * WebSocket: task.getDailyTasks
   */
  async getDailyTasks(): Promise<ServiceResponse<any[]>> {
    return wsService.sendMessage('task.getDailyTasks')
  }

  /**
   * 完成任务
   * WebSocket: task.complete
   */
  async completeTask(taskId: string): Promise<ServiceResponse<any>> {
    return wsService.sendMessage('task.complete', { taskId })
  }

  /**
   * 获取排行榜
   * WebSocket: ranking.getList
   */
  async getRankingList(type: string): Promise<ServiceResponse<any[]>> {
    return wsService.sendMessage('ranking.getList', { type })
  }
}

// 导出单例
export const gameService = new GameService()
