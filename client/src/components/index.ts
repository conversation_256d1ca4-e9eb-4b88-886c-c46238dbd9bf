/**
 * 组件库统一导出
 * 
 * 提供完整的游戏UI组件库，包括：
 * - 基础UI组件
 * - 游戏专用组件
 * - 业务组件
 */

// 基础UI组件
export { default as GameButton } from './common/GameButton.vue'
export { default as GameCard } from './common/GameCard.vue'
export { default as GameModal } from './common/GameModal.vue'
export { default as GameTooltip } from './common/GameTooltip.vue'
export { default as GameProgress } from './common/GameProgress.vue'

// 游戏专用组件
export { default as HeroCard } from './game/HeroCard.vue'
export { default as FormationField } from './game/FormationField.vue'
export { default as TrainingCenter } from './game/TrainingCenter.vue'
export { default as MatchSimulator } from './game/MatchSimulator.vue'
export { default as MarketPlace } from './game/MarketPlace.vue'
export { default as LiveMatchModal } from './game/LiveMatchModal.vue'

// 组件类型定义
export interface ComponentLibrary {
  // 基础组件
  GameButton: typeof import('./common/GameButton.vue').default
  GameCard: typeof import('./common/GameCard.vue').default
  GameModal: typeof import('./common/GameModal.vue').default
  GameTooltip: typeof import('./common/GameTooltip.vue').default
  GameProgress: typeof import('./common/GameProgress.vue').default
  
  // 游戏组件
  HeroCard: typeof import('./game/HeroCard.vue').default
  FormationField: typeof import('./game/FormationField.vue').default
  TrainingCenter: typeof import('./game/TrainingCenter.vue').default
  MatchSimulator: typeof import('./game/MatchSimulator.vue').default
  MarketPlace: typeof import('./game/MarketPlace.vue').default
  LiveMatchModal: typeof import('./game/LiveMatchModal.vue').default
}

// 组件注册函数
export function registerGameComponents(app: any) {
  // 基础组件
  app.component('GameButton', () => import('./common/GameButton.vue'))
  app.component('GameCard', () => import('./common/GameCard.vue'))
  app.component('GameModal', () => import('./common/GameModal.vue'))
  app.component('GameTooltip', () => import('./common/GameTooltip.vue'))
  app.component('GameProgress', () => import('./common/GameProgress.vue'))
  
  // 游戏组件
  app.component('HeroCard', () => import('./game/HeroCard.vue'))
  app.component('FormationField', () => import('./game/FormationField.vue'))
  app.component('TrainingCenter', () => import('./game/TrainingCenter.vue'))
  app.component('MatchSimulator', () => import('./game/MatchSimulator.vue'))
  app.component('MarketPlace', () => import('./game/MarketPlace.vue'))
  app.component('LiveMatchModal', () => import('./game/LiveMatchModal.vue'))
}

// 组件配置
export const componentConfig = {
  // 基础组件默认配置
  gameCard: {
    defaultSize: 'default',
    defaultType: 'default',
    defaultHoverable: true
  },
  
  gameModal: {
    defaultWidth: 520,
    defaultCentered: true,
    defaultMaskClosable: true
  },
  
  gameProgress: {
    defaultSize: 'default',
    defaultType: 'primary',
    defaultAnimated: true
  },
  
  // 游戏组件默认配置
  heroCard: {
    defaultShowActions: true,
    defaultShowMoreActions: true,
    defaultCompact: false
  },
  
  formationField: {
    defaultInteractive: true,
    fieldWidth: 800,
    fieldHeight: 500
  },
  
  trainingCenter: {
    maxTrainingSlots: 10,
    maxTrainingCount: 10,
    defaultAutoRenew: false
  }
}

// 组件工具函数
export const componentUtils = {
  /**
   * 格式化货币显示
   */
  formatCurrency: (value: number): string => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toString()
  },

  /**
   * 格式化时间显示
   */
  formatDuration: (ms: number): string => {
    const hours = Math.floor(ms / 3600000)
    const minutes = Math.floor((ms % 3600000) / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    } else if (minutes > 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }
    return `${seconds}s`
  },

  /**
   * 获取稀有度颜色
   */
  getRarityColor: (rarity: string): string => {
    const colors: Record<string, string> = {
      common: '#8c8c8c',
      uncommon: '#52c41a',
      rare: '#1890ff',
      epic: '#722ed1',
      legendary: '#fa8c16',
      mythic: '#f5222d'
    }
    return colors[rarity] || colors.common
  },

  /**
   * 获取位置颜色
   */
  getPositionColor: (position: string): string => {
    const colors: Record<string, string> = {
      GK: '#f5222d',
      DF: '#52c41a',
      MF: '#1890ff',
      FW: '#fa8c16'
    }
    return colors[position] || '#8c8c8c'
  },

  /**
   * 获取属性评级
   */
  getAttributeGrade: (value: number): { grade: string; color: string } => {
    if (value >= 90) return { grade: 'S', color: '#f5222d' }
    if (value >= 80) return { grade: 'A', color: '#fa8c16' }
    if (value >= 70) return { grade: 'B', color: '#52c41a' }
    if (value >= 60) return { grade: 'C', color: '#1890ff' }
    if (value >= 50) return { grade: 'D', color: '#722ed1' }
    return { grade: 'E', color: '#8c8c8c' }
  },

  /**
   * 计算球员总评
   */
  calculateOverallRating: (attributes: Record<string, number>): number => {
    const values = Object.values(attributes)
    if (values.length === 0) return 0
    
    const sum = values.reduce((total, value) => total + value, 0)
    return Math.round(sum / values.length)
  },

  /**
   * 生成随机ID
   */
  generateId: (): string => {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },

  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(null, args), wait)
    }
  },

  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(null, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }
}

// 组件主题配置
export const componentTheme = {
  colors: {
    primary: '#52c41a',
    success: '#52c41a',
    warning: '#fa8c16',
    error: '#f5222d',
    info: '#1890ff',
    
    // 稀有度颜色
    rarity: {
      common: '#8c8c8c',
      uncommon: '#52c41a',
      rare: '#1890ff',
      epic: '#722ed1',
      legendary: '#fa8c16',
      mythic: '#f5222d'
    },
    
    // 位置颜色
    position: {
      GK: '#f5222d',
      DF: '#52c41a',
      MF: '#1890ff',
      FW: '#fa8c16'
    }
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    base: '16px',
    lg: '24px',
    xl: '32px'
  },
  
  borderRadius: {
    sm: '4px',
    base: '6px',
    lg: '8px',
    xl: '12px'
  },
  
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    xxl: '24px'
  },
  
  transition: {
    duration: '0.3s',
    easing: 'ease-in-out'
  }
}

// 导出所有内容
export default {
  registerGameComponents,
  componentConfig,
  componentUtils,
  componentTheme
}
