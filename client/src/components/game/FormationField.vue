<template>
  <div class="formation-field" :class="{ 'field-interactive': interactive }">
    <!-- 足球场背景 -->
    <div class="field-background">
      <!-- 场地标线 -->
      <div class="field-lines">
        <!-- 中线 -->
        <div class="center-line"></div>
        <!-- 中圈 -->
        <div class="center-circle"></div>
        <!-- 禁区 -->
        <div class="penalty-area penalty-area-left"></div>
        <div class="penalty-area penalty-area-right"></div>
        <!-- 小禁区 -->
        <div class="goal-area goal-area-left"></div>
        <div class="goal-area goal-area-right"></div>
        <!-- 球门 -->
        <div class="goal goal-left"></div>
        <div class="goal goal-right"></div>
      </div>

      <!-- 阵型位置点 -->
      <div class="formation-positions">
        <div
          v-for="position in formationPositions"
          :key="position.id"
          class="position-slot"
          :class="{
            'slot-occupied': position.heroId,
            'slot-highlighted': highlightedPosition === position.id,
            'slot-droppable': isDragOver && canDropHero(position)
          }"
          :style="{
            left: `${position.x}%`,
            top: `${position.y}%`
          }"
          @click="handlePositionClick(position)"
          @dragover.prevent="handleDragOver(position)"
          @dragleave="handleDragLeave"
          @drop="handleDrop(position, $event)"
        >
          <!-- 位置标识 -->
          <div class="position-marker" :class="`marker-${position.role}`">
            {{ getPositionAbbr(position.role) }}
          </div>

          <!-- 球员卡片 -->
          <div v-if="position.heroId" class="position-hero">
            <GameTooltip
              :title="getHeroName(position.heroId)"
              :description="getHeroDescription(position.heroId)"
              :attributes="getHeroAttributes(position.heroId)"
            >
              <div
                class="hero-avatar"
                :draggable="interactive"
                @dragstart="handleDragStart(position, $event)"
                @dragend="handleDragEnd"
              >
                <!-- MUD风格：使用文字和符号代替图片 -->
                <div class="hero-display">
                  <div class="hero-initial">{{ getHeroInitial(position.heroId) }}</div>
                  <div class="hero-position-mark">{{ getHeroPositionMark(position.heroId) }}</div>
                </div>

                <!-- 球员评分 -->
                <div class="hero-rating">
                  {{ getHeroRating(position.heroId) }}
                </div>

                <!-- 移除按钮 -->
                <div
                  v-if="interactive"
                  class="remove-hero"
                  @click.stop="removeHero(position)"
                >
                  <CloseOutlined />
                </div>
              </div>
            </GameTooltip>
          </div>

          <!-- 空位置提示 -->
          <div v-else-if="interactive" class="empty-position">
            <PlusOutlined />
          </div>
        </div>
      </div>

      <!-- 拖拽指示器 -->
      <div v-if="isDragging" class="drag-indicator">
        <div class="drag-preview">
          <div class="drag-hero-initial">{{ getDragHeroInitial() }}</div>
          <span>{{ dragPreviewName }}</span>
        </div>
      </div>
    </div>

    <!-- 阵型信息 -->
    <div class="formation-info">
      <div class="formation-header">
        <h3 class="formation-name">{{ formation.name || '自定义阵型' }}</h3>
        <div class="formation-rating">
          <span class="rating-label">整体评分:</span>
          <span class="rating-value">{{ calculateFormationRating() }}</span>
        </div>
      </div>

      <div class="formation-stats">
        <div class="stat-item">
          <span class="stat-label">攻击力:</span>
          <span class="stat-value">{{ calculateAttackPower() }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">防守力:</span>
          <span class="stat-value">{{ calculateDefensePower() }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">中场控制:</span>
          <span class="stat-value">{{ calculateMidfieldControl() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { CloseOutlined, PlusOutlined } from '@ant-design/icons-vue'
import GameTooltip from '@/components/common/GameTooltip.vue'
import type { Formation, FormationPosition, Hero } from '@/types'

interface Props {
  formation: Formation
  heroes: Hero[]
  interactive?: boolean
  highlightedPosition?: string | null
}

interface Emits {
  (e: 'position-click', position: FormationPosition): void
  (e: 'hero-assigned', position: FormationPosition, hero: Hero): void
  (e: 'hero-removed', position: FormationPosition): void
  (e: 'formation-updated', formation: Formation): void
}

const props = withDefaults(defineProps<Props>(), {
  interactive: true,
  highlightedPosition: null
})

const emit = defineEmits<Emits>()

// 响应式数据
const isDragging = ref(false)
const isDragOver = ref(false)
const dragPreviewAvatar = ref('')
const dragPreviewName = ref('')
const draggedHero = ref<Hero | null>(null)

// 计算属性
const formationPositions = computed(() => {
  return props.formation.positions || []
})

const heroMap = computed(() => {
  const map = new Map<string, Hero>()
  props.heroes.forEach(hero => {
    map.set(hero.heroId, hero)
  })
  return map
})

// 工具函数
const getPositionAbbr = (role: string): string => {
  const abbrs: Record<string, string> = {
    'goalkeeper': 'GK',
    'defender': 'DF',
    'midfielder': 'MF',
    'forward': 'FW'
  }
  return abbrs[role] || role.substring(0, 2).toUpperCase()
}

const getHeroName = (heroId: string): string => {
  return heroMap.value.get(heroId)?.name || '未知球员'
}

const getHeroDescription = (heroId: string): string => {
  const hero = heroMap.value.get(heroId)
  if (!hero) return ''
  return `${hero.position} | 年龄: ${hero.age} | 国籍: ${hero.nationality}`
}

const getHeroAttributes = (heroId: string): AttributeItem[] => {
  const hero = heroMap.value.get(heroId)
  if (!hero?.attributes) return []

  return [
    { label: '速度', value: hero.attributes.pace || 0, type: 'normal' as const },
    { label: '射门', value: hero.attributes.shooting || 0, type: 'normal' as const },
    { label: '传球', value: hero.attributes.passing || 0, type: 'normal' as const },
    { label: '防守', value: hero.attributes.defending || 0, type: 'normal' as const }
  ]
}

const getHeroInitial = (heroId: string): string => {
  const hero = heroMap.value.get(heroId)
  if (!hero) return '?'

  // 取球员姓名的首字母
  const name = hero.name || 'Unknown'
  return name.charAt(0).toUpperCase()
}

const getHeroPositionMark = (heroId: string): string => {
  const hero = heroMap.value.get(heroId)
  if (!hero) return ''

  // 根据位置返回符号
  const position = hero.position?.toLowerCase() || ''
  if (position.includes('gk')) return '🥅'
  if (position.includes('df')) return '🛡️'
  if (position.includes('mf')) return '⚽'
  if (position.includes('fw')) return '⚡'
  return '👤'
}

const getDragHeroInitial = (): string => {
  if (!draggedHero.value) return '?'
  return draggedHero.value.name.charAt(0).toUpperCase()
}

const getHeroRating = (heroId: string): number => {
  return heroMap.value.get(heroId)?.overallRating || 0
}

// 拖拽处理
const handleDragStart = (position: FormationPosition, event: DragEvent) => {
  if (!props.interactive) return
  
  const hero = heroMap.value.get(position.heroId!)
  if (!hero) return

  isDragging.value = true
  draggedHero.value = hero
  dragPreviewAvatar.value = hero.avatar || '/default-hero.png'
  dragPreviewName.value = hero.name

  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', hero.heroId)
  }
}

const handleDragEnd = () => {
  isDragging.value = false
  isDragOver.value = false
  draggedHero.value = null
  dragPreviewAvatar.value = ''
  dragPreviewName.value = ''
}

const handleDragOver = (position: FormationPosition) => {
  if (!props.interactive) return
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (position: FormationPosition, event: DragEvent) => {
  if (!props.interactive) return
  
  event.preventDefault()
  isDragOver.value = false

  const heroId = event.dataTransfer?.getData('text/plain')
  if (!heroId) return

  const hero = heroMap.value.get(heroId) || draggedHero.value
  if (!hero) return

  // 检查是否可以放置
  if (!canDropHero(position)) return

  // 如果位置已被占用，需要交换
  if (position.heroId) {
    // 实现球员交换逻辑
    swapHeroes(position, hero)
  } else {
    // 直接分配球员到位置
    assignHeroToPosition(position, hero)
  }
}

const canDropHero = (position: FormationPosition): boolean => {
  if (!draggedHero.value) return false
  
  // 检查位置兼容性
  const hero = draggedHero.value
  const heroPosition = hero.position?.toLowerCase()
  const slotRole = position.role?.toLowerCase()

  // 简单的位置兼容性检查
  const compatibility: Record<string, string[]> = {
    'goalkeeper': ['gk'],
    'defender': ['df', 'cb', 'lb', 'rb'],
    'midfielder': ['mf', 'cm', 'dm', 'am'],
    'forward': ['fw', 'st', 'lw', 'rw']
  }

  return compatibility[slotRole]?.some(pos => heroPosition?.includes(pos)) || false
}

// 业务逻辑
const handlePositionClick = (position: FormationPosition) => {
  if (!props.interactive) return
  emit('position-click', position)
}

const removeHero = (position: FormationPosition) => {
  if (!props.interactive) return
  
  const updatedPosition = { ...position, heroId: null }
  emit('hero-removed', updatedPosition)
  updateFormation()
}

const assignHeroToPosition = (position: FormationPosition, hero: Hero) => {
  const updatedPosition = { ...position, heroId: hero.heroId }
  emit('hero-assigned', updatedPosition, hero)
  updateFormation()
}

const swapHeroes = (position: FormationPosition, newHero: Hero) => {
  // 实现球员交换逻辑
  const currentHeroId = position.heroId
  if (!currentHeroId) return

  // 找到新球员当前的位置
  const newHeroPosition = formationPositions.value.find(p => p.heroId === newHero.heroId)
  
  if (newHeroPosition) {
    // 交换两个球员的位置
    newHeroPosition.heroId = currentHeroId
    position.heroId = newHero.heroId
  } else {
    // 新球员没有位置，直接替换
    position.heroId = newHero.heroId
  }

  updateFormation()
}

const updateFormation = () => {
  const updatedFormation = {
    ...props.formation,
    positions: formationPositions.value
  }
  emit('formation-updated', updatedFormation)
}

// 计算阵型数据
const calculateFormationRating = (): number => {
  const occupiedPositions = formationPositions.value.filter(p => p.heroId)
  if (occupiedPositions.length === 0) return 0

  const totalRating = occupiedPositions.reduce((sum, position) => {
    const hero = heroMap.value.get(position.heroId!)
    return sum + (hero?.overallRating || 0)
  }, 0)

  return Math.round(totalRating / occupiedPositions.length)
}

const calculateAttackPower = (): number => {
  const attackPositions = formationPositions.value.filter(p => 
    p.heroId && (p.role === 'forward' || p.role === 'midfielder')
  )
  
  if (attackPositions.length === 0) return 0

  const totalAttack = attackPositions.reduce((sum, position) => {
    const hero = heroMap.value.get(position.heroId!)
    const shooting = hero?.attributes?.shooting || 0
    const passing = hero?.attributes?.passing || 0
    return sum + (shooting + passing) / 2
  }, 0)

  return Math.round(totalAttack / attackPositions.length)
}

const calculateDefensePower = (): number => {
  const defensePositions = formationPositions.value.filter(p => 
    p.heroId && (p.role === 'defender' || p.role === 'goalkeeper')
  )
  
  if (defensePositions.length === 0) return 0

  const totalDefense = defensePositions.reduce((sum, position) => {
    const hero = heroMap.value.get(position.heroId!)
    const defending = hero?.attributes?.defending || 0
    return sum + defending
  }, 0)

  return Math.round(totalDefense / defensePositions.length)
}

const calculateMidfieldControl = (): number => {
  const midfieldPositions = formationPositions.value.filter(p => 
    p.heroId && p.role === 'midfielder'
  )
  
  if (midfieldPositions.length === 0) return 0

  const totalControl = midfieldPositions.reduce((sum, position) => {
    const hero = heroMap.value.get(position.heroId!)
    const passing = hero?.attributes?.passing || 0
    const dribbling = hero?.attributes?.dribbling || 0
    return sum + (passing + dribbling) / 2
  }, 0)

  return Math.round(totalControl / midfieldPositions.length)
}

// 移除图片错误处理函数，改为MUD风格显示
</script>

<style lang="less" scoped>
.formation-field {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  
  &.field-interactive {
    .position-slot {
      cursor: pointer;
      
      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

.field-background {
  position: relative;
  width: 100%;
  height: 500px;
  background: linear-gradient(180deg, #2d5a27 0%, #4a7c59 100%);
  border: 2px solid #ffffff;
  border-radius: @border-radius-base;
  overflow: hidden;
}

.field-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  
  .center-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #ffffff;
    transform: translateX(-50%);
  }
  
  .center-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 120px;
    height: 120px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
  }
  
  .penalty-area {
    position: absolute;
    width: 120px;
    height: 200px;
    border: 2px solid #ffffff;
    top: 50%;
    transform: translateY(-50%);
    
    &.penalty-area-left {
      left: 0;
      border-right: none;
    }
    
    &.penalty-area-right {
      right: 0;
      border-left: none;
    }
  }
  
  .goal-area {
    position: absolute;
    width: 60px;
    height: 100px;
    border: 2px solid #ffffff;
    top: 50%;
    transform: translateY(-50%);
    
    &.goal-area-left {
      left: 0;
      border-right: none;
    }
    
    &.goal-area-right {
      right: 0;
      border-left: none;
    }
  }
  
  .goal {
    position: absolute;
    width: 8px;
    height: 60px;
    background-color: #ffffff;
    top: 50%;
    transform: translateY(-50%);
    
    &.goal-left {
      left: -4px;
    }
    
    &.goal-right {
      right: -4px;
    }
  }
}

.formation-positions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.position-slot {
  position: absolute;
  width: 60px;
  height: 60px;
  transform: translate(-50%, -50%);
  transition: all @transition-duration;
  
  &.slot-highlighted {
    transform: translate(-50%, -50%) scale(1.2);
    z-index: 10;
  }
  
  &.slot-droppable {
    .position-marker {
      border-color: @success-color;
      background-color: fade(@success-color, 20%);
    }
  }
}

.position-marker {
  width: 30px;
  height: 30px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: @font-size-xs;
  font-weight: bold;
  color: #ffffff;
  margin: 0 auto;
  
  &.marker-goalkeeper {
    background-color: @position-gk;
  }
  
  &.marker-defender {
    background-color: @position-def;
  }
  
  &.marker-midfielder {
    background-color: @position-mid;
  }
  
  &.marker-forward {
    background-color: @position-att;
  }
}

.position-hero {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  
  .hero-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    cursor: grab;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      cursor: grabbing;
    }

    .hero-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #ffffff;

      .hero-initial {
        font-size: @font-size-lg;
        font-weight: bold;
        line-height: 1;
      }

      .hero-position-mark {
        font-size: @font-size-xs;
        line-height: 1;
        margin-top: 2px;
      }
    }
    
    .hero-rating {
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      background-color: @primary-color;
      color: #ffffff;
      font-size: @font-size-xs;
      font-weight: bold;
      padding: 2px 6px;
      border-radius: 10px;
      min-width: 24px;
      text-align: center;
    }
    
    .remove-hero {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 20px;
      height: 20px;
      background-color: @error-color;
      color: #ffffff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: @font-size-xs;
      cursor: pointer;
      opacity: 0;
      transition: opacity @transition-duration;
    }
    
    &:hover .remove-hero {
      opacity: 1;
    }
  }
}

.empty-position {
  width: 30px;
  height: 30px;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  margin: 15px auto 0;
  transition: all @transition-duration;
  
  &:hover {
    border-color: #ffffff;
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.drag-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
  
  .drag-preview {
    position: absolute;
    display: flex;
    align-items: center;
    gap: @margin-xs;
    background-color: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: @padding-xs @padding-sm;
    border-radius: @border-radius-base;
    font-size: @font-size-sm;

    .drag-hero-initial {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: @primary-color;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: #ffffff;
    }
  }
}

.formation-info {
  margin-top: @margin-lg;
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base;
  
  .formation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-base;
    
    .formation-name {
      color: @text-color;
      margin: 0;
    }
    
    .formation-rating {
      .rating-label {
        color: @text-color-secondary;
        margin-right: @margin-xs;
      }
      
      .rating-value {
        color: @primary-color;
        font-weight: bold;
        font-size: @font-size-lg;
      }
    }
  }
  
  .formation-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: @margin-base;
    
    .stat-item {
      text-align: center;
      
      .stat-label {
        display: block;
        color: @text-color-secondary;
        font-size: @font-size-sm;
        margin-bottom: @margin-xs;
      }
      
      .stat-value {
        color: @text-color;
        font-weight: bold;
        font-size: @font-size-lg;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .field-background {
    height: 400px;
  }
  
  .position-slot {
    width: 50px;
    height: 50px;
  }
  
  .position-hero .hero-avatar {
    width: 40px;
    height: 40px;
  }
  
  .formation-stats {
    grid-template-columns: 1fr;
    gap: @margin-sm;
  }
}
</style>
