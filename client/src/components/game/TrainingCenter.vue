<template>
  <div class="training-center">
    <!-- 训练中心头部 -->
    <div class="training-header">
      <div class="header-info">
        <h2 class="center-title">
          <ThunderboltOutlined />
          训练中心
        </h2>
        <p class="center-description">提升球员属性，增强球队实力</p>
      </div>
      
      <div class="training-stats">
        <div class="stat-card">
          <div class="stat-value">{{ availableSlots }}</div>
          <div class="stat-label">可用训练位</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ trainingInProgress }}</div>
          <div class="stat-label">训练中</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ totalTrainingToday }}</div>
          <div class="stat-label">今日训练</div>
        </div>
      </div>
    </div>

    <!-- 训练类型选择 -->
    <div class="training-types">
      <h3 class="section-title">选择训练类型</h3>
      <div class="types-grid">
        <GameCard
          v-for="trainingType in trainingTypes"
          :key="trainingType.id"
          :class="['training-type-card', { 'type-selected': selectedTrainingType?.id === trainingType.id }]"
          :selected="selectedTrainingType?.id === trainingType.id"
          @click="selectTrainingType(trainingType)"
        >
          <template #header>
            <div class="type-header">
              <component :is="trainingType.icon" class="type-icon" />
              <span class="type-name">{{ trainingType.name }}</span>
            </div>
          </template>

          <div class="type-content">
            <p class="type-description">{{ trainingType.description }}</p>
            
            <div class="type-effects">
              <div class="effect-title">训练效果:</div>
              <div class="effects-list">
                <div
                  v-for="effect in trainingType.effects"
                  :key="effect.attribute"
                  class="effect-item"
                >
                  <span class="effect-attr">{{ getAttributeName(effect.attribute) }}</span>
                  <span class="effect-value">+{{ effect.value }}</span>
                </div>
              </div>
            </div>

            <div class="type-cost">
              <div class="cost-item">
                <CoinIcon />
                <span>{{ trainingType.coinCost }}</span>
              </div>
              <div class="cost-item">
                <EnergyIcon />
                <span>{{ trainingType.energyCost }}</span>
              </div>
              <div class="cost-item">
                <ClockIcon />
                <span>{{ formatDuration(trainingType.duration) }}</span>
              </div>
            </div>
          </div>
        </GameCard>
      </div>
    </div>

    <!-- 球员选择 -->
    <div v-if="selectedTrainingType" class="hero-selection">
      <h3 class="section-title">选择训练球员</h3>
      
      <!-- 筛选器 -->
      <div class="selection-filters">
        <a-select
          v-model:value="positionFilter"
          placeholder="位置筛选"
          style="width: 120px"
          @change="filterHeroes"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="GK">门将</a-select-option>
          <a-select-option value="DF">后卫</a-select-option>
          <a-select-option value="MF">中场</a-select-option>
          <a-select-option value="FW">前锋</a-select-option>
        </a-select>

        <a-select
          v-model:value="statusFilter"
          placeholder="状态筛选"
          style="width: 120px"
          @change="filterHeroes"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="available">可训练</a-select-option>
          <a-select-option value="training">训练中</a-select-option>
          <a-select-option value="injured">受伤</a-select-option>
        </a-select>

        <a-input
          v-model:value="nameFilter"
          placeholder="搜索球员姓名"
          style="width: 200px"
          @input="filterHeroes"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>
      </div>

      <!-- 球员列表 -->
      <div class="heroes-grid">
        <HeroCard
          v-for="hero in filteredHeroes"
          :key="hero.heroId"
          :hero="hero"
          :selectable="true"
          :selected="selectedHeroes.includes(hero.heroId)"
          :compact="true"
          @click="toggleHeroSelection(hero)"
        />
      </div>

      <!-- 批量选择 -->
      <div class="batch-selection">
        <a-space>
          <a-button @click="selectAllAvailable">选择所有可训练</a-button>
          <a-button @click="selectByPosition">按位置选择</a-button>
          <a-button @click="clearSelection">清空选择</a-button>
        </a-space>
        
        <div class="selection-info">
          已选择 {{ selectedHeroes.length }} 名球员
        </div>
      </div>
    </div>

    <!-- 训练配置 -->
    <div v-if="selectedTrainingType && selectedHeroes.length > 0" class="training-config">
      <h3 class="section-title">训练配置</h3>
      
      <div class="config-panel">
        <div class="config-item">
          <label class="config-label">训练次数:</label>
          <a-input-number
            v-model:value="trainingCount"
            :min="1"
            :max="10"
            style="width: 120px"
          />
        </div>

        <div class="config-item">
          <label class="config-label">使用道具:</label>
          <a-select
            v-model:value="selectedItems"
            mode="multiple"
            placeholder="选择训练道具"
            style="width: 300px"
          >
            <a-select-option
              v-for="item in availableTrainingItems"
              :key="item.id"
              :value="item.id"
            >
              {{ item.name }} ({{ item.effect }})
            </a-select-option>
          </a-select>
        </div>

        <div class="config-item">
          <label class="config-label">自动续训:</label>
          <a-switch v-model:checked="autoRenew" />
        </div>
      </div>

      <!-- 成本计算 -->
      <div class="cost-summary">
        <div class="summary-title">训练成本预览</div>
        <div class="cost-breakdown">
          <div class="cost-row">
            <span>金币消耗:</span>
            <span class="cost-value">{{ calculateTotalCost().coins }}</span>
          </div>
          <div class="cost-row">
            <span>体力消耗:</span>
            <span class="cost-value">{{ calculateTotalCost().energy }}</span>
          </div>
          <div class="cost-row">
            <span>预计时间:</span>
            <span class="cost-value">{{ formatDuration(calculateTotalCost().time) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 开始训练按钮 -->
    <div v-if="canStartTraining" class="training-actions">
      <a-button
        type="primary"
        size="large"
        :loading="isStartingTraining"
        @click="startTraining"
      >
        <ThunderboltOutlined />
        开始训练 ({{ selectedHeroes.length }}名球员)
      </a-button>
    </div>

    <!-- 训练队列 -->
    <div v-if="trainingQueue.length > 0" class="training-queue">
      <h3 class="section-title">训练队列</h3>
      
      <div class="queue-list">
        <div
          v-for="training in trainingQueue"
          :key="training.id"
          class="queue-item"
        >
          <div class="queue-hero">
            <div class="hero-avatar-text">
              {{ getHeroInitial(training.heroName) }}
            </div>
            <div class="hero-info">
              <div class="hero-name">{{ training.heroName }}</div>
              <div class="training-type">{{ training.trainingTypeName }}</div>
            </div>
          </div>

          <div class="queue-progress">
            <GameProgress
              :current="training.progress"
              :total="100"
              :show-label="false"
              size="small"
              type="primary"
            />
            <div class="progress-text">
              {{ training.remainingTime }} 剩余
            </div>
          </div>

          <div class="queue-actions">
            <GameTooltip title="加速训练">
              <a-button size="small" @click="speedUpTraining(training)">
                <FastForwardOutlined />
              </a-button>
            </GameTooltip>
            
            <GameTooltip title="取消训练">
              <a-button size="small" danger @click="cancelTraining(training)">
                <StopOutlined />
              </a-button>
            </GameTooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ThunderboltOutlined,
  SearchOutlined,
  FastForwardOutlined,
  StopOutlined
} from '@ant-design/icons-vue'
import GameCard from '@/components/common/GameCard.vue'
import GameProgress from '@/components/common/GameProgress.vue'
import GameTooltip from '@/components/common/GameTooltip.vue'
import HeroCard from './HeroCard.vue'
import type { Hero, TrainingType, TrainingQueue } from '@/types'

// 响应式数据
const selectedTrainingType = ref<TrainingType | null>(null)
const selectedHeroes = ref<string[]>([])
const trainingCount = ref(1)
const selectedItems = ref<string[]>([])
const autoRenew = ref(false)
const isStartingTraining = ref(false)

// 筛选器
const positionFilter = ref('')
const statusFilter = ref('')
const nameFilter = ref('')

// 模拟数据
const trainingTypes = ref<TrainingType[]>([
  {
    id: '1',
    name: '体能训练',
    description: '提升球员的速度和体力属性',
    icon: 'ThunderboltOutlined',
    effects: [
      { attribute: 'pace', value: 2 },
      { attribute: 'physical', value: 3 }
    ],
    coinCost: 1000,
    energyCost: 10,
    duration: 3600000 // 1小时
  },
  {
    id: '2',
    name: '技术训练',
    description: '提升球员的技术和盘带能力',
    icon: 'StarOutlined',
    effects: [
      { attribute: 'dribbling', value: 3 },
      { attribute: 'passing', value: 2 }
    ],
    coinCost: 1500,
    energyCost: 15,
    duration: 7200000 // 2小时
  }
])

const heroes = ref<Hero[]>([])
const trainingQueue = ref<TrainingQueue[]>([])
const availableTrainingItems = ref([])

// 计算属性
const availableSlots = computed(() => 10 - trainingQueue.value.length)
const trainingInProgress = computed(() => trainingQueue.value.length)
const totalTrainingToday = computed(() => 5) // 模拟数据

const filteredHeroes = computed(() => {
  return heroes.value.filter(hero => {
    if (positionFilter.value && !hero.position?.includes(positionFilter.value)) {
      return false
    }
    if (statusFilter.value) {
      if (statusFilter.value === 'available' && hero.status !== 'normal') {
        return false
      }
      if (statusFilter.value === 'training' && hero.status !== 'training') {
        return false
      }
      if (statusFilter.value === 'injured' && hero.status !== 'injured') {
        return false
      }
    }
    if (nameFilter.value && !hero.name.toLowerCase().includes(nameFilter.value.toLowerCase())) {
      return false
    }
    return true
  })
})

const canStartTraining = computed(() => {
  return selectedTrainingType.value && 
         selectedHeroes.value.length > 0 && 
         availableSlots.value >= selectedHeroes.value.length
})

// 方法
const selectTrainingType = (type: TrainingType) => {
  selectedTrainingType.value = type
}

const toggleHeroSelection = (hero: Hero) => {
  const index = selectedHeroes.value.indexOf(hero.heroId)
  if (index > -1) {
    selectedHeroes.value.splice(index, 1)
  } else {
    selectedHeroes.value.push(hero.heroId)
  }
}

const selectAllAvailable = () => {
  selectedHeroes.value = filteredHeroes.value
    .filter(hero => hero.status === 'normal')
    .map(hero => hero.heroId)
}

const selectByPosition = () => {
  if (!positionFilter.value) return
  selectedHeroes.value = filteredHeroes.value
    .filter(hero => hero.position?.includes(positionFilter.value))
    .map(hero => hero.heroId)
}

const clearSelection = () => {
  selectedHeroes.value = []
}

const filterHeroes = () => {
  // 筛选逻辑已在计算属性中实现
}

const calculateTotalCost = () => {
  if (!selectedTrainingType.value) {
    return { coins: 0, energy: 0, time: 0 }
  }

  const heroCount = selectedHeroes.value.length
  const type = selectedTrainingType.value

  return {
    coins: type.coinCost * heroCount * trainingCount.value,
    energy: type.energyCost * heroCount * trainingCount.value,
    time: type.duration * trainingCount.value
  }
}

const startTraining = async () => {
  if (!canStartTraining.value) return

  isStartingTraining.value = true
  
  try {
    // 调用训练API
    // await trainingService.startTraining({
    //   trainingTypeId: selectedTrainingType.value!.id,
    //   heroIds: selectedHeroes.value,
    //   count: trainingCount.value,
    //   items: selectedItems.value,
    //   autoRenew: autoRenew.value
    // })

    // 重置选择
    selectedHeroes.value = []
    selectedTrainingType.value = null
    
    // 刷新训练队列
    await loadTrainingQueue()
    
  } catch (error) {
    console.error('开始训练失败:', error)
  } finally {
    isStartingTraining.value = false
  }
}

const speedUpTraining = async (training: TrainingQueue) => {
  // 实现加速训练逻辑
}

const cancelTraining = async (training: TrainingQueue) => {
  // 实现取消训练逻辑
}

const loadTrainingQueue = async () => {
  // 加载训练队列数据
}

const getAttributeName = (attribute: string): string => {
  const names: Record<string, string> = {
    pace: '速度',
    shooting: '射门',
    passing: '传球',
    dribbling: '盘带',
    defending: '防守',
    physical: '身体'
  }
  return names[attribute] || attribute
}

const formatDuration = (ms: number): string => {
  const hours = Math.floor(ms / 3600000)
  const minutes = Math.floor((ms % 3600000) / 60000)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

// 生命周期
onMounted(() => {
  loadTrainingQueue()
})
</script>

<style lang="less" scoped>
.training-center {
  padding: @padding-lg;
}

.training-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @margin-xl;
  
  .header-info {
    .center-title {
      display: flex;
      align-items: center;
      gap: @margin-sm;
      color: @text-color;
      margin: 0 0 @margin-xs 0;
    }
    
    .center-description {
      color: @text-color-secondary;
      margin: 0;
    }
  }
  
  .training-stats {
    display: flex;
    gap: @margin-base;
    
    .stat-card {
      text-align: center;
      padding: @padding-base;
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      min-width: 80px;
      
      .stat-value {
        font-size: @font-size-xl;
        font-weight: bold;
        color: @primary-color;
      }
      
      .stat-label {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-top: @margin-xs;
      }
    }
  }
}

.section-title {
  color: @text-color;
  margin-bottom: @margin-base;
  font-size: @font-size-lg;
}

.training-types {
  margin-bottom: @margin-xl;
  
  .types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: @margin-base;
  }
  
  .training-type-card {
    .type-header {
      display: flex;
      align-items: center;
      gap: @margin-xs;
      
      .type-icon {
        color: @primary-color;
      }
      
      .type-name {
        font-weight: bold;
      }
    }
    
    .type-content {
      .type-description {
        color: @text-color-secondary;
        margin-bottom: @margin-base;
      }
      
      .type-effects {
        margin-bottom: @margin-base;
        
        .effect-title {
          font-weight: bold;
          color: @text-color;
          margin-bottom: @margin-xs;
        }
        
        .effects-list {
          display: flex;
          flex-wrap: wrap;
          gap: @margin-xs;
          
          .effect-item {
            background-color: fade(@success-color, 10%);
            border: 1px solid @success-color;
            border-radius: @border-radius-base;
            padding: 2px 8px;
            font-size: @font-size-xs;
            
            .effect-attr {
              color: @text-color-secondary;
            }
            
            .effect-value {
              color: @success-color;
              font-weight: bold;
              margin-left: 4px;
            }
          }
        }
      }
      
      .type-cost {
        display: flex;
        justify-content: space-between;
        
        .cost-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }
}

.hero-selection {
  margin-bottom: @margin-xl;
  
  .selection-filters {
    display: flex;
    gap: @margin-base;
    margin-bottom: @margin-base;
    align-items: center;
  }
  
  .heroes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: @margin-base;
    margin-bottom: @margin-base;
  }
  
  .batch-selection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: @padding-base;
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    
    .selection-info {
      color: @text-color-secondary;
      font-size: @font-size-sm;
    }
  }
}

.training-config {
  margin-bottom: @margin-xl;
  
  .config-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: @margin-base;
    margin-bottom: @margin-base;
    
    .config-item {
      display: flex;
      align-items: center;
      gap: @margin-sm;
      
      .config-label {
        min-width: 80px;
        color: @text-color;
      }
    }
  }
  
  .cost-summary {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    
    .summary-title {
      font-weight: bold;
      color: @text-color;
      margin-bottom: @margin-sm;
    }
    
    .cost-breakdown {
      .cost-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: @margin-xs;
        
        .cost-value {
          font-weight: bold;
          color: @primary-color;
        }
      }
    }
  }
}

.training-actions {
  text-align: center;
  margin-bottom: @margin-xl;
}

.training-queue {
  .queue-list {
    display: flex;
    flex-direction: column;
    gap: @margin-base;
  }
  
  .queue-item {
    display: flex;
    align-items: center;
    gap: @margin-base;
    padding: @padding-base;
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    
    .queue-hero {
      display: flex;
      align-items: center;
      gap: @margin-sm;
      min-width: 200px;
      
      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }
      
      .hero-info {
        .hero-name {
          font-weight: bold;
          color: @text-color;
        }
        
        .training-type {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
    
    .queue-progress {
      flex: 1;
      
      .progress-text {
        text-align: center;
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-top: @margin-xs;
      }
    }
    
    .queue-actions {
      display: flex;
      gap: @margin-xs;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .training-header {
    flex-direction: column;
    gap: @margin-base;
  }
  
  .training-stats {
    justify-content: center;
  }
  
  .types-grid {
    grid-template-columns: 1fr;
  }
  
  .heroes-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .config-panel {
    grid-template-columns: 1fr;
  }
  
  .batch-selection {
    flex-direction: column;
    gap: @margin-sm;
  }
}
</style>
