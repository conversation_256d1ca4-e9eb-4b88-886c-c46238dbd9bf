<template>
  <div 
    class="hero-card" 
    :class="[
      `rarity-${hero.rarity || 'common'}`,
      `position-${hero.position.toLowerCase()}`,
      { 'compact': compact, 'selectable': selectable, 'selected': selected }
    ]"
    @click="handleClick"
  >
    <!-- 紧凑模式 -->
    <template v-if="compact">
      <div class="hero-compact">
        <div class="hero-basic">
          <span class="hero-name">{{ hero.name }}</span>
          <span class="hero-overall">{{ hero.overall }}</span>
        </div>
        <div class="hero-info">
          <span class="hero-position">{{ hero.position }}</span>
          <span class="hero-level">Lv.{{ hero.level || 1 }}</span>
        </div>
      </div>
    </template>

    <!-- 完整模式 -->
    <template v-else>
      <!-- 卡片头部 -->
      <div class="hero-header">
        <div class="hero-title">
          <h3 class="hero-name">{{ hero.name }}</h3>
          <div class="hero-meta">
            <span class="hero-position">{{ hero.position }}</span>
            <span class="hero-nationality">{{ hero.nationality || '未知' }}</span>
          </div>
        </div>
        <div class="hero-rating">
          <span class="overall-score">{{ hero.overall }}</span>
          <span class="potential-score" v-if="hero.potential">
            ({{ hero.potential }})
          </span>
        </div>
      </div>

      <!-- 基础信息 -->
      <div class="hero-basic-info">
        <div class="info-row">
          <span class="info-label">年龄:</span>
          <span class="info-value">{{ hero.age || '未知' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">等级:</span>
          <span class="info-value">{{ hero.level || 1 }}</span>
        </div>
        <div class="info-row" v-if="hero.marketValue">
          <span class="info-label">身价:</span>
          <span class="info-value">{{ formatCurrency(hero.marketValue) }}</span>
        </div>
      </div>

      <!-- 属性显示 -->
      <div class="hero-attributes" v-if="hero.attributes">
        <div class="attributes-grid">
          <div class="attribute-item" v-for="(value, key) in hero.attributes" :key="key">
            <span class="attr-name">{{ getAttributeName(key) }}</span>
            <div class="attr-bar">
              <div 
                class="attr-fill" 
                :style="{ width: `${value}%` }"
                :class="getAttributeClass(value)"
              ></div>
              <span class="attr-value">{{ value }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态指示器 -->
      <div class="hero-status" v-if="hero.status">
        <a-tag 
          :color="getStatusColor(hero.status)"
          class="status-tag"
        >
          {{ getStatusText(hero.status) }}
        </a-tag>
      </div>

      <!-- 技能列表 -->
      <div class="hero-skills" v-if="hero.skills && hero.skills.length > 0">
        <div class="skills-title">特殊技能</div>
        <div class="skills-list">
          <a-tag 
            v-for="skill in hero.skills.slice(0, 3)" 
            :key="skill.id"
            size="small"
            class="skill-tag"
          >
            {{ skill.name }} Lv.{{ skill.level }}
          </a-tag>
          <span v-if="hero.skills.length > 3" class="more-skills">
            +{{ hero.skills.length - 3 }}
          </span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="hero-actions" v-if="showActions">
        <a-space>
          <a-button size="small" @click.stop="$emit('view-detail', hero)">
            详情
          </a-button>
          <a-button size="small" @click.stop="$emit('train', hero)">
            训练
          </a-button>
          <a-dropdown v-if="showMoreActions">
            <a-button size="small">
              更多
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="sell">出售</a-menu-item>
                <a-menu-item key="loan">租借</a-menu-item>
                <a-menu-item key="release">释放</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import type { Hero } from '@/types'

// Props
interface Props {
  hero: Hero
  compact?: boolean
  selectable?: boolean
  selected?: boolean
  showActions?: boolean
  showMoreActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false,
  selectable: false,
  selected: false,
  showActions: true,
  showMoreActions: true
})

// Emits
const emit = defineEmits<{
  click: [hero: Hero]
  'view-detail': [hero: Hero]
  train: [hero: Hero]
  sell: [hero: Hero]
  loan: [hero: Hero]
  release: [hero: Hero]
}>()

// 处理点击事件
const handleClick = () => {
  if (props.selectable) {
    emit('click', props.hero)
  }
}

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'sell':
      emit('sell', props.hero)
      break
    case 'loan':
      emit('loan', props.hero)
      break
    case 'release':
      emit('release', props.hero)
      break
  }
}

// 工具函数
const formatCurrency = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toString()
}

const getAttributeName = (key: string): string => {
  const names: Record<string, string> = {
    pace: '速度',
    shooting: '射门',
    passing: '传球',
    dribbling: '盘带',
    defending: '防守',
    physical: '身体'
  }
  return names[key] || key
}

const getAttributeClass = (value: number): string => {
  if (value >= 80) return 'excellent'
  if (value >= 70) return 'good'
  if (value >= 60) return 'average'
  return 'poor'
}

const getStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    active: 'green',
    injured: 'red',
    suspended: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string): string => {
  const texts: Record<string, string> = {
    active: '正常',
    injured: '受伤',
    suspended: '停赛'
  }
  return texts[status] || status
}
</script>

<style lang="less" scoped>
.hero-card {
  background-color: @card-bg;
  border: 2px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base;
  transition: all @transition-duration;
  font-family: @font-family-mono;
  position: relative;
  
  &:hover {
    border-color: @primary-color;
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.2);
    transform: translateY(-2px);
  }
  
  &.selectable {
    cursor: pointer;
    
    &.selected {
      border-color: @primary-color;
      background-color: rgba(82, 196, 26, 0.1);
    }
  }
  
  &.compact {
    padding: @padding-sm;
    min-height: auto;
  }
  
  // 稀有度样式
  &.rarity-common { border-left: 4px solid @rarity-common; }
  &.rarity-uncommon { border-left: 4px solid @rarity-uncommon; }
  &.rarity-rare { border-left: 4px solid @rarity-rare; }
  &.rarity-epic { border-left: 4px solid @rarity-epic; }
  &.rarity-legendary { border-left: 4px solid @rarity-legendary; }
  &.rarity-mythic { border-left: 4px solid @rarity-mythic; }
  
  // 位置样式
  &.position-gk::before { background-color: @position-gk; }
  &.position-def::before { background-color: @position-def; }
  &.position-mid::before { background-color: @position-mid; }
  &.position-att::before { background-color: @position-att; }
  
  &::before {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}

// 紧凑模式样式
.hero-compact {
  .hero-basic {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    
    .hero-name {
      font-weight: bold;
      color: @text-color;
    }
    
    .hero-overall {
      color: @highlight-color;
      font-weight: bold;
    }
  }
  
  .hero-info {
    display: flex;
    justify-content: space-between;
    font-size: @font-size-sm;
    color: @text-color-secondary;
  }
}

// 完整模式样式
.hero-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @margin-base;
  
  .hero-title {
    flex: 1;
    
    .hero-name {
      font-size: @font-size-lg;
      font-weight: bold;
      color: @text-color;
      margin: 0 0 4px 0;
    }
    
    .hero-meta {
      display: flex;
      gap: @margin-sm;
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
  }
  
  .hero-rating {
    text-align: right;
    
    .overall-score {
      font-size: @font-size-xl;
      font-weight: bold;
      color: @highlight-color;
    }
    
    .potential-score {
      font-size: @font-size-sm;
      color: @text-color-secondary;
      margin-left: 4px;
    }
  }
}

.hero-basic-info {
  margin-bottom: @margin-base;
  
  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: @font-size-sm;
    
    .info-label {
      color: @text-color-secondary;
    }
    
    .info-value {
      color: @text-color;
      font-weight: bold;
    }
  }
}

.hero-attributes {
  margin-bottom: @margin-base;
  
  .attributes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: @margin-xs;
  }
  
  .attribute-item {
    .attr-name {
      font-size: @font-size-xs;
      color: @text-color-secondary;
      display: block;
      margin-bottom: 2px;
    }
    
    .attr-bar {
      position: relative;
      height: 12px;
      background-color: @border-color;
      border-radius: 6px;
      overflow: hidden;
      
      .attr-fill {
        height: 100%;
        transition: width @transition-duration;
        
        &.excellent { background-color: @success-color; }
        &.good { background-color: @info-color; }
        &.average { background-color: @warning-color; }
        &.poor { background-color: @error-color; }
      }
      
      .attr-value {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%);
        font-size: @font-size-xs;
        color: @text-color;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
      }
    }
  }
}

.hero-status {
  margin-bottom: @margin-sm;
  
  .status-tag {
    font-family: @font-family-mono;
    font-size: @font-size-xs;
  }
}

.hero-skills {
  margin-bottom: @margin-base;
  
  .skills-title {
    font-size: @font-size-sm;
    color: @text-color-secondary;
    margin-bottom: @margin-xs;
  }
  
  .skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: @margin-xs;
    align-items: center;
    
    .skill-tag {
      font-family: @font-family-mono;
      font-size: @font-size-xs;
      background-color: rgba(82, 196, 26, 0.2);
      border-color: @primary-color;
      color: @primary-color;
    }
    
    .more-skills {
      font-size: @font-size-xs;
      color: @text-color-secondary;
    }
  }
}

.hero-actions {
  border-top: 1px solid @border-color;
  padding-top: @padding-sm;
  
  :deep(.ant-btn) {
    font-family: @font-family-mono;
    font-size: @font-size-xs;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hero-card {
    padding: @padding-sm;
    
    .hero-attributes .attributes-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
