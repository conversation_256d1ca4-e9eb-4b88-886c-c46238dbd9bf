<template>
  <div class="marketplace">
    <!-- 市场头部 -->
    <div class="market-header">
      <div class="header-info">
        <h2 class="market-title">
          <ShopOutlined />
          转会市场
        </h2>
        <p class="market-description">买卖球员，组建你的梦之队</p>
      </div>

      <div class="market-stats">
        <div class="stat-item">
          <div class="stat-value">{{ totalListings }}</div>
          <div class="stat-label">在售球员</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ myListings }}</div>
          <div class="stat-label">我的挂牌</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatCurrency(myBalance) }}</div>
          <div class="stat-label">我的余额</div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="market-filters">
      <div class="filters-row">
        <a-select
          v-model:value="filters.position"
          placeholder="位置"
          style="width: 120px"
          @change="applyFilters"
        >
          <a-select-option value="">全部位置</a-select-option>
          <a-select-option value="GK">门将</a-select-option>
          <a-select-option value="DF">后卫</a-select-option>
          <a-select-option value="MF">中场</a-select-option>
          <a-select-option value="FW">前锋</a-select-option>
        </a-select>

        <a-select
          v-model:value="filters.rarity"
          placeholder="稀有度"
          style="width: 120px"
          @change="applyFilters"
        >
          <a-select-option value="">全部稀有度</a-select-option>
          <a-select-option value="common">普通</a-select-option>
          <a-select-option value="uncommon">优秀</a-select-option>
          <a-select-option value="rare">稀有</a-select-option>
          <a-select-option value="epic">史诗</a-select-option>
          <a-select-option value="legendary">传奇</a-select-option>
        </a-select>

        <div class="price-range">
          <a-input-number
            v-model:value="filters.minPrice"
            placeholder="最低价格"
            :min="0"
            style="width: 120px"
            @change="applyFilters"
          />
          <span class="range-separator">-</span>
          <a-input-number
            v-model:value="filters.maxPrice"
            placeholder="最高价格"
            :min="0"
            style="width: 120px"
            @change="applyFilters"
          />
        </div>

        <a-input
          v-model:value="filters.playerName"
          placeholder="搜索球员姓名"
          style="width: 200px"
          @input="debounceSearch"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>

        <a-select
          v-model:value="filters.sortBy"
          style="width: 150px"
          @change="applyFilters"
        >
          <a-select-option value="price_asc">价格从低到高</a-select-option>
          <a-select-option value="price_desc">价格从高到低</a-select-option>
          <a-select-option value="rating_desc">评分从高到低</a-select-option>
          <a-select-option value="time_desc">最新上架</a-select-option>
        </a-select>
      </div>

      <div class="filters-actions">
        <a-button @click="resetFilters">重置筛选</a-button>
        <a-button type="primary" @click="showSellModal = true">
          <PlusOutlined />
          出售球员
        </a-button>
      </div>
    </div>

    <!-- 市场列表 -->
    <div class="market-listings">
      <a-spin :spinning="loading">
        <div v-if="filteredListings.length === 0" class="empty-state">
          <a-empty description="暂无符合条件的球员" />
        </div>
        
        <div v-else class="listings-grid">
          <GameCard
            v-for="listing in paginatedListings"
            :key="listing.id"
            class="listing-card"
            hoverable
          >
            <div class="listing-content">
              <!-- 球员信息 -->
              <div class="player-section">
                <div class="player-avatar">
                  <div class="hero-avatar-text">
                    {{ getHeroInitial(listing.hero?.name || listing.heroName) }}
                  </div>
                  <div class="rarity-indicator" :class="`rarity-${listing.hero?.rarity || 'common'}`"></div>
                </div>

                <div class="player-info">
                  <h4 class="player-name">{{ listing.hero?.name || listing.heroName }}</h4>
                  <div class="player-meta">
                    <span class="position">{{ listing.hero?.position || listing.position }}</span>
                    <span class="rating">{{ listing.hero?.overallRating || listing.overall }}</span>
                    <span class="level">Lv.{{ listing.hero?.level || listing.level }}</span>
                  </div>
                </div>
              </div>

              <!-- 价格信息 -->
              <div class="price-section">
                <div class="current-price">
                  <span class="price-label">当前价格</span>
                  <span class="price-value">{{ formatCurrency(listing.currentPrice) }}</span>
                </div>
                
                <div v-if="listing.type === 'auction'" class="auction-info">
                  <div class="time-left">
                    <ClockCircleOutlined />
                    {{ formatTimeLeft(listing.endTime) }}
                  </div>
                  <div class="bid-count">{{ listing.bidCount }} 次出价</div>
                </div>
                
                <div v-else class="buyout-info">
                  <div class="listing-type">一口价</div>
                </div>
              </div>

              <!-- 卖家信息 -->
              <div class="seller-section">
                <div class="seller-info">
                  <span class="seller-label">卖家:</span>
                  <span class="seller-name">{{ listing.sellerName }}</span>
                </div>
                <div class="listing-time">
                  {{ formatListingTime(listing.createdAt) }}
                </div>
              </div>
            </div>

            <template #footer>
              <div class="listing-actions">
                <a-space>
                  <GameTooltip title="查看详情">
                    <a-button size="small" @click="viewPlayerDetail(listing.hero)">
                      <EyeOutlined />
                    </a-button>
                  </GameTooltip>
                  
                  <template v-if="listing.type === 'auction'">
                    <a-button 
                      type="primary" 
                      size="small"
                      :disabled="!canBid(listing)"
                      @click="showBidModal(listing)"
                    >
                      出价
                    </a-button>
                  </template>
                  
                  <template v-else>
                    <a-button 
                      type="primary" 
                      size="small"
                      :disabled="!canBuy(listing)"
                      @click="buyPlayer(listing)"
                    >
                      购买
                    </a-button>
                  </template>

                  <a-button 
                    v-if="listing.sellerId === currentUserId"
                    danger
                    size="small"
                    @click="cancelListing(listing)"
                  >
                    取消
                  </a-button>
                </a-space>
              </div>
            </template>
          </GameCard>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="filteredListings.length"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
            @change="handlePageChange"
          />
        </div>
      </a-spin>
    </div>

    <!-- 出售球员模态框 -->
    <GameModal
      v-model:open="showSellModal"
      title="出售球员"
      width="600px"
      @ok="confirmSell"
      @cancel="showSellModal = false"
    >
      <div class="sell-modal-content">
        <!-- 选择球员 -->
        <div class="select-player">
          <h4>选择要出售的球员</h4>
          <div class="my-heroes-grid">
            <HeroCard
              v-for="hero in myHeroes"
              :key="hero.heroId"
              :hero="hero"
              :selectable="true"
              :selected="selectedHeroForSell?.heroId === hero.heroId"
              :compact="true"
              @click="selectHeroForSell(hero)"
            />
          </div>
        </div>

        <!-- 设置价格 -->
        <div v-if="selectedHeroForSell" class="price-settings">
          <h4>设置价格</h4>
          
          <div class="price-type">
            <a-radio-group v-model:value="sellForm.type">
              <a-radio value="buyout">一口价</a-radio>
              <a-radio value="auction">拍卖</a-radio>
            </a-radio-group>
          </div>

          <div class="price-inputs">
            <div class="input-group">
              <label>起始价格:</label>
              <a-input-number
                v-model:value="sellForm.startPrice"
                :min="1000"
                style="width: 200px"
                placeholder="起始价格"
              />
            </div>

            <div v-if="sellForm.type === 'buyout'" class="input-group">
              <label>一口价:</label>
              <a-input-number
                v-model:value="sellForm.buyoutPrice"
                :min="sellForm.startPrice"
                style="width: 200px"
                placeholder="一口价"
              />
            </div>

            <div v-if="sellForm.type === 'auction'" class="input-group">
              <label>拍卖时长:</label>
              <a-select v-model:value="sellForm.duration" style="width: 200px">
                <a-select-option :value="3600000">1小时</a-select-option>
                <a-select-option :value="21600000">6小时</a-select-option>
                <a-select-option :value="43200000">12小时</a-select-option>
                <a-select-option :value="86400000">24小时</a-select-option>
              </a-select>
            </div>
          </div>

          <div class="price-suggestion">
            <div class="suggestion-title">建议价格范围:</div>
            <div class="suggestion-range">
              {{ formatCurrency(getSuggestedPrice().min) }} - {{ formatCurrency(getSuggestedPrice().max) }}
            </div>
          </div>
        </div>
      </div>
    </GameModal>

    <!-- 出价模态框 -->
    <GameModal
      v-model:open="showBidModalVisible"
      title="竞价出价"
      @ok="confirmBid"
      @cancel="showBidModalVisible = false"
    >
      <div v-if="selectedListing" class="bid-modal-content">
        <div class="bid-player-info">
          <img :src="selectedListing.hero.avatar" :alt="selectedListing.hero.name" />
          <div>
            <h4>{{ selectedListing.hero.name }}</h4>
            <p>{{ selectedListing.hero.position }} | {{ selectedListing.hero.overallRating }}</p>
          </div>
        </div>

        <div class="bid-info">
          <div class="current-bid">
            <span>当前价格: {{ formatCurrency(selectedListing.currentPrice) }}</span>
          </div>
          <div class="min-bid">
            <span>最低出价: {{ formatCurrency(getMinBidAmount(selectedListing)) }}</span>
          </div>
        </div>

        <div class="bid-input">
          <label>您的出价:</label>
          <a-input-number
            v-model:value="bidAmount"
            :min="getMinBidAmount(selectedListing)"
            :max="myBalance"
            style="width: 200px"
          />
        </div>
      </div>
    </GameModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ShopOutlined,
  SearchOutlined,
  PlusOutlined,
  EyeOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import GameCard from '@/components/common/GameCard.vue'
import GameModal from '@/components/common/GameModal.vue'
import GameTooltip from '@/components/common/GameTooltip.vue'
import HeroCard from './HeroCard.vue'
import { componentUtils } from '@/components'
import type { MarketListing, Hero } from '@/types'

// 响应式数据
const loading = ref(false)
const showSellModal = ref(false)
const showBidModalVisible = ref(false)
const selectedListing = ref<MarketListing | null>(null)
const selectedHeroForSell = ref<Hero | null>(null)
const bidAmount = ref(0)

// 筛选器
const filters = ref({
  position: '',
  rarity: '',
  minPrice: null as number | null,
  maxPrice: null as number | null,
  playerName: '',
  sortBy: 'time_desc'
})

// 出售表单
const sellForm = ref({
  type: 'buyout',
  startPrice: 10000,
  buyoutPrice: 15000,
  duration: 86400000
})

// 分页
const pagination = ref({
  current: 1,
  pageSize: 12
})

// 模拟数据
const listings = ref<MarketListing[]>([])
const myHeroes = ref<Hero[]>([])
const totalListings = ref(156)
const myListings = ref(3)
const myBalance = ref(50000)
const currentUserId = ref('user123')

// 添加缺失的响应式变量
const searchText = ref('')

// 计算属性
const filteredListings = computed(() => {
  let result = [...listings.value]
  
  // 应用筛选器
  if (filters.value.position) {
    result = result.filter(listing => listing.hero.position?.includes(filters.value.position))
  }
  
  if (filters.value.rarity) {
    result = result.filter(listing => listing.hero.rarity === filters.value.rarity)
  }
  
  if (filters.value.minPrice) {
    result = result.filter(listing => listing.currentPrice >= filters.value.minPrice!)
  }
  
  if (filters.value.maxPrice) {
    result = result.filter(listing => listing.currentPrice <= filters.value.maxPrice!)
  }
  
  if (filters.value.playerName) {
    result = result.filter(listing => 
      listing.hero.name.toLowerCase().includes(filters.value.playerName.toLowerCase())
    )
  }
  
  // 排序
  switch (filters.value.sortBy) {
    case 'price_asc':
      result.sort((a, b) => a.currentPrice - b.currentPrice)
      break
    case 'price_desc':
      result.sort((a, b) => b.currentPrice - a.currentPrice)
      break
    case 'rating_desc':
      result.sort((a, b) => (b.hero.overallRating || 0) - (a.hero.overallRating || 0))
      break
    case 'time_desc':
      result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
  }
  
  return result
})

const paginatedListings = computed(() => {
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredListings.value.slice(start, end)
})

// 方法
const formatCurrency = componentUtils.formatCurrency
const debounceSearch = componentUtils.debounce(() => applyFilters(), 500)

const applyFilters = () => {
  pagination.value.current = 1
  // 触发重新计算
}

const resetFilters = () => {
  filters.value = {
    position: '',
    rarity: '',
    minPrice: null,
    maxPrice: null,
    playerName: '',
    sortBy: 'time_desc'
  }
  applyFilters()
}

const handlePageChange = () => {
  // 分页变化处理
}

const canBid = (listing: MarketListing): boolean => {
  return listing.sellerId !== currentUserId.value &&
         myBalance.value >= getMinBidAmount(listing)
}

const canBuy = (listing: MarketListing): boolean => {
  return listing.sellerId !== currentUserId.value &&
         myBalance.value >= listing.currentPrice
}

const getMinBidAmount = (listing: MarketListing): number => {
  return Math.floor(listing.currentPrice * 1.05) // 最少加价5%
}

const showBidModal = (listing: MarketListing) => {
  selectedListing.value = listing
  bidAmount.value = getMinBidAmount(listing)
  showBidModalVisible.value = true
}

const confirmBid = async () => {
  // 实现出价逻辑
  showBidModalVisible.value = false
}

const buyPlayer = async (listing: MarketListing) => {
  // 实现购买逻辑
  console.log('购买球员:', listing.hero.name)
}

const cancelListing = async (listing: MarketListing) => {
  // 实现取消挂牌逻辑
  console.log('取消挂牌:', listing.hero.name)
}

const viewPlayerDetail = (hero: Hero) => {
  // 查看球员详情
  console.log('查看球员详情:', hero.name)
}

const selectHeroForSell = (hero: Hero) => {
  selectedHeroForSell.value = hero
}

const getSuggestedPrice = () => {
  if (!selectedHeroForSell.value) return { min: 0, max: 0 }

  const rating = selectedHeroForSell.value.overallRating || 50
  const basePrice = rating * 1000

  return {
    min: Math.floor(basePrice * 0.8),
    max: Math.floor(basePrice * 1.2)
  }
}

const confirmSell = async () => {
  // 实现出售逻辑
  console.log('出售球员:', selectedHeroForSell.value?.name)
  showSellModal.value = false
}

const formatTimeLeft = (endTime: string): string => {
  const now = new Date().getTime()
  const end = new Date(endTime).getTime()
  const diff = end - now
  
  if (diff <= 0) return '已结束'
  
  const hours = Math.floor(diff / 3600000)
  const minutes = Math.floor((diff % 3600000) / 60000)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

const formatListingTime = (time: string): string => {
  const now = new Date()
  const listingTime = new Date(time)
  const diff = now.getTime() - listingTime.getTime()
  
  const days = Math.floor(diff / 86400000)
  const hours = Math.floor((diff % 86400000) / 3600000)
  
  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  }
  return '刚刚'
}

// 生命周期
onMounted(() => {
  // 加载市场数据
})
</script>

<style lang="less" scoped>
.marketplace {
  padding: @padding-lg;
}

.market-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: @margin-xl;
  
  .header-info {
    .market-title {
      display: flex;
      align-items: center;
      gap: @margin-sm;
      color: @text-color;
      margin: 0 0 @margin-xs 0;
    }
    
    .market-description {
      color: @text-color-secondary;
      margin: 0;
    }
  }
  
  .market-stats {
    display: flex;
    gap: @margin-base;
    
    .stat-item {
      text-align: center;
      padding: @padding-base;
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      min-width: 100px;
      
      .stat-value {
        font-size: @font-size-xl;
        font-weight: bold;
        color: @primary-color;
      }
      
      .stat-label {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-top: @margin-xs;
      }
    }
  }
}

.market-filters {
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base;
  margin-bottom: @margin-lg;
  
  .filters-row {
    display: flex;
    gap: @margin-base;
    align-items: center;
    margin-bottom: @margin-base;
    flex-wrap: wrap;
    
    .price-range {
      display: flex;
      align-items: center;
      gap: @margin-xs;
      
      .range-separator {
        color: @text-color-secondary;
      }
    }
  }
  
  .filters-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.market-listings {
  .empty-state {
    text-align: center;
    padding: @padding-xl;
  }
  
  .listings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: @margin-base;
    margin-bottom: @margin-lg;
  }
  
  .listing-card {
    .listing-content {
      .player-section {
        display: flex;
        align-items: center;
        gap: @margin-base;
        margin-bottom: @margin-base;
        
        .player-avatar {
          position: relative;
          width: 60px;
          height: 60px;
          
          .hero-avatar-text {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: @primary-color;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-weight: bold;
            font-size: @font-size-xl;
          }
          
          .rarity-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            
            &.rarity-common { background-color: #8c8c8c; }
            &.rarity-uncommon { background-color: #52c41a; }
            &.rarity-rare { background-color: #1890ff; }
            &.rarity-epic { background-color: #722ed1; }
            &.rarity-legendary { background-color: #fa8c16; }
          }
        }
        
        .player-info {
          flex: 1;
          
          .player-name {
            color: @text-color;
            margin: 0 0 @margin-xs 0;
            font-size: @font-size-base;
          }
          
          .player-meta {
            display: flex;
            gap: @margin-sm;
            font-size: @font-size-sm;
            
            .position {
              color: @primary-color;
              font-weight: bold;
            }
            
            .rating {
              color: @warning-color;
              font-weight: bold;
            }
            
            .level {
              color: @text-color-secondary;
            }
          }
        }
      }
      
      .price-section {
        margin-bottom: @margin-base;
        
        .current-price {
          .price-label {
            color: @text-color-secondary;
            font-size: @font-size-sm;
            display: block;
          }
          
          .price-value {
            color: @success-color;
            font-size: @font-size-lg;
            font-weight: bold;
          }
        }
        
        .auction-info {
          margin-top: @margin-xs;
          
          .time-left {
            color: @warning-color;
            font-size: @font-size-sm;
            display: flex;
            align-items: center;
            gap: 4px;
          }
          
          .bid-count {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
        
        .buyout-info {
          .listing-type {
            color: @info-color;
            font-size: @font-size-sm;
            font-weight: bold;
          }
        }
      }
      
      .seller-section {
        .seller-info {
          .seller-label {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
          
          .seller-name {
            color: @text-color;
            font-weight: 500;
          }
        }
        
        .listing-time {
          color: @text-color-secondary;
          font-size: @font-size-xs;
          margin-top: 2px;
        }
      }
    }
  }
  
  .pagination-wrapper {
    text-align: center;
    margin-top: @margin-lg;
  }
}

.sell-modal-content {
  .select-player {
    margin-bottom: @margin-lg;
    
    h4 {
      color: @text-color;
      margin-bottom: @margin-base;
    }
    
    .my-heroes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: @margin-base;
      max-height: 300px;
      overflow-y: auto;
    }
  }
  
  .price-settings {
    h4 {
      color: @text-color;
      margin-bottom: @margin-base;
    }
    
    .price-type {
      margin-bottom: @margin-base;
    }
    
    .price-inputs {
      .input-group {
        display: flex;
        align-items: center;
        gap: @margin-base;
        margin-bottom: @margin-base;
        
        label {
          min-width: 80px;
          color: @text-color;
        }
      }
    }
    
    .price-suggestion {
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: @border-radius-base;
      padding: @padding-base;
      margin-top: @margin-base;
      
      .suggestion-title {
        color: @text-color-secondary;
        font-size: @font-size-sm;
      }
      
      .suggestion-range {
        color: @primary-color;
        font-weight: bold;
        margin-top: @margin-xs;
      }
    }
  }
}

.bid-modal-content {
  .bid-player-info {
    display: flex;
    align-items: center;
    gap: @margin-base;
    margin-bottom: @margin-lg;
    
    img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
    }
    
    h4 {
      color: @text-color;
      margin: 0;
    }
    
    p {
      color: @text-color-secondary;
      margin: @margin-xs 0 0 0;
    }
  }
  
  .bid-info {
    margin-bottom: @margin-lg;
    
    .current-bid,
    .min-bid {
      margin-bottom: @margin-xs;
      
      span {
        color: @text-color;
      }
    }
  }
  
  .bid-input {
    display: flex;
    align-items: center;
    gap: @margin-base;
    
    label {
      color: @text-color;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .market-header {
    flex-direction: column;
    gap: @margin-base;
  }
  
  .market-stats {
    justify-content: center;
  }
  
  .filters-row {
    flex-direction: column;
    align-items: stretch;
    
    > * {
      width: 100% !important;
    }
  }
  
  .listings-grid {
    grid-template-columns: 1fr;
  }
  
  .my-heroes-grid {
    grid-template-columns: 1fr;
  }
}
</style>
