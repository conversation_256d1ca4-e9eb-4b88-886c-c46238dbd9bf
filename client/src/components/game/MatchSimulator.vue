<template>
  <div class="match-simulator">
    <!-- 比赛头部信息 -->
    <div class="match-header">
      <div class="team home-team">
        <div class="team-logo">
          <img :src="match.homeTeam.logo" :alt="match.homeTeam.name" />
        </div>
        <div class="team-info">
          <h3 class="team-name">{{ match.homeTeam.name }}</h3>
          <div class="team-rating">{{ match.homeTeam.rating }}</div>
        </div>
      </div>

      <div class="match-center">
        <div class="match-score">
          <span class="score-home">{{ match.homeScore }}</span>
          <span class="score-separator">:</span>
          <span class="score-away">{{ match.awayScore }}</span>
        </div>
        
        <div class="match-time">
          <div class="time-display">{{ formatMatchTime(match.currentTime) }}'</div>
          <div class="match-status" :class="`status-${match.status}`">
            {{ getStatusText(match.status) }}
          </div>
        </div>

        <div class="match-controls" v-if="canControl">
          <a-space>
            <a-button 
              v-if="match.status === 'waiting'"
              type="primary"
              @click="startMatch"
              :loading="isStarting"
            >
              开始比赛
            </a-button>
            
            <a-button 
              v-if="match.status === 'playing'"
              @click="pauseMatch"
            >
              暂停
            </a-button>
            
            <a-button 
              v-if="match.status === 'paused'"
              type="primary"
              @click="resumeMatch"
            >
              继续
            </a-button>

            <a-dropdown>
              <a-button>
                速度 {{ playbackSpeed }}x
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu @click="changeSpeed">
                  <a-menu-item key="0.5">0.5x</a-menu-item>
                  <a-menu-item key="1">1x</a-menu-item>
                  <a-menu-item key="2">2x</a-menu-item>
                  <a-menu-item key="4">4x</a-menu-item>
                  <a-menu-item key="8">8x</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </div>

      <div class="team away-team">
        <div class="team-info">
          <h3 class="team-name">{{ match.awayTeam.name }}</h3>
          <div class="team-rating">{{ match.awayTeam.rating }}</div>
        </div>
        <div class="team-logo">
          <img :src="match.awayTeam.logo" :alt="match.awayTeam.name" />
        </div>
      </div>
    </div>

    <!-- 比赛进度条 -->
    <div class="match-progress">
      <GameProgress
        :current="match.currentTime"
        :total="90"
        :show-label="false"
        :show-percent="false"
        size="large"
        type="primary"
        :segments="getTimeSegments()"
      />
    </div>

    <!-- 比赛统计 -->
    <div class="match-stats">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value home">{{ match.stats.homeShots }}</div>
          <div class="stat-label">射门</div>
          <div class="stat-value away">{{ match.stats.awayShots }}</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value home">{{ match.stats.homeShotsOnTarget }}</div>
          <div class="stat-label">射正</div>
          <div class="stat-value away">{{ match.stats.awayShotsOnTarget }}</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value home">{{ match.stats.homePossession }}%</div>
          <div class="stat-label">控球率</div>
          <div class="stat-value away">{{ match.stats.awayPossession }}%</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value home">{{ match.stats.homeCorners }}</div>
          <div class="stat-label">角球</div>
          <div class="stat-value away">{{ match.stats.awayCorners }}</div>
        </div>
        
        <div class="stat-item">
          <div class="stat-value home">{{ match.stats.homeFouls }}</div>
          <div class="stat-label">犯规</div>
          <div class="stat-value away">{{ match.stats.awayFouls }}</div>
        </div>
      </div>
    </div>

    <!-- 比赛事件 -->
    <div class="match-events">
      <h4 class="events-title">比赛事件</h4>
      
      <div class="events-timeline">
        <div
          v-for="event in sortedEvents"
          :key="event.id"
          class="event-item"
          :class="`event-${event.type}`"
        >
          <div class="event-time">{{ event.minute }}'</div>
          
          <div class="event-content" :class="{ 'event-away': event.team === 'away' }">
            <div class="event-icon">
              <component :is="getEventIcon(event.type)" />
            </div>
            
            <div class="event-details">
              <div class="event-description">{{ event.description }}</div>
              <div v-if="event.player" class="event-player">{{ event.player }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 阵容对比 -->
    <div v-if="showLineups" class="match-lineups">
      <div class="lineups-header">
        <h4>首发阵容</h4>
        <a-switch v-model:checked="showFormation" />
        <span>显示阵型</span>
      </div>

      <div class="lineups-content">
        <div class="lineup home-lineup">
          <h5>{{ match.homeTeam.name }}</h5>
          <div v-if="showFormation" class="formation-display">
            <FormationField
              v-if="match.homeTeam.formation"
              :formation="match.homeTeam.formation"
              :heroes="match.homeTeam.players || []"
              :interactive="false"
            />
          </div>
          <div v-else class="players-list">
            <div
              v-for="player in match.homeTeam.players"
              :key="player.id"
              class="player-item"
            >
              <span class="player-number">{{ player.number }}</span>
              <span class="player-name">{{ player.name }}</span>
              <span class="player-position">{{ player.position }}</span>
            </div>
          </div>
        </div>

        <div class="lineup away-lineup">
          <h5>{{ match.awayTeam.name }}</h5>
          <div v-if="showFormation" class="formation-display">
            <FormationField
              :formation="match.awayTeam.formation"
              :heroes="match.awayTeam.players"
              :interactive="false"
            />
          </div>
          <div v-else class="players-list">
            <div
              v-for="player in match.awayTeam.players"
              :key="player.id"
              class="player-item"
            >
              <span class="player-number">{{ player.number }}</span>
              <span class="player-name">{{ player.name }}</span>
              <span class="player-position">{{ player.position }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import GameProgress from '@/components/common/GameProgress.vue'
import FormationField from './FormationField.vue'
import type { Match, MatchEvent } from '@/types'

interface Props {
  match: Match
  canControl?: boolean
  showLineups?: boolean
  autoUpdate?: boolean
}

interface Emits {
  (e: 'match-start'): void
  (e: 'match-pause'): void
  (e: 'match-resume'): void
  (e: 'match-end'): void
  (e: 'speed-change', speed: number): void
}

const props = withDefaults(defineProps<Props>(), {
  canControl: false,
  showLineups: true,
  autoUpdate: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const isStarting = ref(false)
const playbackSpeed = ref(1)
const showFormation = ref(true)
const updateInterval = ref<NodeJS.Timeout | null>(null)

// 计算属性
const sortedEvents = computed(() => {
  return [...props.match.events].sort((a, b) => a.minute - b.minute)
})

// 方法
const formatMatchTime = (time: number): number => {
  return Math.floor(time)
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    waiting: '等待开始',
    playing: '进行中',
    paused: '暂停',
    halftime: '中场休息',
    finished: '已结束'
  }
  return statusMap[status] || status
}

const getTimeSegments = () => {
  return [
    { value: 45, label: '上半场结束' },
    { value: 90, label: '全场结束' }
  ]
}

const getEventIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    goal: 'TrophyOutlined',
    yellow_card: 'WarningOutlined',
    red_card: 'StopOutlined',
    substitution: 'SwapOutlined',
    corner: 'CornerOutlined',
    offside: 'FlagOutlined'
  }
  return iconMap[type] || 'InfoCircleOutlined'
}

const startMatch = async () => {
  isStarting.value = true
  try {
    emit('match-start')
    if (props.autoUpdate) {
      startAutoUpdate()
    }
  } finally {
    isStarting.value = false
  }
}

const pauseMatch = () => {
  emit('match-pause')
  stopAutoUpdate()
}

const resumeMatch = () => {
  emit('match-resume')
  if (props.autoUpdate) {
    startAutoUpdate()
  }
}

const changeSpeed = ({ key }: { key: string }) => {
  const speed = parseFloat(key)
  playbackSpeed.value = speed
  emit('speed-change', speed)
  
  if (props.autoUpdate && props.match.status === 'playing') {
    stopAutoUpdate()
    startAutoUpdate()
  }
}

const startAutoUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
  
  const interval = 1000 / playbackSpeed.value
  updateInterval.value = setInterval(() => {
    // 这里可以触发比赛数据更新
    if (props.match.status !== 'playing') {
      stopAutoUpdate()
    }
  }, interval)
}

const stopAutoUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 监听比赛状态变化
watch(() => props.match.status, (newStatus) => {
  if (newStatus === 'finished') {
    stopAutoUpdate()
    emit('match-end')
  }
})

// 生命周期
onMounted(() => {
  if (props.autoUpdate && props.match.status === 'playing') {
    startAutoUpdate()
  }
})

onUnmounted(() => {
  stopAutoUpdate()
})
</script>

<style lang="less" scoped>
.match-simulator {
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-lg;
}

.match-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: @margin-lg;
  
  .team {
    display: flex;
    align-items: center;
    gap: @margin-base;
    min-width: 200px;
    
    &.away-team {
      flex-direction: row-reverse;
      text-align: right;
    }
    
    .team-logo {
      width: 60px;
      height: 60px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }
    }
    
    .team-info {
      .team-name {
        color: @text-color;
        margin: 0 0 @margin-xs 0;
        font-size: @font-size-lg;
      }
      
      .team-rating {
        color: @primary-color;
        font-weight: bold;
        font-size: @font-size-base;
      }
    }
  }
  
  .match-center {
    text-align: center;
    flex: 1;
    max-width: 300px;
    
    .match-score {
      font-size: 3rem;
      font-weight: bold;
      color: @text-color;
      margin-bottom: @margin-sm;
      
      .score-separator {
        margin: 0 @margin-base;
        color: @text-color-secondary;
      }
    }
    
    .match-time {
      margin-bottom: @margin-base;
      
      .time-display {
        font-size: @font-size-xl;
        font-weight: bold;
        color: @primary-color;
      }
      
      .match-status {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-top: @margin-xs;
        
        &.status-playing {
          color: @success-color;
        }
        
        &.status-paused {
          color: @warning-color;
        }
        
        &.status-finished {
          color: @error-color;
        }
      }
    }
  }
}

.match-progress {
  margin-bottom: @margin-lg;
}

.match-stats {
  margin-bottom: @margin-lg;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: @margin-base;
    
    .stat-item {
      text-align: center;
      padding: @padding-base;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: @border-radius-base;
      
      .stat-label {
        color: @text-color-secondary;
        font-size: @font-size-sm;
        margin: @margin-xs 0;
      }
      
      .stat-value {
        font-weight: bold;
        font-size: @font-size-lg;
        
        &.home {
          color: @primary-color;
        }
        
        &.away {
          color: @info-color;
        }
      }
    }
  }
}

.match-events {
  margin-bottom: @margin-lg;
  
  .events-title {
    color: @text-color;
    margin-bottom: @margin-base;
  }
  
  .events-timeline {
    max-height: 300px;
    overflow-y: auto;
    
    .event-item {
      display: flex;
      align-items: center;
      gap: @margin-base;
      padding: @padding-sm 0;
      border-bottom: 1px solid @border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      .event-time {
        min-width: 40px;
        font-weight: bold;
        color: @primary-color;
        text-align: center;
      }
      
      .event-content {
        display: flex;
        align-items: center;
        gap: @margin-sm;
        flex: 1;
        
        &.event-away {
          flex-direction: row-reverse;
          text-align: right;
        }
        
        .event-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: @primary-color;
          color: white;
          font-size: @font-size-sm;
        }
        
        .event-details {
          .event-description {
            color: @text-color;
            font-weight: 500;
          }
          
          .event-player {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
      }
      
      &.event-goal .event-icon {
        background-color: @success-color;
      }
      
      &.event-yellow_card .event-icon {
        background-color: @warning-color;
      }
      
      &.event-red_card .event-icon {
        background-color: @error-color;
      }
    }
  }
}

.match-lineups {
  .lineups-header {
    display: flex;
    align-items: center;
    gap: @margin-base;
    margin-bottom: @margin-base;
    
    h4 {
      color: @text-color;
      margin: 0;
    }
  }
  
  .lineups-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: @margin-xl;
    
    .lineup {
      h5 {
        color: @text-color;
        text-align: center;
        margin-bottom: @margin-base;
      }
      
      .formation-display {
        height: 400px;
      }
      
      .players-list {
        .player-item {
          display: flex;
          align-items: center;
          gap: @margin-sm;
          padding: @padding-xs 0;
          border-bottom: 1px solid @border-color;
          
          &:last-child {
            border-bottom: none;
          }
          
          .player-number {
            min-width: 30px;
            text-align: center;
            font-weight: bold;
            color: @primary-color;
          }
          
          .player-name {
            flex: 1;
            color: @text-color;
          }
          
          .player-position {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .match-header {
    flex-direction: column;
    gap: @margin-base;
    
    .team {
      min-width: auto;
      justify-content: center;
    }
    
    .match-center {
      max-width: none;
    }
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .lineups-content {
    grid-template-columns: 1fr;
  }
}
</style>
