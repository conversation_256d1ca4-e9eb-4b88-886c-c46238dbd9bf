// 自动生成的formation模块类型定义
// 基于服务端Schema生成，请勿手动修改

export enum FormationType {
  COMMON = 1,
  LEAGUE = 2,
  WAR_OF_FAITH = 3,
  TOURNAMENT = 4,
  FRIENDLY = 5,
}

export enum TeamType {
  MAIN = 1,
  SUBSTITUTE = 2,
  RESERVE = 3,
}

export interface FormationTrainer {
  uid?: string;
  resId?: number;
  type?: number;
  level?: number;
  readonly tactics?: any[];
}

export interface ReadonlyFormationTrainer {
  readonly uid?: string;
  readonly resId?: number;
  readonly type?: number;
  readonly level?: number;
  readonly tactics?: any[];
}

export interface PositionToHerosObject {
  readonly GK?: string[];
  readonly DL?: string[];
  readonly DC?: string[];
  readonly DR?: string[];
  readonly ML?: string[];
  readonly MC?: string[];
  readonly MR?: string[];
  readonly WL?: string[];
  readonly ST?: string[];
  readonly WR?: string[];
  readonly AM?: string[];
  readonly DM?: string[];
}

export interface ReadonlyPositionToHerosObject {
  readonly GK?: string[];
  readonly DL?: string[];
  readonly DC?: string[];
  readonly DR?: string[];
  readonly ML?: string[];
  readonly MC?: string[];
  readonly MR?: string[];
  readonly WL?: string[];
  readonly ST?: string[];
  readonly WR?: string[];
  readonly AM?: string[];
  readonly DM?: string[];
}

export interface TeamFormation {
  uid: string;
  resId: number;
  name?: string;
  attack?: number;
  defend?: number;
  actualStrength?: number;
  isInitName?: number;
  isInitFormation?: number;
  isLeague?: number;
  teamId?: number;
  teamType?: number;
  readonly scenceUse?: string[];
  inspireRate?: number;
  useTactics?: number;
  useDefTactics?: number;
  freeKickHero?: string;
  penaltiesHero?: string;
  cornerKickHero?: string;
  readonly trainers?: FormationTrainer[];
  type?: number;
}

export interface ReadonlyTeamFormation {
  readonly uid: string;
  readonly resId: number;
  readonly name?: string;
  readonly attack?: number;
  readonly defend?: number;
  readonly actualStrength?: number;
  readonly isInitName?: number;
  readonly isInitFormation?: number;
  readonly isLeague?: number;
  readonly teamId?: number;
  readonly teamType?: number;
  readonly scenceUse?: string[];
  readonly inspireRate?: number;
  readonly useTactics?: number;
  readonly useDefTactics?: number;
  readonly freeKickHero?: string;
  readonly penaltiesHero?: string;
  readonly cornerKickHero?: string;
  readonly trainers?: FormationTrainer[];
  readonly type?: number;
}

export interface TeamFormations {
  uid: string;
  characterId: string;
  serverId: string;
  readonly teamFormations?: TeamFormation[];
  currTeamFormationId?: string;
  leagueTeamFormationId?: string;
  warOfFaithTeamFormationId?: string;
  allTactics?: Record<string, any>;
  allDefTactics?: Record<string, any>;
  readonly allFormations?: any[];
  fixId?: number;
}

export interface ReadonlyTeamFormations {
  readonly uid: string;
  readonly characterId: string;
  readonly serverId: string;
  readonly teamFormations?: TeamFormation[];
  readonly currTeamFormationId?: string;
  readonly leagueTeamFormationId?: string;
  readonly warOfFaithTeamFormationId?: string;
  readonly allTactics?: Record<string, any>;
  readonly allDefTactics?: Record<string, any>;
  readonly allFormations?: any[];
  readonly fixId?: number;
}


