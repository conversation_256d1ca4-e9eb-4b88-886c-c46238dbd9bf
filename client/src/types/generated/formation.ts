// 自动生成的Formation模块类型定义
// 基于服务端Schema生成，请勿手动修改

// 阵型类型枚举
export enum FormationType {
  LEAGUE = 1,
  FRIENDLY = 2,
  TOURNAMENT = 3
}

// 英雄位置信息接口
export interface HeroPosition {
  readonly heroId?: string;
  readonly position: string;
  readonly x: number;
  readonly y: number;
  readonly role?: string;
}

// 队伍阵型接口
export interface TeamFormation {
  readonly uid: string;
  readonly resId: number;
  readonly name: string;
  readonly attack: number;
  readonly defend: number;
  readonly actualStrength: number;
  readonly isInitName: number;
  readonly isInitFormation: number;
  readonly isActive: number;
  readonly isLeague?: number;
  readonly teamId?: number;
  readonly teamType?: number;
  readonly positionToHerosObject?: Record<string, readonly string[]>;
  readonly freeKickHero?: string;
  readonly penaltiesHero?: string;
  readonly cornerKickHero?: string;
  readonly useTactics?: number;
  readonly useDefTactics?: number;
  readonly scenceUse?: readonly any[];
  readonly inspireRate?: number;
  readonly type: FormationType;
  readonly heroPositions?: readonly HeroPosition[];
  readonly createdAt: number;
  readonly updatedAt: number;
  readonly deletedAt: number;
  readonly version: number;
  readonly characterId: string;
  readonly serverId: string;
}

// 阵型主接口（基于服务端Schema）
export interface Formation {
  readonly uid: string;
  readonly characterId: string;
  readonly serverId: string;
  readonly currTeamFormationId: string;
  readonly teamFormations: readonly TeamFormation[];
  readonly createdAt?: number;
  readonly updatedAt?: number;
  readonly deletedAt?: number;
  readonly version?: number;
}

// 前端显示用的简化阵型接口
export interface ClientFormation {
  readonly id: string;
  readonly name: string;
  readonly formation: string;
  readonly description?: string;
  readonly positions: readonly FormationPosition[];
  readonly tactics: TacticSettings;
  readonly isActive: boolean;
}

// 阵型位置接口（前端用）
export interface FormationPosition {
  readonly id: string;
  readonly position: string;
  readonly x: number;
  readonly y: number;
  readonly heroId?: string | null;
  readonly role?: string;
}

// 战术设置接口
export interface TacticSettings {
  readonly attackingStyle?: 'possession' | 'counter' | 'direct';
  readonly defendingStyle?: 'high_press' | 'mid_block' | 'low_block';
  readonly tempo?: number;
  readonly width?: number;
  readonly mentality?: 'defensive' | 'balanced' | 'attacking';
  readonly attacking?: number;
  readonly defending?: number;
  readonly pressing?: number;
}

// 可变版本（用于前端编辑）
export interface MutableFormation extends Omit<Formation, 'teamFormations'> {
  teamFormations: TeamFormation[];
}

export interface MutableClientFormation extends Omit<ClientFormation, 'positions'> {
  positions: FormationPosition[];
}
