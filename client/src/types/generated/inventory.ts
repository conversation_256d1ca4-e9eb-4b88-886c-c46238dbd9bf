// 自动生成的inventory模块类型定义
// 基于服务端Schema生成，请勿手动修改

export enum BookMarkType {
  EQUIPMENT = 1,
  CONSUMABLE = 2,
  MATERIAL = 3,
  CARD = 4,
  GIFT = 5,
  CURRENCY = 6,
}

export enum ItemUseType {
  CURRENCY = 1,
  CONSUME = 2,
  GIFT_BAG = 3,
  EQUIPMENT = 4,
}

export enum InventoryType {
  EQUIPMENT = 1,
  CONSUMABLE = 2,
  MATERIAL = 3,
  CARD = 4,
  GIFT = 5,
  CURRENCY = 6,
}

export interface BookMarkConfig {
  id: number;
  name: string;
  type: BookMarkType;
  initCapacity?: number;
  maxCapacity?: number;
  expandStep?: number;
  baseCost?: number;
  costMultiplier?: number;
  isActive?: boolean;
}

export interface ReadonlyBookMarkConfig {
  readonly id: number;
  readonly name: string;
  readonly type: BookMarkType;
  readonly initCapacity?: number;
  readonly maxCapacity?: number;
  readonly expandStep?: number;
  readonly baseCost?: number;
  readonly costMultiplier?: number;
  readonly isActive?: boolean;
}

export interface BookMarkEntry {
  id: number;
  name: string;
  type: BookMarkType;
  capacity?: number;
  usedSlots?: number;
  expandCount?: number;
  nextExpandCost?: number;
  readonly itemUids?: string[];
  createTime?: number;
  updateTime?: number;
}

export interface ReadonlyBookMarkEntry {
  readonly id: number;
  readonly name: string;
  readonly type: BookMarkType;
  readonly capacity?: number;
  readonly usedSlots?: number;
  readonly expandCount?: number;
  readonly nextExpandCost?: number;
  readonly itemUids?: string[];
  readonly createTime?: number;
  readonly updateTime?: number;
}

export interface ItemToBookMarkMapping {
  itemUid: string;
  bookMarkId: number;
}

export interface ReadonlyItemToBookMarkMapping {
  readonly itemUid: string;
  readonly bookMarkId: number;
}

export interface InventoryItem {
  itemId: string;
  configId: number;
  quantity?: number;
  bind?: number;
  acquiredTime?: number;
  slot?: number;
  obtainTime?: number;
}

export interface ReadonlyInventoryItem {
  readonly itemId: string;
  readonly configId: number;
  readonly quantity?: number;
  readonly bind?: number;
  readonly acquiredTime?: number;
  readonly slot?: number;
  readonly obtainTime?: number;
}

export interface Inventory {
  uid: string;
  characterId: string;
  serverId: string;
  readonly bag?: BookMarkEntry[];
  readonly items?: InventoryItem[];
  readonly itemUidToBookMarkId?: ItemToBookMarkMapping[];
  createTime?: number;
  updateTime?: number;
}

export interface ReadonlyInventory {
  readonly uid: string;
  readonly characterId: string;
  readonly serverId: string;
  readonly bag?: BookMarkEntry[];
  readonly items?: InventoryItem[];
  readonly itemUidToBookMarkId?: ItemToBookMarkMapping[];
  readonly createTime?: number;
  readonly updateTime?: number;
}


