// 自动生成的Character模块类型定义
// 基于服务端Schema生成，请勿手动修改

// 游戏进度枚举
export enum CreateRoleStep {
  WATCH_RECORD = 1,
  CREATE_TEAM = 2,
  COMPLETE = 3
}

// 游戏进度接口
export interface GameProgress {
  readonly createRoleStep: number;
  readonly isNewer: boolean;
  readonly isRobot: boolean;
  readonly activeDay: number;
  readonly recentActiveDay: number;
}

// 登录信息接口
export interface LoginInfo {
  readonly loginTime?: number;
  readonly createTime?: number;
  readonly frontendId?: string;
  readonly sessionId?: string;
  readonly lastLoginTime?: number;
  readonly loginCount?: number;
}

// 体力信息接口
export interface EnergyInfo {
  readonly maxEnergy: number;
  readonly energyRecoveryTime?: number;
  readonly lastEnergyTime?: number;
}

// VIP信息接口
export interface VipInfo {
  readonly vip: number;
  readonly vipExp: number;
  readonly vipExpireTime?: number;
  readonly totalRecharge: number;
  readonly monthlyRecharge: number;
}

// 角色主接口（基于服务端Schema）
export interface Character {
  readonly characterId: string;
  readonly userId: string;
  readonly serverId: string;
  readonly openId: string;
  readonly name: string;
  readonly avatar?: string;
  readonly faceIcon?: number;
  readonly faceUrl?: string;
  readonly clubFaceIcon?: readonly number[];
  readonly countryFaceIcon?: readonly number[];
  readonly level: number;
  readonly exp?: number;
  readonly maxExp?: number;
  readonly cash: number;
  readonly gold: number;
  readonly energy: number;
  readonly fame: number;
  readonly allFame: number;
  readonly trophy: number;
  readonly worldCoin: number;
  readonly chip: number;
  readonly integral: number;
  readonly fieldLevel: number;
  readonly qualified: number;
  readonly league: number;
  readonly gameProgress?: GameProgress;
  readonly loginInfo?: LoginInfo;
  readonly energyInfo?: EnergyInfo;
  readonly vipInfo?: VipInfo;
  readonly lastLoginAt?: string;
  readonly createdAt?: string;
  readonly updatedAt?: string;
  
  // 虚拟字段
  readonly currentFormationId?: string;
  readonly heroCount?: number;
  readonly isOnline?: boolean;
}

// 可变版本（用于前端编辑）
export interface MutableCharacter extends Omit<Character, 'clubFaceIcon' | 'countryFaceIcon'> {
  clubFaceIcon?: number[];
  countryFaceIcon?: number[];
}
