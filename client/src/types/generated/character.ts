// 自动生成的character模块类型定义
// 基于服务端Schema生成，请勿手动修改

export enum CreateRoleStep {
  WATCH_RECORD = 1,
  CREATE_TEAM = 2,
  COMPLETE = 3,
}

export interface GameProgress {
  createRoleStep?: number;
  isNewer?: boolean;
  isRobot?: boolean;
  activeDay?: number;
  recentActiveDay?: number;
}

export interface ReadonlyGameProgress {
  readonly createRoleStep?: number;
  readonly isNewer?: boolean;
  readonly isRobot?: boolean;
  readonly activeDay?: number;
  readonly recentActiveDay?: number;
}

export interface LoginInfo {
  loginTime?: number;
  leaveTime?: number;
  createTime?: number;
  ip?: string;
}

export interface ReadonlyLoginInfo {
  readonly loginTime?: number;
  readonly leaveTime?: number;
  readonly createTime?: number;
  readonly ip?: string;
}

export interface EnergyInfo {
  buyEnergyCount?: number;
  buyTime?: number;
  freeTime?: number;
  firstFree?: number;
  secondFree?: number;
  isEnergyFull?: number;
  energyRecoverTime?: number;
}

export interface ReadonlyEnergyInfo {
  readonly buyEnergyCount?: number;
  readonly buyTime?: number;
  readonly freeTime?: number;
  readonly firstFree?: number;
  readonly secondFree?: number;
  readonly isEnergyFull?: number;
  readonly energyRecoverTime?: number;
}

export interface VipInfo {
  vip?: number;
  vipExp?: number;
  readonly isFirstRecharge?: boolean[];
  firstRechargeRefreshTime?: number;
  totalRecharge?: number;
}

export interface ReadonlyVipInfo {
  readonly vip?: number;
  readonly vipExp?: number;
  readonly isFirstRecharge?: boolean[];
  readonly firstRechargeRefreshTime?: number;
  readonly totalRecharge?: number;
}

export interface BeliefInfo {
  beliefId?: number;
  honor?: number;
  beliefNum?: number;
  beliefLiveness?: number;
  lockBelief?: boolean;
  reChangeBeliefTime?: number;
  readonly beliefChangeRecord?: any[];
}

export interface ReadonlyBeliefInfo {
  readonly beliefId?: number;
  readonly honor?: number;
  readonly beliefNum?: number;
  readonly beliefLiveness?: number;
  readonly lockBelief?: boolean;
  readonly reChangeBeliefTime?: number;
  readonly beliefChangeRecord?: any[];
}

export interface Character {
  characterId: string;
  userId: string;
  serverId: string;
  openId: string;
  name: string;
  avatar?: string;
  faceIcon?: number;
  faceUrl?: string;
  readonly clubFaceIcon?: number[];
  readonly countryFaceIcon?: number[];
  level?: number;
  cash?: number;
  gold?: number;
  energy?: number;
  fame?: number;
  allFame?: number;
  trophy?: number;
  worldCoin?: number;
  chip?: number;
  integral?: number;
  fieldLevel?: number;
  qualified?: number;
  league?: number;
  gameProgress?: GameProgress;
  loginInfo?: LoginInfo;
  energyInfo?: EnergyInfo;
  vipInfo?: VipInfo;
  beliefInfo?: BeliefInfo;
  beliefId?: number;
  deemCodeList?: Record<string, string>;
  continuedBuffStartTime?: number;
  continuedBuffEndTime?: number;
  lastBuffUpdateTime?: number;
  alreadyAddEnergyCount?: number;
  isBuyedCoach?: boolean;
  buyBestFootballerNum?: number;
  bestFootballerTime?: number;
  isShowTactics?: number;
  readonly leagueList?: string[];
  readonly exchangeNumList?: number[];
  readonly associationSponsorReward?: any[];
  regTime?: number;
  isFristGetReward?: number;
  formationAct?: number;
}

export interface ReadonlyCharacter {
  readonly characterId: string;
  readonly userId: string;
  readonly serverId: string;
  readonly openId: string;
  readonly name: string;
  readonly avatar?: string;
  readonly faceIcon?: number;
  readonly faceUrl?: string;
  readonly clubFaceIcon?: number[];
  readonly countryFaceIcon?: number[];
  readonly level?: number;
  readonly cash?: number;
  readonly gold?: number;
  readonly energy?: number;
  readonly fame?: number;
  readonly allFame?: number;
  readonly trophy?: number;
  readonly worldCoin?: number;
  readonly chip?: number;
  readonly integral?: number;
  readonly fieldLevel?: number;
  readonly qualified?: number;
  readonly league?: number;
  readonly gameProgress?: GameProgress;
  readonly loginInfo?: LoginInfo;
  readonly energyInfo?: EnergyInfo;
  readonly vipInfo?: VipInfo;
  readonly beliefInfo?: BeliefInfo;
  readonly beliefId?: number;
  readonly deemCodeList?: Record<string, string>;
  readonly continuedBuffStartTime?: number;
  readonly continuedBuffEndTime?: number;
  readonly lastBuffUpdateTime?: number;
  readonly alreadyAddEnergyCount?: number;
  readonly isBuyedCoach?: boolean;
  readonly buyBestFootballerNum?: number;
  readonly bestFootballerTime?: number;
  readonly isShowTactics?: number;
  readonly leagueList?: string[];
  readonly exchangeNumList?: number[];
  readonly associationSponsorReward?: any[];
  readonly regTime?: number;
  readonly isFristGetReward?: number;
  readonly formationAct?: number;
}


