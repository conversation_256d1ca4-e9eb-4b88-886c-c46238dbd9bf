// 自动生成的Match模块类型定义
// 基于服务端Schema生成，请勿手动修改

// 比赛状态枚举
export enum MatchStatus {
  SCHEDULED = 'scheduled',
  PLAYING = 'playing',
  FINISHED = 'finished',
  CANCELLED = 'cancelled'
}

// 比赛类型枚举
export enum MatchType {
  LEAGUE = 'league',
  FRIENDLY = 'friendly',
  TOURNAMENT = 'tournament',
  TRAINING = 'training'
}

// 比赛结果枚举
export enum MatchResult {
  WIN = 'win',
  DRAW = 'draw',
  LOSS = 'loss'
}

// 比赛统计接口
export interface MatchStats {
  readonly homePossession: number;
  readonly awayPossession: number;
  readonly homeShots: number;
  readonly awayShots: number;
  readonly homeShotsOnTarget: number;
  readonly awayShotsOnTarget: number;
  readonly homeCorners: number;
  readonly awayCorners: number;
  readonly homeFouls: number;
  readonly awayFouls: number;
  readonly homeYellowCards?: number;
  readonly awayYellowCards?: number;
  readonly homeRedCards?: number;
  readonly awayRedCards?: number;
}

// 比赛事件接口
export interface MatchEvent {
  readonly id: string;
  readonly type: 'goal' | 'yellow_card' | 'red_card' | 'substitution' | 'penalty';
  readonly minute: number;
  readonly team: 'home' | 'away';
  readonly playerId?: string;
  readonly playerName?: string;
  readonly description: string;
  readonly additionalInfo?: Record<string, any>;
}

// 球队信息接口
export interface MatchTeam {
  readonly id: string;
  readonly name: string;
  readonly logo?: string;
  readonly rating?: number;
  readonly formation?: Formation;
  readonly players?: readonly Hero[];
}

// 比赛主接口（基于服务端Schema）
export interface Match {
  readonly matchId: string;
  readonly homeTeam: MatchTeam;
  readonly awayTeam: MatchTeam;
  readonly homeScore: number;
  readonly awayScore: number;
  readonly status: MatchStatus;
  readonly type: MatchType;
  readonly competition?: string;
  readonly round?: number;
  readonly season?: string;
  readonly startTime?: string;
  readonly endTime?: string;
  readonly currentTime?: number;
  readonly venue?: string;
  readonly weather?: string;
  readonly attendance?: number;
  readonly referee?: string;
  readonly stats: MatchStats;
  readonly events?: readonly MatchEvent[];
  readonly result?: MatchResult;
  readonly isHome?: boolean;
  readonly createdAt?: string;
  readonly updatedAt?: string;
}

// 比赛列表项接口（简化版）
export interface MatchListItem {
  readonly matchId: string;
  readonly homeTeam: string;
  readonly awayTeam: string;
  readonly homeScore: number;
  readonly awayScore: number;
  readonly status: MatchStatus;
  readonly startTime?: string;
  readonly competition?: string;
  readonly result?: MatchResult;
}

// 联赛信息接口
export interface League {
  readonly id: string;
  readonly name: string;
  readonly level: number;
  readonly currentPoints: number;
  readonly requiredPoints: number;
  readonly rewards: readonly LeagueReward[];
  readonly season?: string;
  readonly startDate?: string;
  readonly endDate?: string;
}

// 联赛奖励接口
export interface LeagueReward {
  readonly type: string;
  readonly amount: number;
  readonly icon?: string;
  readonly name?: string;
}

// 可变版本（用于前端编辑）
export interface MutableMatch extends Omit<Match, 'events'> {
  events?: MatchEvent[];
}
