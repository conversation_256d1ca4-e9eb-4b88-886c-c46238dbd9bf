// 自动生成的hero模块类型定义
// 基于服务端Schema生成，请勿手动修改

export interface HeroAttributes {
  speed?: number;
  shooting?: number;
  passing?: number;
  defending?: number;
  dribbling?: number;
  physicality?: number;
  goalkeeping?: number;
}

export interface ReadonlyHeroAttributes {
  readonly speed?: number;
  readonly shooting?: number;
  readonly passing?: number;
  readonly defending?: number;
  readonly dribbling?: number;
  readonly physicality?: number;
  readonly goalkeeping?: number;
}

export interface HeroSkill {
  skillId: number;
  level?: number;
  isActive?: boolean;
}

export interface ReadonlyHeroSkill {
  readonly skillId: number;
  readonly level?: number;
  readonly isActive?: boolean;
}

export interface TrainingStageInfo {
  stage?: number;
  stageProgress?: number;
  totalTrainingCount?: number;
}

export interface ReadonlyTrainingStageInfo {
  readonly stage?: number;
  readonly stageProgress?: number;
  readonly totalTrainingCount?: number;
}

export interface HeroTraining {
  exp?: number;
  trainingCount?: number;
  lastTrainingTime?: number;
  trainingCooldown?: number;
  speedStage?: TrainingStageInfo;
  shootingStage?: TrainingStageInfo;
  passingStage?: TrainingStageInfo;
  defendingStage?: TrainingStageInfo;
  dribblingStage?: TrainingStageInfo;
  physicalityStage?: TrainingStageInfo;
  additive?: any;
  type1?: any;
  type2?: any;
  type3?: any;
  type4?: any;
}

export interface ReadonlyHeroTraining {
  readonly exp?: number;
  readonly trainingCount?: number;
  readonly lastTrainingTime?: number;
  readonly trainingCooldown?: number;
  readonly speedStage?: TrainingStageInfo;
  readonly shootingStage?: TrainingStageInfo;
  readonly passingStage?: TrainingStageInfo;
  readonly defendingStage?: TrainingStageInfo;
  readonly dribblingStage?: TrainingStageInfo;
  readonly physicalityStage?: TrainingStageInfo;
  readonly additive?: any;
  readonly type1?: any;
  readonly type2?: any;
  readonly type3?: any;
  readonly type4?: any;
}

export interface HeroEvolution {
  star?: number;
  evolutionExp?: number;
  evolutionCount?: number;
  readonly consumedHeroes?: string[];
  lastEvolutionTime?: number;
  isMaxStar?: boolean;
}

export interface ReadonlyHeroEvolution {
  readonly star?: number;
  readonly evolutionExp?: number;
  readonly evolutionCount?: number;
  readonly consumedHeroes?: string[];
  readonly lastEvolutionTime?: number;
  readonly isMaxStar?: boolean;
}

export interface Hero {
  heroId: string;
  characterId: string;
  serverId: string;
  resId: number;
  name: string;
  position: HeroPosition;
  quality: HeroQuality;
  level?: number;
  exp?: number;
  experience?: number;
  avatar?: string;
  faceIcon?: number;
  nationality?: string;
  club?: string;
  attributes?: HeroAttributes;
  baseAttributes?: HeroAttributes;
  readonly skills?: HeroSkill[];
  readonly activeSkills?: number[];
  training?: HeroTraining;
  evolution?: HeroEvolution;
  readonly breakthrough?: number[];
  oldBreakOut?: number;
  isLocked?: boolean;
  isInFormation?: boolean;
  formationPosition?: number;
  energy?: number;
  morale?: number;
  isTreat?: boolean;
  isTrain?: boolean;
  isLockTrain?: boolean;
  fatigue?: number;
  fatigueRatio?: number;
  reTimeFatigue?: number;
  readonly equipments?: string[];
  contractDays?: number;
  treatyReTime?: number;
  contractExpireTime?: Date;
  contractRenewals?: number;
  lastRenewTime?: Date;
  salary?: number;
  lifeNum?: number;
  isUseCareerMedicine?: boolean;
  battleNum?: number;
  marketValue?: number;
  isOnMarket?: boolean;
  marketPrice?: number;
  isRetired?: boolean;
  retirementTime?: number;
  retirementStatus?: string;
  retirementProcessedTime?: Date;
  breakLevel?: number;
  starLevel?: number;
  evolutionStage?: number;
  readonly oneLevelAttr?: number[];
  readonly isStage?: number[];
  status?: {
    morale?: number;
  lastStatusUpdateTime?: Date;
  careerDays?: number;
  statusPromotions?: number;
  levelUps?: number;
  skillUpgrades?: number;
  breakthroughs?: number;
  isTraining?: boolean;
  trainingType?: string;
  trainingStartTime?: Date;
  trainingEndTime?: Date;
  lastGroundTrainingTime?: Date;
  TrainCount?: number;
  Health?: number;
  matchesPlayed?: number;
  goals?: number;
  assists?: number;
  yellowCards?: number;
  redCards?: number;
  averageRating?: number;
  totalPower?: number;
  lastCalculateTime?: Date;
  obtainTime?: number;
  obtainType?: number;
}

export interface ReadonlyHero {
  readonly heroId: string;
  readonly characterId: string;
  readonly serverId: string;
  readonly resId: number;
  readonly name: string;
  readonly position: HeroPosition;
  readonly quality: HeroQuality;
  readonly level?: number;
  readonly exp?: number;
  readonly experience?: number;
  readonly avatar?: string;
  readonly faceIcon?: number;
  readonly nationality?: string;
  readonly club?: string;
  readonly attributes?: HeroAttributes;
  readonly baseAttributes?: HeroAttributes;
  readonly skills?: HeroSkill[];
  readonly activeSkills?: number[];
  readonly training?: HeroTraining;
  readonly evolution?: HeroEvolution;
  readonly breakthrough?: number[];
  readonly oldBreakOut?: number;
  readonly isLocked?: boolean;
  readonly isInFormation?: boolean;
  readonly formationPosition?: number;
  readonly energy?: number;
  readonly morale?: number;
  readonly isTreat?: boolean;
  readonly isTrain?: boolean;
  readonly isLockTrain?: boolean;
  readonly fatigue?: number;
  readonly fatigueRatio?: number;
  readonly reTimeFatigue?: number;
  readonly equipments?: string[];
  readonly contractDays?: number;
  readonly treatyReTime?: number;
  readonly contractExpireTime?: Date;
  readonly contractRenewals?: number;
  readonly lastRenewTime?: Date;
  readonly salary?: number;
  readonly lifeNum?: number;
  readonly isUseCareerMedicine?: boolean;
  readonly battleNum?: number;
  readonly marketValue?: number;
  readonly isOnMarket?: boolean;
  readonly marketPrice?: number;
  readonly isRetired?: boolean;
  readonly retirementTime?: number;
  readonly retirementStatus?: string;
  readonly retirementProcessedTime?: Date;
  readonly breakLevel?: number;
  readonly starLevel?: number;
  readonly evolutionStage?: number;
  readonly oneLevelAttr?: number[];
  readonly isStage?: number[];
  readonly status?: {
    morale?: number;
  readonly lastStatusUpdateTime?: Date;
  readonly careerDays?: number;
  readonly statusPromotions?: number;
  readonly levelUps?: number;
  readonly skillUpgrades?: number;
  readonly breakthroughs?: number;
  readonly isTraining?: boolean;
  readonly trainingType?: string;
  readonly trainingStartTime?: Date;
  readonly trainingEndTime?: Date;
  readonly lastGroundTrainingTime?: Date;
  readonly TrainCount?: number;
  readonly Health?: number;
  readonly matchesPlayed?: number;
  readonly goals?: number;
  readonly assists?: number;
  readonly yellowCards?: number;
  readonly redCards?: number;
  readonly averageRating?: number;
  readonly totalPower?: number;
  readonly lastCalculateTime?: Date;
  readonly obtainTime?: number;
  readonly obtainType?: number;
}


