// 自动生成的Hero模块类型定义
// 基于服务端Schema生成，请勿手动修改

// 英雄位置枚举
export enum HeroPosition {
  GK = 'GK',
  DEF = 'DEF', 
  MID = 'MID',
  ATT = 'ATT'
}

// 英雄品质枚举
export enum HeroQuality {
  COMMON = 1,
  UNCOMMON = 2,
  RARE = 3,
  EPIC = 4,
  LEGENDARY = 5
}

// 英雄状态枚举
export enum HeroStatus {
  NORMAL = 'normal',
  TRAINING = 'training',
  INJURED = 'injured',
  SUSPENDED = 'suspended',
  RETIRED = 'retired'
}

// 英雄属性接口
export interface HeroAttributes {
  readonly pace: number;
  readonly shooting: number;
  readonly passing: number;
  readonly dribbling: number;
  readonly defending: number;
  readonly physical: number;
  readonly goalkeeping?: number;
}

// 英雄技能接口
export interface HeroSkill {
  readonly id: string;
  readonly name: string;
  readonly level: number;
  readonly maxLevel: number;
  readonly description: string;
  readonly configId: number;
}

// 英雄训练信息接口
export interface HeroTraining {
  readonly trainingCooldown?: number;
  readonly lastTrainingTime?: number;
  readonly trainingCount?: number;
  readonly type1?: any;
  readonly type2?: any;
  readonly type3?: any;
  readonly type4?: any;
}

// 英雄升星信息接口
export interface HeroEvolution {
  readonly star: number;
  readonly evolutionExp: number;
  readonly evolutionCount: number;
  readonly consumedHeroes: readonly string[];
  readonly lastEvolutionTime: number;
  readonly isMaxStar: boolean;
}

// 英雄主接口（基于服务端Schema）
export interface Hero {
  readonly heroId: string;
  readonly characterId: string;
  readonly serverId: string;
  readonly resId: number;
  readonly name: string;
  readonly position: HeroPosition;
  readonly quality: HeroQuality;
  readonly level: number;
  readonly exp: number;
  readonly experience: number;
  readonly avatar?: string;
  readonly faceIcon?: number;
  readonly nationality?: string;
  readonly club?: string;
  readonly attributes: HeroAttributes;
  readonly baseAttributes?: HeroAttributes;
  readonly skills?: readonly HeroSkill[];
  readonly activeSkills?: readonly number[];
  readonly training?: HeroTraining;
  readonly evolution?: HeroEvolution;
  readonly breakthrough?: readonly number[];
  readonly oldBreakOut?: number;
  readonly isLocked?: boolean;
  readonly isInFormation?: boolean;
  readonly isOnMarket?: boolean;
  readonly marketPrice?: number;
  readonly obtainTime?: number;
  readonly status?: HeroStatus;
  readonly rarity?: string;
  
  // 计算属性
  readonly overallRating?: number;
  readonly potential?: number;
  readonly age?: number;
}

// 可变版本（用于前端编辑）
export interface MutableHero extends Omit<Hero, 'skills' | 'activeSkills' | 'breakthrough'> {
  skills?: HeroSkill[];
  activeSkills?: number[];
  breakthrough?: number[];
}
