// 全局类型定义

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

// WebSocket消息类型 - 基于服务端真实接口
export interface WSMessage<T = any> {
  id: string
  command: string // 格式: 'service.action' 如 'hero.getList'
  payload: T
  timestamp?: number
}

export interface WSResponse<T = any> {
  event: string
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  executionTime?: number
}

// 服务端标准响应格式
export interface ServiceResponse<T = any> {
  code: number
  message: string
  data?: T
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
}

// 服务器相关类型
export interface ServerInfo {
  id: string
  name: string
  description: string
  status: 'online' | 'offline' | 'maintenance'
  playerCount: number
  maxPlayers: number
  region: string
  isRecommended?: boolean
}

// 角色相关类型
export interface Character {
  id: string
  name: string
  level: number
  experience: number
  serverId: string
  userId: string
  createdAt: string
  lastLoginAt: string
}

// 英雄相关类型 - 基于服务端真实数据结构
export interface Hero {
  heroId: string
  characterId: string
  configId: number
  name: string
  position: HeroPosition
  overall: number
  potential?: number
  age?: number
  level: number
  nationality?: string
  attributes: HeroAttributes
  skills?: HeroSkill[]
  status: HeroStatus
  contractEndDate?: string
  marketValue?: number
  quality: number // 品质等级 1-5
  rarity?: string // 稀有度
  isInFormation?: boolean // 是否在阵容中
  isLocked?: boolean // 是否锁定
  training?: HeroTraining // 训练相关数据
  obtainTime?: number // 获得时间
  // 服务端字段
  star?: number // 星级
  exp?: number // 经验值
  maxExp?: number // 最大经验值
  breakthrough?: number // 突破等级
  careerDays?: number // 生涯天数
}

// 球员位置枚举
export enum HeroPosition {
  GK = 'GK',   // 门将
  DEF = 'DEF', // 后卫
  MID = 'MID', // 中场
  ATT = 'ATT'  // 前锋
}

// 球员状态枚举
export enum HeroStatus {
  ACTIVE = 'active',
  INJURED = 'injured',
  SUSPENDED = 'suspended',
  RETIRED = 'retired'
}

export interface HeroAttributes {
  pace: number
  shooting: number
  passing: number
  dribbling: number
  defending: number
  physical: number
}

export interface HeroSkill {
  id: string
  name: string
  level: number
  maxLevel: number
  description: string
  configId: number
}

export interface HeroTraining {
  trainingCount: number
  lastTrainingTime: number
  trainingCooldown?: number
}

// 训练相关类型 - 基于服务端真实接口
export interface TrainHeroDto {
  heroId: string
  trainType: number
  trainCount: number
  useItems?: string[]
}

export interface TrainResultDto {
  success: boolean
  trainCount: number
  trainingTime: number
  attributeChanges: Record<string, {
    oldValue: number
    newValue: number
    change: number
  }>
  expGained: number
  message: string
}

// 角色相关类型 - 基于服务端Character服务
export interface Character {
  characterId: string
  userId: string
  name: string
  level: number
  exp: number
  maxExp: number
  serverId: string
  serverName?: string
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  // 游戏数据
  coins?: number
  gems?: number
  energy?: number
  maxEnergy?: number
}

export interface CreateCharacterDto {
  name: string
  serverId: string
}

export interface CharacterListDto {
  serverId?: string
}

// 服务器相关类型 - 基于服务端真实数据
export interface ServerInfo {
  serverId: string
  name: string
  region: string
  status: 'online' | 'offline' | 'maintenance'
  playerCount: number
  maxPlayers: number
  description?: string
  isRecommended?: boolean
  openTime?: string
}

// 战术相关类型
export interface Formation {
  id: string
  name: string
  formation: string // 如 "4-4-2"
  positions: FormationPosition[]
  tactics: TacticSettings
  isActive: boolean
}

export interface FormationPosition {
  position: string
  x: number
  y: number
  heroId?: string
}

export interface TacticSettings {
  attackingStyle: 'possession' | 'counter' | 'direct'
  defendingStyle: 'high_press' | 'mid_block' | 'low_block'
  tempo: number // 1-10
  width: number // 1-10
  mentality: 'defensive' | 'balanced' | 'attacking'
}

// 比赛相关类型
export interface Match {
  id: string
  homeTeam: string
  awayTeam: string
  homeScore: number
  awayScore: number
  status: 'scheduled' | 'live' | 'finished' | 'paused' | 'halftime'
  startTime: string
  competition: string
  events: MatchEvent[]
  currentTime?: number
  homeFormation?: string
  awayFormation?: string
  result?: MatchResult
  stats?: MatchStats
}

export interface MatchEvent {
  id: string
  matchId: string
  type: 'goal' | 'card' | 'redcard' | 'substitution' | 'penalty' | 'corner' | 'offside' | 'foul' | 'shot' | 'shotOnTarget'
  minute: number
  player: string
  team: 'home' | 'away'
  description: string
}

export interface MatchResult {
  matchId: string
  isWin: boolean
  isDraw: boolean
  goalsFor: number
  goalsAgainst: number
  rewards: MatchReward[]
  experience: number
  message: string
}

export interface MatchReward {
  type: 'coins' | 'gems' | 'exp' | 'item'
  amount: number
  itemId?: string
}

export interface MatchStats {
  possession?: { home: number; away: number }
  shots?: { home: number; away: number }
  shotsOnTarget?: { home: number; away: number }
  corners?: { home: number; away: number }
  fouls?: { home: number; away: number }
}

// 联赛相关类型
export interface League {
  id: string
  name: string
  level: number
  currentPoints: number
  requiredPoints: number
  rewards: LeagueReward[]
}

export interface LeagueReward {
  id: string
  name: string
  icon: string
  amount: number
}

// 锦标赛相关类型
export interface Tournament {
  id: string
  name: string
  status: 'registration' | 'ongoing' | 'finished'
  entryCost: number
  prizePool: number
  participants: number
  maxParticipants: number
  startTime: number
  isParticipant: boolean
}

// 游戏状态类型
export interface GameState {
  currentScene: string
  isInMatch: boolean
  notifications: Notification[]
  quickActions: QuickAction[]
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
}

export interface QuickAction {
  id: string
  label: string
  icon?: string
  command: string
  hotkey?: string
  disabled?: boolean
}

// 表格相关类型
export interface TableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sorter?: boolean
  filters?: Array<{ text: string; value: any }>
  render?: (value: any, record: any, index: number) => any
}

// 分页类型
export interface Pagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: (total: number, range: [number, number]) => string
}

// 路由元信息类型 - 添加索引签名以兼容vue-router
export interface RouteMeta extends Record<string, any> {
  title?: string
  requiresAuth?: boolean
  requiresCharacter?: boolean
  layout?: 'default' | 'game' | 'auth'
  keepAlive?: boolean
}

// 环境配置类型
export interface AppConfig {
  apiBaseUrl: string
  wsUrl: string
  version: string
  environment: 'development' | 'production' | 'test'
}

// 游戏配置类型
export interface GameConfig {
  maxHeroesPerTeam: number
  maxFormations: number
  matchDuration: number
  currencies: Currency[]
}

export interface Currency {
  id: string
  name: string
  symbol: string
  icon?: string
}

// 背包道具相关类型
export interface InventoryItem {
  itemId: string
  configId: number
  name: string
  type: string
  rarity: string
  quantity: number
  bind: boolean
  obtainTime: number
  slot: number
  cooldown?: number
  lastUseTime?: number
}

export interface InventoryTab {
  bookMarkId: string
  name: string
  type: number
  capacity: number
  usedSlots: number
  items: (InventoryItem | null)[]
}

export interface UseItemResult {
  success: boolean
  effects: ItemEffect[]
  message: string
  cooldown?: number
}

export interface ItemEffect {
  type: string
  value: number
  target?: string
  duration?: number
}

// 每日任务类型
export interface DailyTask {
  id: string
  name: string
  current: number
  target: number
  completed: boolean
  reward: {
    icon: string
    amount: number
  }
}

// 游戏通知类型
export interface GameNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: string
  read: boolean
}

// 表格相关类型
export interface TableColumn {
  title: string
  key: string
  dataIndex?: string
  width?: number
  fixed?: 'left' | 'right'
  sorter?: boolean
  render?: (value: any, record: any) => any
}

export interface Pagination {
  current: number
  pageSize: number
  total: number | (() => number)
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: (total: number, range: [number, number]) => string
}

// 阵型相关类型
export interface Formation {
  id: string
  name: string
  description?: string
  positions: FormationPosition[]
}

export interface FormationPosition {
  id: string
  position: string
  x: number
  y: number
  heroId: string | null
}

// 训练相关类型
export interface TrainingType {
  id: string
  name: string
  description: string
  icon: string
  coinCost: number
  energyCost: number
  effects: string[]
}

export interface TrainingResult {
  heroId: string
  heroName: string
  trainingTypeName: string
  attributeChanges: Record<string, {
    oldValue: number
    newValue: number
    change: number
  }>
  trainingTime: number
}

// 市场相关类型
export interface MarketListing {
  id: string
  heroId: string
  heroName: string
  position: string
  level: number
  overall: number
  price: number
  originalPrice: number
  sellerName: string
  sellerRating: number
  endTime: number
  isMyListing: boolean
}

// 公会相关类型
export interface Guild {
  id: string
  name: string
  level: number
  exp: number
  memberCount: number
  maxMembers: number
  ranking: number
  description?: string
}

export interface GuildMember {
  id: string
  name: string
  role: string
  contribution: number
  lastOnline: string
  isOnline: boolean
}

export interface GuildActivity {
  id: string
  name: string
  description: string
  status: string
  participants: number
  maxParticipants: number
  startTime: number
}
