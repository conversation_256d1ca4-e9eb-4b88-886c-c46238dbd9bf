// 全局类型定义

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

// WebSocket消息类型
export interface WSMessage {
  id: string
  command: string
  payload: any
  timestamp?: number
}

export interface WSResponse {
  id: string
  success: boolean
  data?: any
  error?: string
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
}

// 服务器相关类型
export interface ServerInfo {
  id: string
  name: string
  description: string
  status: 'online' | 'offline' | 'maintenance'
  playerCount: number
  maxPlayers: number
  region: string
  isRecommended?: boolean
}

// 角色相关类型
export interface Character {
  id: string
  name: string
  level: number
  experience: number
  serverId: string
  userId: string
  createdAt: string
  lastLoginAt: string
}

// 英雄相关类型
export interface Hero {
  id: string
  name: string
  position: string
  overall: number
  potential: number
  age: number
  nationality: string
  attributes: HeroAttributes
  skills: HeroSkill[]
  status: 'active' | 'injured' | 'suspended'
  contractEndDate: string
  marketValue: number
}

export interface HeroAttributes {
  pace: number
  shooting: number
  passing: number
  dribbling: number
  defending: number
  physical: number
}

export interface HeroSkill {
  id: string
  name: string
  level: number
  maxLevel: number
  description: string
}

// 战术相关类型
export interface Formation {
  id: string
  name: string
  formation: string // 如 "4-4-2"
  positions: FormationPosition[]
  tactics: TacticSettings
  isActive: boolean
}

export interface FormationPosition {
  position: string
  x: number
  y: number
  heroId?: string
}

export interface TacticSettings {
  attackingStyle: 'possession' | 'counter' | 'direct'
  defendingStyle: 'high_press' | 'mid_block' | 'low_block'
  tempo: number // 1-10
  width: number // 1-10
  mentality: 'defensive' | 'balanced' | 'attacking'
}

// 比赛相关类型
export interface Match {
  id: string
  homeTeam: string
  awayTeam: string
  homeScore: number
  awayScore: number
  status: 'scheduled' | 'live' | 'finished'
  startTime: string
  competition: string
  events: MatchEvent[]
}

export interface MatchEvent {
  id: string
  type: 'goal' | 'card' | 'substitution' | 'penalty'
  minute: number
  player: string
  team: 'home' | 'away'
  description: string
}

// 游戏状态类型
export interface GameState {
  currentScene: string
  isInMatch: boolean
  notifications: Notification[]
  quickActions: QuickAction[]
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
}

export interface QuickAction {
  id: string
  label: string
  icon?: string
  command: string
  hotkey?: string
  disabled?: boolean
}

// 表格相关类型
export interface TableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sorter?: boolean
  filters?: Array<{ text: string; value: any }>
  render?: (value: any, record: any, index: number) => any
}

// 分页类型
export interface Pagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: (total: number, range: [number, number]) => string
}

// 路由元信息类型
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  requiresCharacter?: boolean
  layout?: 'default' | 'game' | 'auth'
  keepAlive?: boolean
}

// 环境配置类型
export interface AppConfig {
  apiBaseUrl: string
  wsUrl: string
  version: string
  environment: 'development' | 'production' | 'test'
}

// 游戏配置类型
export interface GameConfig {
  maxHeroesPerTeam: number
  maxFormations: number
  matchDuration: number
  currencies: Currency[]
}

export interface Currency {
  id: string
  name: string
  symbol: string
  icon?: string
}
