// 全局类型定义

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

// WebSocket消息类型 - 基于服务端真实接口
export interface WSMessage<T = any> {
  id: string
  command: string // 格式: 'service.action' 如 'hero.getList'
  payload: T
  timestamp?: number
}

export interface WSResponse<T = any> {
  event: string
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  executionTime?: number
}

// 服务端标准响应格式
export interface ServiceResponse<T = any> {
  code: number
  message: string
  data?: T
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
}

// 这些类型定义已在下方重新定义，删除重复定义

// 英雄相关类型 - 基于服务端Hero Schema
export interface Hero {
  // 基础标识信息
  heroId: string
  characterId: string
  serverId: string
  resId: number // 配置表ID

  // 基础信息
  name: string
  position: HeroPosition
  quality: number // 品质等级 1-5
  level: number
  exp: number
  experience: number // 训练系统使用的经验值

  // 外观信息
  avatar?: string
  faceIcon?: number
  nationality?: string
  club?: string

  // 属性信息
  attributes: HeroAttributes
  baseAttributes?: HeroAttributes // 基础属性（不含装备加成）

  // 技能信息
  skills?: HeroSkill[]
  activeSkills?: number[] // 激活的技能ID列表

  // 训练信息
  training?: HeroTraining

  // 升星信息
  evolution?: {
    star: number // 星级 (0-9)
    evolutionExp: number // 升星经验
    evolutionCount: number // 升星次数
    isMaxStar: boolean // 是否已达到最高星级
  }

  // 突破系统
  breakthrough?: number[]
  oldBreakOut?: number // 突破最大值

  // 状态信息
  isLocked?: boolean
  isInFormation?: boolean
  status?: HeroStatus

  // 计算属性
  overallRating?: number // 总评分
  marketValue?: number // 市场价值
  rarity?: string // 稀有度（客户端计算）
}

// 球员位置枚举
export enum HeroPosition {
  GK = 'GK',   // 门将
  DEF = 'DEF', // 后卫
  MID = 'MID', // 中场
  ATT = 'ATT'  // 前锋
}

// 球员状态枚举
export enum HeroStatus {
  ACTIVE = 'active',
  INJURED = 'injured',
  SUSPENDED = 'suspended',
  RETIRED = 'retired'
}

export interface HeroAttributes {
  pace: number
  shooting: number
  passing: number
  dribbling: number
  defending: number
  physical: number
}

export interface HeroSkill {
  id: string
  name: string
  level: number
  maxLevel: number
  description: string
  configId: number
}

export interface HeroTraining {
  trainingCount: number
  lastTrainingTime: number
  trainingCooldown?: number
}

// 训练相关类型 - 基于服务端真实接口
export interface TrainHeroDto {
  heroId: string
  trainType: number
  trainCount: number
  useItems?: string[]
}

export interface TrainResultDto {
  success: boolean
  trainCount: number
  trainingTime: number
  attributeChanges: Record<string, {
    oldValue: number
    newValue: number
    change: number
  }>
  expGained: number
  message: string
}

export interface CreateHeroDto {
  name: string
  position: string
  configId?: number
}

// 角色相关类型 - 基于服务端Character Schema
export interface Character {
  // 基础标识信息
  characterId: string
  userId: string
  serverId: string
  openId: string
  name: string

  // 外观信息
  avatar?: string
  faceIcon?: number
  faceUrl?: string
  clubFaceIcon?: number[]
  countryFaceIcon?: number[]

  // 等级和经验
  level: number

  // 货币资源
  cash: number // 金币
  gold: number // 钻石
  energy: number // 体力

  // 声望和奖杯
  fame: number
  allFame: number
  trophy: number

  // 其他货币
  worldCoin: number
  chip: number
  integral: number

  // 球场信息
  fieldLevel: number
  qualified: number

  // 联赛信息
  league: number

  // 嵌套文档
  gameProgress?: {
    createRoleStep: number
    isNewer: boolean
    isRobot: boolean
    activeDay: number
    recentActiveDay: number
  }

  loginInfo?: {
    loginTime: number
    createTime: number
    frontendId?: string
    sessionId?: string
  }

  energyInfo?: {
    lastEnergyTime: number
    energyBuyTimes: number
  }

  vipInfo?: {
    vip: number
    vipExp: number
    totalRecharge: number
  }

  // 虚拟字段
  currentFormationId?: string
  heroCount?: number
  isOnline?: boolean

  // 时间戳
  createdAt?: string
  updatedAt?: string
}

export interface CreateCharacterDto {
  name: string
  serverId: string
}

export interface CharacterListDto {
  serverId?: string
}

// 阵型相关类型 - 基于服务端Formation Schema
export interface Formation {
  // 基础信息
  uid: string
  characterId: string
  serverId: string

  // 当前阵容信息
  currTeamFormationId: string

  // 阵容列表
  teamFormations: TeamFormation[]

  // 时间戳
  createdAt?: string
  updatedAt?: string
}

export interface TeamFormation {
  uid: string // 阵容UID
  resId: number // 配置阵型Id
  name: string // 阵容名称

  // 团队基础属性
  attack: number // 球队进攻值
  defend: number // 球队防守值
  actualStrength: number // 球队的实力

  // 阵容配置
  isInitName: number // 是否是初始名字 0是 1不是
  isInitFormation: number // 是否初始阵容一 0不是 1是
  isLeague: number // 是否为联赛专用阵容 0为不是 1是为
  teamId: number // 阵容Id 1,2,3,4 阵容1,2,3,4
  teamType: number // 小队类型 1主力队 2替补队 3预备队

  // 球员位置映射
  positionToHerosObject: {
    GK: string[] // 门将
    DL: string[] // 左后卫
    DC: string[] // 中后卫
    DR: string[] // 右后卫
    ML: string[] // 左中场
    MC: string[] // 中中场
    MR: string[] // 右中场
    WL: string[] // 左边锋
    ST: string[] // 前锋
    WR: string[] // 右边锋
    AM: string[] // 前腰
    DM: string[] // 后腰
  }

  // 特殊球员指定
  freeKickHero: string // 任意球球员
  penaltiesHero: string // 点球球员
  cornerKickHero: string // 角球球员

  // 战术配置
  useTactics: number // 当前阵容使用的战术
  useDefTactics: number // 当前阵容使用防守的战术

  // 其他配置
  scenceUse: string[] // 阵型使用场景标记
  inspireRate: number // 鼓舞加成比例
  type: number // 阵容类型(用途)
}

// 阵型位置类型（客户端显示用）
export interface FormationPosition {
  id: string
  position: string
  x: number // 位置X坐标（百分比）
  y: number // 位置Y坐标（百分比）
  role: string // 位置角色（goalkeeper, defender, midfielder, forward）
  heroId?: string | null // 分配的球员ID
}

// 服务器相关类型 - 基于服务端真实数据
export interface ServerInfo {
  serverId: string
  name: string
  region: string
  status: 'online' | 'offline' | 'maintenance'
  playerCount: number
  maxPlayers: number
  description?: string
  isRecommended?: boolean
  openTime?: string
}

// 战术相关类型
export interface Formation {
  id: string
  name: string
  formation: string // 如 "4-4-2"
  positions: FormationPosition[]
  tactics: TacticSettings
  isActive: boolean
}

// 重复定义已删除，使用上方的FormationPosition定义

export interface TacticSettings {
  attackingStyle: 'possession' | 'counter' | 'direct'
  defendingStyle: 'high_press' | 'mid_block' | 'low_block'
  tempo: number // 1-10
  width: number // 1-10
  mentality: 'defensive' | 'balanced' | 'attacking'
}

// 比赛相关类型
export interface Match {
  id: string
  homeTeam: string
  awayTeam: string
  homeScore: number
  awayScore: number
  status: 'scheduled' | 'live' | 'finished' | 'paused' | 'halftime'
  startTime: string
  competition: string
  events: MatchEvent[]
  currentTime?: number
  homeFormation?: string
  awayFormation?: string
  result?: MatchResult
  stats?: MatchStats
}

export interface MatchEvent {
  id: string
  matchId: string
  type: 'goal' | 'card' | 'redcard' | 'substitution' | 'penalty' | 'corner' | 'offside' | 'foul' | 'shot' | 'shotOnTarget'
  minute: number
  player: string
  team: 'home' | 'away'
  description: string
}

export interface MatchResult {
  matchId: string
  isWin: boolean
  isDraw: boolean
  goalsFor: number
  goalsAgainst: number
  rewards: MatchReward[]
  experience: number
  message: string
}

export interface MatchReward {
  type: 'coins' | 'gems' | 'exp' | 'item'
  amount: number
  itemId?: string
}

export interface MatchStats {
  possession?: { home: number; away: number }
  shots?: { home: number; away: number }
  shotsOnTarget?: { home: number; away: number }
  corners?: { home: number; away: number }
  fouls?: { home: number; away: number }
}

// 联赛相关类型
export interface League {
  id: string
  name: string
  level: number
  currentPoints: number
  requiredPoints: number
  rewards: LeagueReward[]
}

export interface LeagueReward {
  id: string
  name: string
  icon: string
  amount: number
}

// 锦标赛相关类型
export interface Tournament {
  id: string
  name: string
  status: 'registration' | 'ongoing' | 'finished'
  entryCost: number
  prizePool: number
  participants: number
  maxParticipants: number
  startTime: number
  isParticipant: boolean
}

// 游戏状态类型
export interface GameState {
  currentScene: string
  isInMatch: boolean
  notifications: Notification[]
  quickActions: QuickAction[]
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
}

export interface QuickAction {
  id: string
  label: string
  icon?: string
  command: string
  hotkey?: string
  disabled?: boolean
}

// 表格相关类型
export interface TableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sorter?: boolean
  filters?: Array<{ text: string; value: any }>
  render?: (value: any, record: any, index: number) => any
}

// 分页类型
export interface Pagination {
  current: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: (total: number, range: [number, number]) => string
}

// 路由元信息类型 - 添加索引签名以兼容vue-router
export interface RouteMeta extends Record<PropertyKey, unknown> {
  title?: string
  requiresAuth?: boolean
  requiresCharacter?: boolean
  layout?: 'default' | 'game' | 'auth'
  keepAlive?: boolean
}

// 环境配置类型
export interface AppConfig {
  apiBaseUrl: string
  wsUrl: string
  version: string
  environment: 'development' | 'production' | 'test'
}

// 游戏配置类型
export interface GameConfig {
  maxHeroesPerTeam: number
  maxFormations: number
  matchDuration: number
  currencies: Currency[]
}

export interface Currency {
  id: string
  name: string
  symbol: string
  icon?: string
}

// 背包道具相关类型
export interface InventoryItem {
  itemId: string
  configId: number
  name: string
  type: string
  rarity: string
  quantity: number
  bind: boolean
  obtainTime: number
  slot: number
  cooldown?: number
  lastUseTime?: number
}

export interface InventoryTab {
  bookMarkId: string
  name: string
  type: number
  capacity: number
  usedSlots: number
  items: (InventoryItem | null)[]
}

export interface UseItemResult {
  success: boolean
  effects: ItemEffect[]
  message: string
  cooldown?: number
}

export interface ItemEffect {
  type: string
  value: number
  target?: string
  duration?: number
}

// 每日任务类型
export interface DailyTask {
  id: string
  name: string
  current: number
  target: number
  completed: boolean
  reward: {
    icon: string
    amount: number
  }
}

// 游戏通知类型
export interface GameNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: string
  read: boolean
}

// 重复定义已删除，使用上方的TableColumn和Pagination定义

// 重复定义已删除，使用上方的FormationPosition定义

// 重复定义已删除，使用上方的TrainResultDto定义

// 市场相关类型
export interface MarketListing {
  id: string
  heroId: string
  heroName: string
  position: string
  level: number
  overall: number
  price: number
  originalPrice: number
  sellerName: string
  sellerRating: number
  endTime: number
  isMyListing: boolean
}

// 公会相关类型
export interface Guild {
  id: string
  name: string
  level: number
  exp: number
  memberCount: number
  maxMembers: number
  ranking: number
  description?: string
}

export interface GuildMember {
  id: string
  name: string
  role: string
  contribution: number
  lastOnline: string
  isOnline: boolean
}

export interface GuildActivity {
  id: string
  name: string
  description: string
  status: string
  participants: number
  maxParticipants: number
  startTime: number
}
