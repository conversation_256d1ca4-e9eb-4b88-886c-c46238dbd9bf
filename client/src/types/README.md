# 类型定义重构方案

## 目标
1. 按模块拆分类型定义，提高可维护性
2. 确保前后端数据结构一致性
3. 建立类型定义的最佳实践

## 新的目录结构
```
client/src/types/
├── README.md                 # 类型定义说明
├── index.ts                  # 统一导出入口
├── common/                   # 通用类型
│   ├── base.ts              # 基础类型（ID、时间戳等）
│   ├── api.ts               # API响应类型
│   └── ui.ts                # UI组件类型
├── game/                     # 游戏业务类型
│   ├── hero.ts              # 英雄相关类型
│   ├── formation.ts         # 阵型相关类型
│   ├── match.ts             # 比赛相关类型
│   ├── training.ts          # 训练相关类型
│   ├── market.ts            # 市场相关类型
│   └── inventory.ts         # 背包相关类型
├── auth/                     # 认证相关类型
│   ├── user.ts              # 用户类型
│   ├── character.ts         # 角色类型
│   └── server.ts            # 服务器类型
└── generated/                # 自动生成的类型
    ├── server-schemas.ts     # 从服务端Schema生成
    └── api-types.ts          # 从API文档生成
```

## 实施步骤
1. 扫描服务端数据结构，生成基础类型
2. 按模块拆分现有类型定义
3. 建立前后端类型同步机制
4. 重构现有代码以使用新的类型结构

## 类型定义原则
1. **服务端优先**：以服务端Schema为准，前端适配
2. **模块化**：按业务领域拆分，避免单文件过大
3. **兼容性**：支持readonly和mutable类型转换
4. **文档化**：每个类型都有清晰的注释说明
