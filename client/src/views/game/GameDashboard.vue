<template>
  <div class="game-dashboard">
    <!-- 游戏头部 -->
    <div class="dashboard-header">
      <div class="user-info">
        <div class="user-avatar">
          <img :src="userInfo.avatar" :alt="userInfo.username" />
        </div>
        <div class="user-details">
          <h2 class="username">{{ userInfo.username }}</h2>
          <div class="user-stats">
            <span class="level">等级 {{ userInfo.level }}</span>
            <span class="server">{{ userInfo.serverName }}</span>
          </div>
        </div>
      </div>

      <div class="resources">
        <div class="resource-item">
          <CoinIcon />
          <span class="resource-value">{{ formatCurrency(userInfo.coins) }}</span>
        </div>
        <div class="resource-item">
          <DiamondIcon />
          <span class="resource-value">{{ userInfo.diamonds }}</span>
        </div>
        <div class="resource-item">
          <EnergyIcon />
          <span class="resource-value">{{ userInfo.energy }}/{{ userInfo.maxEnergy }}</span>
        </div>
      </div>

      <div class="quick-actions">
        <a-space>
          <GameTooltip title="每日签到">
            <a-button type="primary" @click="showDailyReward = true">
              <GiftOutlined />
            </a-button>
          </GameTooltip>
          
          <GameTooltip title="邮件">
            <a-badge :count="unreadMails" size="small">
              <a-button @click="showMailbox = true">
                <MailOutlined />
              </a-button>
            </a-badge>
          </GameTooltip>
          
          <GameTooltip title="设置">
            <a-button @click="showSettings = true">
              <SettingOutlined />
            </a-button>
          </GameTooltip>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <!-- 球队信息 -->
        <GameCard title="我的球队" class="team-card">
          <template #extra>
            <a-button size="small" @click="$router.push('/game/team')">
              管理
            </a-button>
          </template>

          <div class="team-overview">
            <div class="team-stats">
              <div class="stat-item">
                <span class="stat-label">球队评分</span>
                <span class="stat-value">{{ teamInfo.rating }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">球员数量</span>
                <span class="stat-value">{{ teamInfo.playerCount }}/{{ teamInfo.maxPlayers }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">阵型</span>
                <span class="stat-value">{{ teamInfo.formation }}</span>
              </div>
            </div>

            <div class="team-progress">
              <div class="progress-item">
                <span class="progress-label">经验值</span>
                <GameProgress
                  :current="teamInfo.experience"
                  :total="teamInfo.expToNextLevel"
                  size="small"
                  type="success"
                />
              </div>
            </div>
          </div>
        </GameCard>

        <!-- 快速导航 -->
        <GameCard title="快速导航" class="navigation-card">
          <div class="nav-grid">
            <div
              v-for="nav in quickNavigation"
              :key="nav.key"
              class="nav-item"
              @click="$router.push(nav.path)"
            >
              <div class="nav-icon">
                <component :is="nav.icon" />
              </div>
              <span class="nav-label">{{ nav.label }}</span>
              <div v-if="nav.badge" class="nav-badge">{{ nav.badge }}</div>
            </div>
          </div>
        </GameCard>

        <!-- 活动公告 -->
        <GameCard title="活动公告" class="announcement-card">
          <div class="announcements">
            <div
              v-for="announcement in announcements"
              :key="announcement.id"
              class="announcement-item"
              @click="viewAnnouncement(announcement)"
            >
              <div class="announcement-title">{{ announcement.title }}</div>
              <div class="announcement-time">{{ formatTime(announcement.createdAt) }}</div>
            </div>
          </div>
        </GameCard>
      </div>

      <!-- 中间面板 -->
      <div class="center-panel">
        <!-- 比赛中心 -->
        <GameCard title="比赛中心" class="match-center">
          <template #extra>
            <a-space>
              <a-button size="small" @click="$router.push('/game/matches')">
                查看全部
              </a-button>
              <a-button type="primary" size="small" @click="quickMatch">
                快速比赛
              </a-button>
            </a-space>
          </template>

          <div v-if="currentMatch" class="current-match">
            <h4>进行中的比赛</h4>
            <MatchSimulator
              :match="currentMatch"
              :can-control="false"
              :show-lineups="false"
              :auto-update="true"
            />
          </div>

          <div v-else class="no-match">
            <a-empty description="暂无进行中的比赛">
              <a-button type="primary" @click="quickMatch">开始比赛</a-button>
            </a-empty>
          </div>

          <div class="recent-matches">
            <h4>最近比赛</h4>
            <div class="matches-list">
              <div
                v-for="match in recentMatches"
                :key="match.id"
                class="match-item"
                @click="viewMatchDetail(match)"
              >
                <div class="match-teams">
                  <span class="home-team">{{ match.homeTeam.name }}</span>
                  <span class="match-score">{{ match.homeScore }} : {{ match.awayScore }}</span>
                  <span class="away-team">{{ match.awayTeam.name }}</span>
                </div>
                <div class="match-time">{{ formatTime(match.endTime) }}</div>
              </div>
            </div>
          </div>
        </GameCard>

        <!-- 训练状态 -->
        <GameCard title="训练状态" class="training-status">
          <template #extra>
            <a-button size="small" @click="$router.push('/game/training')">
              训练中心
            </a-button>
          </template>

          <div v-if="trainingQueue.length > 0" class="training-list">
            <div
              v-for="training in trainingQueue"
              :key="training.id"
              class="training-item"
            >
              <div class="training-hero">
                <img :src="training.heroAvatar" :alt="training.heroName" />
                <div class="hero-info">
                  <span class="hero-name">{{ training.heroName }}</span>
                  <span class="training-type">{{ training.trainingTypeName }}</span>
                </div>
              </div>
              
              <div class="training-progress">
                <GameProgress
                  :current="training.progress"
                  :total="100"
                  size="small"
                  type="primary"
                />
                <span class="time-left">{{ training.remainingTime }}</span>
              </div>
            </div>
          </div>

          <div v-else class="no-training">
            <a-empty description="暂无训练中的球员">
              <a-button type="primary" @click="$router.push('/game/training')">
                开始训练
              </a-button>
            </a-empty>
          </div>
        </GameCard>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 市场热门 -->
        <GameCard title="市场热门" class="market-hot">
          <template #extra>
            <a-button size="small" @click="$router.push('/game/market')">
              转会市场
            </a-button>
          </template>

          <div class="hot-players">
            <HeroCard
              v-for="player in hotMarketPlayers"
              :key="player.heroId"
              :hero="player"
              :compact="true"
              :show-actions="false"
              @click="viewPlayerInMarket(player)"
            />
          </div>
        </GameCard>

        <!-- 排行榜 -->
        <GameCard title="排行榜" class="rankings">
          <template #extra>
            <a-button size="small" @click="$router.push('/game/rankings')">
              查看全部
            </a-button>
          </template>

          <a-tabs v-model:activeKey="rankingTab" size="small">
            <a-tab-pane key="team" tab="球队">
              <div class="ranking-list">
                <div
                  v-for="(team, index) in teamRankings"
                  :key="team.id"
                  class="ranking-item"
                >
                  <span class="rank">{{ index + 1 }}</span>
                  <span class="team-name">{{ team.name }}</span>
                  <span class="rating">{{ team.rating }}</span>
                </div>
              </div>
            </a-tab-pane>
            
            <a-tab-pane key="player" tab="球员">
              <div class="ranking-list">
                <div
                  v-for="(player, index) in playerRankings"
                  :key="player.id"
                  class="ranking-item"
                >
                  <span class="rank">{{ index + 1 }}</span>
                  <span class="player-name">{{ player.name }}</span>
                  <span class="rating">{{ player.rating }}</span>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </GameCard>

        <!-- 每日任务 -->
        <GameCard title="每日任务" class="daily-tasks">
          <div class="tasks-list">
            <div
              v-for="task in dailyTasks"
              :key="task.id"
              class="task-item"
              :class="{ 'task-completed': task.completed }"
            >
              <div class="task-info">
                <span class="task-title">{{ task.title }}</span>
                <span class="task-reward">{{ task.reward }}</span>
              </div>
              
              <div class="task-progress">
                <GameProgress
                  :current="task.progress"
                  :total="task.target"
                  size="small"
                  :type="task.completed ? 'success' : 'primary'"
                />
              </div>
              
              <a-button
                v-if="task.completed && !task.claimed"
                type="primary"
                size="small"
                @click="claimTaskReward(task)"
              >
                领取
              </a-button>
            </div>
          </div>
        </GameCard>
      </div>
    </div>

    <!-- 模态框 -->
    <GameModal
      v-model:open="showDailyReward"
      title="每日签到"
      @ok="claimDailyReward"
    >
      <div class="daily-reward-content">
        <p>今日签到奖励：</p>
        <div class="reward-items">
          <div class="reward-item">
            <CoinIcon />
            <span>1000 金币</span>
          </div>
          <div class="reward-item">
            <EnergyIcon />
            <span>50 体力</span>
          </div>
        </div>
      </div>
    </GameModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  GiftOutlined,
  MailOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import {
  GameCard,
  GameModal,
  GameTooltip,
  GameProgress,
  HeroCard,
  MatchSimulator
} from '@/components'
import { componentUtils } from '@/components'
import type { Match, Hero, TrainingQueue, DailyTask } from '@/types'

// 响应式数据
const showDailyReward = ref(false)
const showMailbox = ref(false)
const showSettings = ref(false)
const rankingTab = ref('team')

// 用户信息
const userInfo = ref({
  username: '足球经理',
  level: 15,
  avatar: '/default-avatar.png',
  serverName: '欧洲服务器',
  coins: 125000,
  diamonds: 500,
  energy: 80,
  maxEnergy: 100
})

// 球队信息
const teamInfo = ref({
  rating: 85,
  playerCount: 22,
  maxPlayers: 30,
  formation: '4-3-3',
  experience: 15600,
  expToNextLevel: 20000
})

// 其他数据
const unreadMails = ref(3)
const currentMatch = ref<Match | null>(null)
const recentMatches = ref<Match[]>([])
const trainingQueue = ref<TrainingQueue[]>([])
const hotMarketPlayers = ref<Hero[]>([])
const teamRankings = ref([])
const playerRankings = ref([])
const dailyTasks = ref<DailyTask[]>([])
const announcements = ref([])

// 快速导航
const quickNavigation = ref([
  { key: 'team', label: '球队管理', icon: 'TeamOutlined', path: '/game/team' },
  { key: 'training', label: '训练中心', icon: 'ThunderboltOutlined', path: '/game/training', badge: 3 },
  { key: 'market', label: '转会市场', icon: 'ShopOutlined', path: '/game/market' },
  { key: 'matches', label: '比赛', icon: 'TrophyOutlined', path: '/game/matches' },
  { key: 'formation', label: '阵型', icon: 'DeploymentUnitOutlined', path: '/game/formation' },
  { key: 'inventory', label: '背包', icon: 'InboxOutlined', path: '/game/inventory' }
])

// 方法
const formatCurrency = componentUtils.formatCurrency
const formatTime = (time: string) => {
  // 实现时间格式化
  return new Date(time).toLocaleString()
}

const quickMatch = () => {
  // 实现快速比赛
}

const viewMatchDetail = (match: Match) => {
  // 查看比赛详情
}

const viewPlayerInMarket = (player: Hero) => {
  // 在市场中查看球员
}

const viewAnnouncement = (announcement: any) => {
  // 查看公告详情
}

const claimTaskReward = (task: DailyTask) => {
  // 领取任务奖励
}

const claimDailyReward = () => {
  // 领取每日奖励
  showDailyReward.value = false
}

// 生命周期
onMounted(() => {
  // 加载仪表板数据
})
</script>

<style lang="less" scoped>
.game-dashboard {
  padding: @padding-lg;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @margin-xl;
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base @padding-lg;
  
  .user-info {
    display: flex;
    align-items: center;
    gap: @margin-base;
    
    .user-avatar {
      width: 60px;
      height: 60px;
      
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    
    .user-details {
      .username {
        color: @text-color;
        margin: 0 0 @margin-xs 0;
      }
      
      .user-stats {
        display: flex;
        gap: @margin-base;
        font-size: @font-size-sm;
        color: @text-color-secondary;
      }
    }
  }
  
  .resources {
    display: flex;
    gap: @margin-lg;
    
    .resource-item {
      display: flex;
      align-items: center;
      gap: @margin-xs;
      padding: @padding-sm @padding-base;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: @border-radius-base;
      
      .resource-value {
        font-weight: bold;
        color: @primary-color;
      }
    }
  }
}

.dashboard-content {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: @margin-lg;
  
  .left-panel,
  .right-panel {
    display: flex;
    flex-direction: column;
    gap: @margin-base;
  }
  
  .center-panel {
    display: flex;
    flex-direction: column;
    gap: @margin-base;
  }
}

.team-card {
  .team-overview {
    .team-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: @margin-base;
      margin-bottom: @margin-base;
      
      .stat-item {
        text-align: center;
        
        .stat-label {
          display: block;
          color: @text-color-secondary;
          font-size: @font-size-sm;
          margin-bottom: @margin-xs;
        }
        
        .stat-value {
          color: @text-color;
          font-weight: bold;
          font-size: @font-size-lg;
        }
      }
    }
    
    .team-progress {
      .progress-item {
        .progress-label {
          color: @text-color-secondary;
          font-size: @font-size-sm;
          margin-bottom: @margin-xs;
          display: block;
        }
      }
    }
  }
}

.navigation-card {
  .nav-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: @margin-sm;
    
    .nav-item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: @margin-xs;
      padding: @padding-base;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: @border-radius-base;
      cursor: pointer;
      transition: all @transition-duration;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
      }
      
      .nav-icon {
        font-size: @font-size-xl;
        color: @primary-color;
      }
      
      .nav-label {
        font-size: @font-size-sm;
        color: @text-color;
        text-align: center;
      }
      
      .nav-badge {
        position: absolute;
        top: 4px;
        right: 4px;
        background-color: @error-color;
        color: white;
        font-size: @font-size-xs;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
      }
    }
  }
}

.match-center {
  .current-match {
    margin-bottom: @margin-lg;
    
    h4 {
      color: @text-color;
      margin-bottom: @margin-base;
    }
  }
  
  .recent-matches {
    h4 {
      color: @text-color;
      margin-bottom: @margin-base;
    }
    
    .matches-list {
      .match-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: @padding-sm;
        border-bottom: 1px solid @border-color;
        cursor: pointer;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.1);
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .match-teams {
          display: flex;
          align-items: center;
          gap: @margin-sm;
          
          .home-team,
          .away-team {
            color: @text-color;
            min-width: 80px;
          }
          
          .away-team {
            text-align: right;
          }
          
          .match-score {
            color: @primary-color;
            font-weight: bold;
            font-size: @font-size-lg;
          }
        }
        
        .match-time {
          color: @text-color-secondary;
          font-size: @font-size-sm;
        }
      }
    }
  }
}

.training-status {
  .training-list {
    .training-item {
      display: flex;
      align-items: center;
      gap: @margin-base;
      padding: @padding-sm 0;
      border-bottom: 1px solid @border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      .training-hero {
        display: flex;
        align-items: center;
        gap: @margin-sm;
        min-width: 120px;
        
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }
        
        .hero-info {
          display: flex;
          flex-direction: column;
          
          .hero-name {
            color: @text-color;
            font-size: @font-size-sm;
            font-weight: 500;
          }
          
          .training-type {
            color: @text-color-secondary;
            font-size: @font-size-xs;
          }
        }
      }
      
      .training-progress {
        flex: 1;
        
        .time-left {
          color: @text-color-secondary;
          font-size: @font-size-xs;
          margin-top: @margin-xs;
          display: block;
          text-align: center;
        }
      }
    }
  }
}

.market-hot {
  .hot-players {
    display: flex;
    flex-direction: column;
    gap: @margin-sm;
  }
}

.rankings {
  .ranking-list {
    .ranking-item {
      display: flex;
      align-items: center;
      gap: @margin-sm;
      padding: @padding-xs 0;
      border-bottom: 1px solid @border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      .rank {
        min-width: 24px;
        text-align: center;
        font-weight: bold;
        color: @primary-color;
      }
      
      .team-name,
      .player-name {
        flex: 1;
        color: @text-color;
        font-size: @font-size-sm;
      }
      
      .rating {
        color: @warning-color;
        font-weight: bold;
        font-size: @font-size-sm;
      }
    }
  }
}

.daily-tasks {
  .tasks-list {
    .task-item {
      padding: @padding-sm 0;
      border-bottom: 1px solid @border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.task-completed {
        opacity: 0.7;
      }
      
      .task-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @margin-xs;
        
        .task-title {
          color: @text-color;
          font-size: @font-size-sm;
        }
        
        .task-reward {
          color: @success-color;
          font-size: @font-size-xs;
          font-weight: bold;
        }
      }
      
      .task-progress {
        margin-bottom: @margin-xs;
      }
    }
  }
}

.daily-reward-content {
  text-align: center;
  
  .reward-items {
    display: flex;
    justify-content: center;
    gap: @margin-lg;
    margin-top: @margin-base;
    
    .reward-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: @margin-xs;
      padding: @padding-base;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: @border-radius-base;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    
    .left-panel,
    .right-panel {
      order: 2;
    }
    
    .center-panel {
      order: 1;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: @margin-base;
  }
  
  .resources {
    justify-content: center;
  }
  
  .nav-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
