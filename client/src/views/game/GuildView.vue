<template>
  <div class="guild-view">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">公会系统</h2>
        <div v-if="currentGuild" class="guild-info">
          <span class="guild-name">{{ currentGuild.name }}</span>
          <span class="guild-level">Lv.{{ currentGuild.level }}</span>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button v-if="!currentGuild" @click="showCreateGuild = true" type="primary">
            <PlusOutlined />
            创建公会
          </a-button>
          <a-button v-if="!currentGuild" @click="showJoinGuild = true">
            <TeamOutlined />
            加入公会
          </a-button>
          <a-button v-if="currentGuild" @click="refreshGuildData">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 无公会状态 -->
    <div v-if="!currentGuild" class="no-guild-state">
      <div class="empty-content">
        <div class="empty-icon">🏛️</div>
        <h3>您还没有加入公会</h3>
        <p>加入公会可以与其他玩家一起参与公会活动，获得更多奖励</p>
        <a-space>
          <a-button type="primary" @click="showCreateGuild = true">
            创建公会
          </a-button>
          <a-button @click="showJoinGuild = true">
            加入公会
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 公会主界面 -->
    <div v-else class="guild-content">
      <a-tabs v-model:activeKey="activeTab">
        <!-- 公会概览 -->
        <a-tab-pane key="overview" tab="公会概览">
          <div class="guild-overview">
            <div class="guild-stats">
              <div class="stat-card">
                <div class="stat-value">{{ currentGuild.memberCount }}</div>
                <div class="stat-label">成员数量</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ currentGuild.level }}</div>
                <div class="stat-label">公会等级</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ currentGuild.exp }}</div>
                <div class="stat-label">公会经验</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ currentGuild.ranking }}</div>
                <div class="stat-label">公会排名</div>
              </div>
            </div>
            
            <div class="guild-description">
              <h4>公会简介</h4>
              <p>{{ currentGuild.description || '暂无公会简介' }}</p>
            </div>
          </div>
        </a-tab-pane>

        <!-- 成员管理 -->
        <a-tab-pane key="members" tab="成员管理">
          <div class="guild-members">
            <div class="members-header">
              <h4>公会成员 ({{ guildMembers.length }}/{{ currentGuild.maxMembers }})</h4>
              <a-button v-if="canInvite" @click="showInviteModal = true">
                <UserAddOutlined />
                邀请成员
              </a-button>
            </div>
            
            <a-table
              :columns="memberColumns"
              :data-source="guildMembers"
              :pagination="false"
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                  <div class="member-info">
                    <span class="member-name">{{ record.name }}</span>
                    <span v-if="record.isOnline" class="online-status">在线</span>
                  </div>
                </template>
                
                <template v-else-if="column.key === 'role'">
                  <a-tag :color="getRoleColor(record.role)">
                    {{ getRoleText(record.role) }}
                  </a-tag>
                </template>
                
                <template v-else-if="column.key === 'contribution'">
                  <span class="contribution">{{ record.contribution }}</span>
                </template>
                
                <template v-else-if="column.key === 'actions'">
                  <a-space v-if="canManageMembers && record.id !== currentUserId">
                    <a-button size="small" @click="promoteMember(record)">
                      提升
                    </a-button>
                    <a-button size="small" danger @click="kickMember(record)">
                      踢出
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 公会活动 -->
        <a-tab-pane key="activities" tab="公会活动">
          <div class="guild-activities">
            <div class="activities-list">
              <div
                v-for="activity in guildActivities"
                :key="activity.id"
                class="activity-card"
              >
                <div class="activity-header">
                  <div class="activity-name">{{ activity.name }}</div>
                  <div class="activity-status" :class="activity.status">
                    {{ getActivityStatusText(activity.status) }}
                  </div>
                </div>
                
                <div class="activity-description">
                  {{ activity.description }}
                </div>
                
                <div class="activity-info">
                  <div class="info-item">
                    <span>参与人数:</span>
                    <span>{{ activity.participants }}/{{ activity.maxParticipants }}</span>
                  </div>
                  <div class="info-item">
                    <span>活动时间:</span>
                    <span>{{ formatTime(activity.startTime) }}</span>
                  </div>
                </div>
                
                <div class="activity-actions">
                  <a-button
                    v-if="activity.status === 'open'"
                    type="primary"
                    @click="joinActivity(activity)"
                    :disabled="activity.participants >= activity.maxParticipants"
                  >
                    参加活动
                  </a-button>
                  <a-button
                    v-else-if="activity.status === 'joined'"
                    @click="leaveActivity(activity)"
                  >
                    退出活动
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 公会商店 -->
        <a-tab-pane key="shop" tab="公会商店">
          <div class="guild-shop">
            <div class="shop-header">
              <h4>公会商店</h4>
              <div class="contribution-info">
                我的贡献值: <strong>{{ myContribution }}</strong>
              </div>
            </div>
            
            <div class="shop-items">
              <div
                v-for="item in shopItems"
                :key="item.id"
                class="shop-item"
              >
                <div class="item-icon">{{ item.icon }}</div>
                <div class="item-info">
                  <div class="item-name">{{ item.name }}</div>
                  <div class="item-description">{{ item.description }}</div>
                </div>
                <div class="item-price">
                  <span class="price">{{ item.price }} 贡献值</span>
                  <a-button
                    size="small"
                    type="primary"
                    @click="buyItem(item)"
                    :disabled="myContribution < item.price"
                  >
                    购买
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 创建公会模态框 -->
    <a-modal
      v-model:open="showCreateGuild"
      title="创建公会"
      @ok="createGuild"
      @cancel="resetCreateForm"
      :confirm-loading="isCreating"
    >
      <a-form :model="createForm" layout="vertical">
        <a-form-item label="公会名称" required>
          <a-input v-model:value="createForm.name" placeholder="请输入公会名称" />
        </a-form-item>
        <a-form-item label="公会简介">
          <a-textarea
            v-model:value="createForm.description"
            placeholder="请输入公会简介"
            :rows="3"
          />
        </a-form-item>
        <a-form-item label="创建费用">
          <div class="create-cost">
            💰 {{ createCost }} 金币
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 加入公会模态框 -->
    <a-modal
      v-model:open="showJoinGuild"
      title="加入公会"
      :footer="null"
      width="600px"
    >
      <div class="join-guild-content">
        <a-input
          v-model:value="searchGuildText"
          placeholder="搜索公会名称"
          @change="searchGuilds"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>
        
        <div class="guild-list">
          <div
            v-for="guild in availableGuilds"
            :key="guild.id"
            class="guild-item"
          >
            <div class="guild-info">
              <div class="guild-name">{{ guild.name }}</div>
              <div class="guild-stats">
                Lv.{{ guild.level }} | {{ guild.memberCount }}/{{ guild.maxMembers }}人
              </div>
            </div>
            <a-button
              size="small"
              type="primary"
              @click="requestJoinGuild(guild)"
              :disabled="guild.memberCount >= guild.maxMembers"
            >
              申请加入
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import {
  PlusOutlined,
  TeamOutlined,
  ReloadOutlined,
  UserAddOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import { useGlobalStore } from '@/stores/global'
import type { Guild, GuildMember, GuildActivity } from '@/types'

// 状态
const globalStore = useGlobalStore()

const activeTab = ref('overview')
const isCreating = ref(false)
const showCreateGuild = ref(false)
const showJoinGuild = ref(false)
const showInviteModal = ref(false)
const searchGuildText = ref('')

// 公会数据
const currentGuild = ref<Guild | null>(null)
const guildMembers = ref<GuildMember[]>([])
const guildActivities = ref<GuildActivity[]>([])
const availableGuilds = ref<Guild[]>([])
const myContribution = ref(1250)
const currentUserId = ref('user123')
const createCost = ref(50000)

// 创建公会表单
const createForm = reactive({
  name: '',
  description: ''
})

// 计算属性
const canInvite = computed(() => {
  // 检查是否有邀请权限
  return true
})

const canManageMembers = computed(() => {
  // 检查是否有管理成员权限
  return true
})

// 成员表格列
const memberColumns = [
  { title: '成员', key: 'name', dataIndex: 'name' },
  { title: '职位', key: 'role' },
  { title: '贡献值', key: 'contribution' },
  { title: '最后在线', key: 'lastOnline', dataIndex: 'lastOnline' },
  { title: '操作', key: 'actions' }
]

// 商店物品
const shopItems = ref([
  {
    id: '1',
    name: '训练加速器',
    description: '减少训练冷却时间',
    icon: '⚡',
    price: 100
  },
  {
    id: '2',
    name: '经验药水',
    description: '增加球员经验获得',
    icon: '🧪',
    price: 200
  }
])

// 方法
const refreshGuildData = async () => {
  // TODO: 刷新公会数据
}

const createGuild = async () => {
  if (!createForm.name.trim()) {
    globalStore.addNotification({
      type: 'warning',
      title: '信息不完整',
      message: '请输入公会名称'
    })
    return
  }
  
  try {
    isCreating.value = true
    
    // TODO: 调用创建公会API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    globalStore.addNotification({
      type: 'success',
      title: '创建成功',
      message: `公会 ${createForm.name} 创建成功！`
    })
    
    showCreateGuild.value = false
    resetCreateForm()
    // 刷新数据
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '创建失败',
      message: error.message
    })
  } finally {
    isCreating.value = false
  }
}

const resetCreateForm = () => {
  createForm.name = ''
  createForm.description = ''
}

const searchGuilds = () => {
  // TODO: 搜索公会
}

const requestJoinGuild = async (guild: Guild) => {
  try {
    // TODO: 申请加入公会
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    globalStore.addNotification({
      type: 'success',
      title: '申请已发送',
      message: `已向公会 ${guild.name} 发送加入申请`
    })
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '申请失败',
      message: error.message
    })
  }
}

const joinActivity = async (activity: GuildActivity) => {
  try {
    // TODO: 参加公会活动
    globalStore.addNotification({
      type: 'success',
      title: '参加成功',
      message: `已参加活动: ${activity.name}`
    })
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '参加失败',
      message: error.message
    })
  }
}

const leaveActivity = async (activity: GuildActivity) => {
  try {
    // TODO: 退出公会活动
    globalStore.addNotification({
      type: 'success',
      title: '退出成功',
      message: `已退出活动: ${activity.name}`
    })
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '退出失败',
      message: error.message
    })
  }
}

const buyItem = async (item: any) => {
  try {
    // TODO: 购买公会商店物品
    myContribution.value -= item.price
    
    globalStore.addNotification({
      type: 'success',
      title: '购买成功',
      message: `成功购买 ${item.name}`
    })
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '购买失败',
      message: error.message
    })
  }
}

const promoteMember = (member: GuildMember) => {
  globalStore.addNotification({
    type: 'info',
    title: '提升成员',
    message: `提升 ${member.name} 的职位`
  })
}

const kickMember = (member: GuildMember) => {
  globalStore.addNotification({
    type: 'warning',
    title: '踢出成员',
    message: `将 ${member.name} 踢出公会`
  })
}

// 工具函数
const getRoleColor = (role: string): string => {
  const colors: Record<string, string> = {
    leader: 'red',
    officer: 'orange',
    member: 'blue'
  }
  return colors[role] || 'default'
}

const getRoleText = (role: string): string => {
  const texts: Record<string, string> = {
    leader: '会长',
    officer: '官员',
    member: '成员'
  }
  return texts[role] || role
}

const getActivityStatusText = (status: string): string => {
  const texts: Record<string, string> = {
    open: '开放中',
    joined: '已参加',
    closed: '已结束'
  }
  return texts[status] || status
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(() => {
  // 模拟有公会的情况
  currentGuild.value = {
    id: 'guild1',
    name: '足球精英',
    level: 5,
    exp: 12500,
    memberCount: 25,
    maxMembers: 30,
    ranking: 15,
    description: '专业的足球经理公会，欢迎热爱足球的朋友加入！'
  }
  
  // 模拟成员数据
  guildMembers.value = [
    {
      id: 'user123',
      name: '我',
      role: 'member',
      contribution: 1250,
      lastOnline: '刚刚',
      isOnline: true
    }
  ]
  
  // 模拟活动数据
  guildActivities.value = [
    {
      id: 'activity1',
      name: '公会联赛',
      description: '与其他公会进行友谊比赛',
      status: 'open',
      participants: 8,
      maxParticipants: 16,
      startTime: Date.now() + 3600000
    }
  ]
})
</script>

<style lang="less" scoped>
.guild-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .guild-info {
        display: flex;
        gap: @margin-base;
        
        .guild-name {
          color: @primary-color;
          font-weight: bold;
        }
        
        .guild-level {
          color: @text-color-secondary;
        }
      }
    }
  }
  
  .no-guild-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    
    .empty-content {
      text-align: center;
      
      .empty-icon {
        font-size: 4rem;
        margin-bottom: @margin-lg;
      }
      
      h3 {
        color: @text-color;
        margin-bottom: @margin-base;
      }
      
      p {
        color: @text-color-secondary;
        margin-bottom: @margin-lg;
      }
    }
  }
  
  .guild-content {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    
    .guild-overview {
      .guild-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: @margin-base;
        margin-bottom: @margin-lg;
        
        .stat-card {
          background-color: rgba(0, 0, 0, 0.2);
          border: 1px solid @border-color;
          border-radius: @border-radius-base;
          padding: @padding-base;
          text-align: center;
          
          .stat-value {
            font-size: @font-size-xl;
            font-weight: bold;
            color: @primary-color;
            margin-bottom: @margin-xs;
          }
          
          .stat-label {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
      }
      
      .guild-description {
        h4 {
          color: @text-color;
          margin-bottom: @margin-base;
        }
        
        p {
          color: @text-color-secondary;
          line-height: 1.6;
        }
      }
    }
    
    .guild-members {
      .members-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @margin-base;
        
        h4 {
          color: @text-color;
          margin: 0;
        }
      }
      
      .member-info {
        .member-name {
          color: @text-color;
          margin-right: @margin-sm;
        }
        
        .online-status {
          color: @success-color;
          font-size: @font-size-xs;
        }
      }
      
      .contribution {
        color: @primary-color;
        font-weight: bold;
      }
    }
    
    .guild-activities {
      .activities-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: @margin-base;
        
        .activity-card {
          background-color: rgba(0, 0, 0, 0.2);
          border: 1px solid @border-color;
          border-radius: @border-radius-base;
          padding: @padding-base;
          
          .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: @margin-sm;
            
            .activity-name {
              color: @text-color;
              font-weight: bold;
            }
            
            .activity-status {
              padding: 2px 8px;
              border-radius: 12px;
              font-size: @font-size-xs;
              
              &.open {
                background-color: @success-color;
                color: white;
              }
              
              &.joined {
                background-color: @primary-color;
                color: white;
              }
              
              &.closed {
                background-color: @text-color-secondary;
                color: white;
              }
            }
          }
          
          .activity-description {
            color: @text-color-secondary;
            margin-bottom: @margin-sm;
          }
          
          .activity-info {
            margin-bottom: @margin-base;
            
            .info-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: @margin-xs;
              font-size: @font-size-sm;
              color: @text-color-secondary;
            }
          }
          
          .activity-actions {
            text-align: center;
          }
        }
      }
    }
    
    .guild-shop {
      .shop-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @margin-base;
        
        h4 {
          color: @text-color;
          margin: 0;
        }
        
        .contribution-info {
          color: @text-color-secondary;
          
          strong {
            color: @primary-color;
          }
        }
      }
      
      .shop-items {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: @margin-base;
        
        .shop-item {
          background-color: rgba(0, 0, 0, 0.2);
          border: 1px solid @border-color;
          border-radius: @border-radius-base;
          padding: @padding-base;
          display: flex;
          align-items: center;
          gap: @margin-base;
          
          .item-icon {
            font-size: 2rem;
          }
          
          .item-info {
            flex: 1;
            
            .item-name {
              color: @text-color;
              font-weight: bold;
              margin-bottom: @margin-xs;
            }
            
            .item-description {
              color: @text-color-secondary;
              font-size: @font-size-sm;
            }
          }
          
          .item-price {
            text-align: center;
            
            .price {
              display: block;
              color: @primary-color;
              font-weight: bold;
              margin-bottom: @margin-xs;
            }
          }
        }
      }
    }
  }
}

// 模态框样式
.create-cost {
  color: @warning-color;
  font-weight: bold;
  font-size: @font-size-lg;
}

.join-guild-content {
  .guild-list {
    margin-top: @margin-base;
    max-height: 300px;
    overflow-y: auto;
    
    .guild-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: @padding-sm;
      border-bottom: 1px solid @border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      .guild-info {
        .guild-name {
          color: @text-color;
          font-weight: bold;
          margin-bottom: @margin-xs;
        }
        
        .guild-stats {
          color: @text-color-secondary;
          font-size: @font-size-sm;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .guild-view {
    padding: @padding-base;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-base;
    }
    
    .guild-stats {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .activities-list,
    .shop-items {
      grid-template-columns: 1fr;
    }
  }
}
</style>
