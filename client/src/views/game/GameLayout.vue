<template>
  <div class="game-layout">
    <!-- 游戏头部 -->
    <header class="game-header">
      <div class="header-left">
        <h1 class="game-title">足球游戏</h1>
        <div class="server-info">
          <span class="server-name">{{ selectedServer?.name }}</span>
          <span class="character-name">{{ selectedCharacter?.name }}</span>
        </div>
      </div>
      
      <div class="header-center">
        <div class="game-status">
          <div class="status-item">
            <span class="status-label">等级:</span>
            <span class="status-value">{{ selectedCharacter?.level || 1 }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">金币:</span>
            <span class="status-value">1,234,567</span>
          </div>
          <div class="status-item">
            <span class="status-label">钻石:</span>
            <span class="status-value">999</span>
          </div>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-badge :count="unreadNotifications.length" :offset="[10, 0]">
            <a-button type="text" @click="showNotifications">
              <BellOutlined />
            </a-button>
          </a-badge>
          
          <a-dropdown>
            <a-button type="text">
              <UserOutlined />
              {{ user?.username }}
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleUserMenuClick">
                <a-menu-item key="profile">个人资料</a-menu-item>
                <a-menu-item key="settings">设置</a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">退出登录</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="game-content">
      <!-- 侧边导航 -->
      <aside class="game-sidebar">
        <nav class="game-nav">
          <div class="nav-section">
            <div class="nav-title">主要功能</div>
            <ul class="nav-list">
              <li class="nav-item">
                <router-link to="/game/dashboard" class="nav-link">
                  <DashboardOutlined />
                  <span>主页</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/game/heroes" class="nav-link">
                  <TeamOutlined />
                  <span>球员管理</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/game/formations" class="nav-link">
                  <SettingOutlined />
                  <span>战术配置</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/game/matches" class="nav-link">
                  <TrophyOutlined />
                  <span>比赛</span>
                </router-link>
              </li>
            </ul>
          </div>
          
          <div class="nav-section">
            <div class="nav-title">管理功能</div>
            <ul class="nav-list">
              <li class="nav-item">
                <router-link to="/game/training" class="nav-link">
                  <RiseOutlined />
                  <span>训练</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/game/market" class="nav-link">
                  <ShopOutlined />
                  <span>转会市场</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/game/inventory" class="nav-link">
                  <InboxOutlined />
                  <span>背包</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/game/guild" class="nav-link">
                  <CrownOutlined />
                  <span>公会</span>
                </router-link>
              </li>
            </ul>
          </div>
        </nav>
        
        <!-- 快捷操作 -->
        <div class="quick-actions">
          <div class="quick-title">快捷操作</div>
          <div class="quick-buttons">
            <GameButton 
              size="small" 
              type="ghost" 
              text="快速比赛"
              hotkey="Q"
              @click="quickMatch"
            />
            <GameButton 
              size="small" 
              type="ghost" 
              text="自动训练"
              hotkey="T"
              @click="autoTrain"
            />
          </div>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="game-main">
        <router-view v-slot="{ Component }">
          <transition name="page" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>

    <!-- 底部状态栏 -->
    <footer class="game-footer">
      <div class="footer-left">
        <span class="connection-status" :class="connectionState">
          <span class="status-dot"></span>
          {{ getConnectionText() }}
        </span>
      </div>
      
      <div class="footer-center">
        <span class="game-time">{{ currentTime }}</span>
      </div>
      
      <div class="footer-right">
        <span class="version">v{{ appConfig.version }}</span>
      </div>
    </footer>

    <!-- 通知抽屉 -->
    <a-drawer
      v-model:open="notificationDrawerVisible"
      title="通知中心"
      placement="right"
      width="400"
    >
      <div class="notifications-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.read }"
        >
          <div class="notification-header">
            <span class="notification-title">{{ notification.title }}</span>
            <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
          </div>
          <div class="notification-content">{{ notification.message }}</div>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  BellOutlined,
  UserOutlined,
  DownOutlined,
  DashboardOutlined,
  TeamOutlined,
  SettingOutlined,
  TrophyOutlined,
  RiseOutlined,
  ShopOutlined,
  InboxOutlined,
  CrownOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'
import { wsService } from '@/services/websocket'
import GameButton from '@/components/common/GameButton.vue'

const router = useRouter()
const authStore = useAuthStore()
const globalStore = useGlobalStore()

// 状态
const notificationDrawerVisible = ref(false)
const currentTime = ref(new Date().toLocaleTimeString())

// 计算属性
const user = computed(() => authStore.user)
const selectedServer = computed(() => authStore.selectedServer)
const selectedCharacter = computed(() => authStore.selectedCharacter)
const notifications = computed(() => globalStore.notifications)
const unreadNotifications = computed(() => globalStore.unreadNotifications)
const appConfig = computed(() => globalStore.appConfig)
const connectionState = computed(() => wsService.connectionState)

// 方法
const showNotifications = () => {
  notificationDrawerVisible.value = true
}

const handleUserMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'profile':
      router.push('/game/profile')
      break
    case 'settings':
      router.push('/game/settings')
      break
    case 'logout':
      authStore.logout()
      break
  }
}

const quickMatch = () => {
  globalStore.addNotification({
    type: 'info',
    title: '快速比赛',
    message: '正在寻找对手...'
  })
}

const autoTrain = () => {
  globalStore.addNotification({
    type: 'success',
    title: '自动训练',
    message: '已开启自动训练模式'
  })
}

const getConnectionText = () => {
  switch (connectionState.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'disconnected':
      return '已断开'
    default:
      return '未知状态'
  }
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

// 定时器更新时间
let timeInterval: NodeJS.Timeout

onMounted(async () => {
  // 连接WebSocket
  try {
    await wsService.connect()
  } catch (error) {
    console.error('WebSocket连接失败:', error)
  }
  
  // 启动时间更新
  timeInterval = setInterval(() => {
    currentTime.value = new Date().toLocaleTimeString()
  }, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="less" scoped>
.game-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: @bg-color;
  color: @text-color;
  font-family: @font-family-mono;
}

.game-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @padding-base @padding-lg;
  background-color: @card-bg;
  border-bottom: 1px solid @border-color;
  height: 64px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: @margin-base;
    
    .game-title {
      font-size: @font-size-xl;
      font-weight: bold;
      color: @primary-color;
      margin: 0;
    }
    
    .server-info {
      display: flex;
      flex-direction: column;
      font-size: @font-size-sm;
      
      .server-name {
        color: @text-color-secondary;
      }
      
      .character-name {
        color: @text-color;
        font-weight: bold;
      }
    }
  }
  
  .header-center {
    .game-status {
      display: flex;
      gap: @margin-lg;
      
      .status-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .status-label {
          font-size: @font-size-xs;
          color: @text-color-secondary;
        }
        
        .status-value {
          font-size: @font-size-base;
          color: @highlight-color;
          font-weight: bold;
        }
      }
    }
  }
  
  .header-right {
    :deep(.ant-btn) {
      color: @text-color;
      border-color: transparent;
      
      &:hover {
        color: @primary-color;
        background-color: rgba(82, 196, 26, 0.1);
      }
    }
  }
}

.game-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.game-sidebar {
  width: 280px;
  background-color: @card-bg;
  border-right: 1px solid @border-color;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  
  .game-nav {
    flex: 1;
    padding: @padding-base;
    
    .nav-section {
      margin-bottom: @margin-lg;
      
      .nav-title {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-bottom: @margin-sm;
        padding: 0 @padding-sm;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      
      .nav-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        .nav-item {
          margin-bottom: 2px;
          
          .nav-link {
            display: flex;
            align-items: center;
            gap: @margin-sm;
            padding: @padding-sm @padding-base;
            color: @text-color-secondary;
            text-decoration: none;
            border-radius: @border-radius-base;
            transition: all @transition-duration;
            
            &:hover {
              color: @primary-color;
              background-color: rgba(82, 196, 26, 0.1);
            }
            
            &.router-link-active {
              color: @primary-color;
              background-color: rgba(82, 196, 26, 0.2);
              font-weight: bold;
            }
            
            .anticon {
              font-size: @font-size-base;
            }
          }
        }
      }
    }
  }
  
  .quick-actions {
    padding: @padding-base;
    border-top: 1px solid @border-color;
    
    .quick-title {
      font-size: @font-size-sm;
      color: @text-color-secondary;
      margin-bottom: @margin-sm;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    
    .quick-buttons {
      display: flex;
      flex-direction: column;
      gap: @margin-xs;
    }
  }
}

.game-main {
  flex: 1;
  overflow: auto;
  padding: @padding-lg;
}

.game-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @padding-sm @padding-lg;
  background-color: @card-bg;
  border-top: 1px solid @border-color;
  height: 40px;
  font-size: @font-size-sm;
  
  .connection-status {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: @error-color;
    }
    
    &.connected .status-dot {
      background-color: @success-color;
    }
    
    &.connecting .status-dot {
      background-color: @warning-color;
      animation: pulse 1s infinite;
    }
  }
  
  .game-time {
    color: @text-color-secondary;
  }
  
  .version {
    color: @text-color-secondary;
  }
}

// 页面切换动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 通知列表样式
.notifications-list {
  .notification-item {
    padding: @padding-base;
    border-bottom: 1px solid @border-color;
    
    &.unread {
      background-color: rgba(82, 196, 26, 0.05);
      border-left: 3px solid @primary-color;
    }
    
    .notification-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: @margin-xs;
      
      .notification-title {
        font-weight: bold;
        color: @text-color;
      }
      
      .notification-time {
        font-size: @font-size-xs;
        color: @text-color-secondary;
      }
    }
    
    .notification-content {
      color: @text-color-secondary;
      font-size: @font-size-sm;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .game-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid @border-color;
  }
  
  .game-content {
    flex-direction: column;
  }
  
  .header-center {
    display: none;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
