<template>
  <div class="formations-view">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">战术配置</h2>
        <div class="formation-info">
          <span class="current-formation">当前阵型: {{ currentFormation.name }}</span>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="showFormationSelector = true">
            <SettingOutlined />
            选择阵型
          </a-button>
          <a-button @click="saveFormation" type="primary" :loading="isSaving">
            <SaveOutlined />
            保存配置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 阵型配置区域 -->
    <div class="formation-content">
      <!-- 足球场 -->
      <div class="football-field">
        <div class="field-background">
          <!-- 场地标线 -->
          <div class="field-lines">
            <div class="center-line"></div>
            <div class="center-circle"></div>
            <div class="penalty-area left"></div>
            <div class="penalty-area right"></div>
            <div class="goal-area left"></div>
            <div class="goal-area right"></div>
          </div>

          <!-- 球员位置 -->
          <div class="player-positions">
            <div
              v-for="position in formationPositions"
              :key="position.id"
              class="position-slot"
              :style="getPositionStyle(position)"
              @click="selectPosition(position)"
              @drop="handleDrop($event, position)"
              @dragover.prevent
            >
              <div
                v-if="position.heroId"
                class="player-card"
                :class="{ 'selected': selectedPositionId === position.id }"
                draggable="true"
                @dragstart="handleDragStart($event, position)"
              >
                <div class="player-name">{{ getPlayerName(position.heroId) }}</div>
                <div class="player-overall">{{ getPlayerOverall(position.heroId) }}</div>
                <div class="player-position">{{ position.position }}</div>
              </div>
              <div v-else class="empty-position">
                <span class="position-label">{{ position.position }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 替补席 -->
      <div class="substitutes-section">
        <h3 class="section-title">替补球员</h3>
        <div class="substitutes-list">
          <div
            v-for="hero in availableHeroes"
            :key="hero.heroId"
            class="substitute-card"
            draggable="true"
            @dragstart="handleDragStart($event, null, hero)"
          >
            <div class="hero-name">{{ hero.name }}</div>
            <div class="hero-overall">{{ hero.overall }}</div>
            <div class="hero-position">{{ hero.position }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 阵型选择模态框 -->
    <a-modal
      v-model:open="showFormationSelector"
      title="选择阵型"
      width="600px"
      @ok="applyFormation"
      @cancel="cancelFormationChange"
    >
      <div class="formation-options">
        <div
          v-for="formation in availableFormations"
          :key="formation.id"
          class="formation-option"
          :class="{ 'selected': selectedFormationId === formation.id }"
          @click="selectedFormationId = formation.id"
        >
          <div class="formation-name">{{ formation.name }}</div>
          <div class="formation-description">{{ formation.description }}</div>
          <div class="formation-preview">
            <div class="mini-field">
              <div
                v-for="pos in formation.positions"
                :key="pos.id"
                class="mini-position"
                :style="getMiniPositionStyle(pos)"
              >
                {{ pos.position }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { SettingOutlined, SaveOutlined } from '@ant-design/icons-vue'
import { useHeroStore } from '@/stores/hero'
import { useGlobalStore } from '@/stores/global'
import type { Hero, Formation, FormationPosition } from '@/types'

// 状态
const heroStore = useHeroStore()
const globalStore = useGlobalStore()

const isSaving = ref(false)
const showFormationSelector = ref(false)
const selectedPositionId = ref<string>('')
const selectedFormationId = ref<string>('442')

// 当前阵型配置
const currentFormation = ref<Formation>({
  id: '442',
  name: '4-4-2',
  description: '经典的攻守平衡阵型',
  positions: [
    { id: 'gk', position: 'GK', x: 10, y: 50, heroId: null, role: 'goalkeeper' },
    { id: 'lb', position: 'DEF', x: 25, y: 20, heroId: null, role: 'defender' },
    { id: 'cb1', position: 'DEF', x: 25, y: 40, heroId: null, role: 'defender' },
    { id: 'cb2', position: 'DEF', x: 25, y: 60, heroId: null, role: 'defender' },
    { id: 'rb', position: 'DEF', x: 25, y: 80, heroId: null, role: 'defender' },
    { id: 'lm', position: 'MID', x: 50, y: 25, heroId: null, role: 'midfielder' },
    { id: 'cm1', position: 'MID', x: 50, y: 45, heroId: null, role: 'midfielder' },
    { id: 'cm2', position: 'MID', x: 50, y: 55, heroId: null, role: 'midfielder' },
    { id: 'rm', position: 'MID', x: 50, y: 75, heroId: null, role: 'midfielder' },
    { id: 'st1', position: 'ATT', x: 75, y: 40, heroId: null, role: 'forward' },
    { id: 'st2', position: 'ATT', x: 75, y: 60, heroId: null, role: 'forward' }
  ]
})

// 可用阵型
const availableFormations = ref<Formation[]>([
  {
    id: '442',
    name: '4-4-2',
    description: '经典的攻守平衡阵型',
    positions: currentFormation.value.positions
  },
  {
    id: '433',
    name: '4-3-3',
    description: '进攻型阵型，强调边路突破',
    positions: [
      { id: 'gk', position: 'GK', x: 10, y: 50, heroId: null },
      { id: 'lb', position: 'DEF', x: 25, y: 15, heroId: null },
      { id: 'cb1', position: 'DEF', x: 25, y: 35, heroId: null },
      { id: 'cb2', position: 'DEF', x: 25, y: 65, heroId: null },
      { id: 'rb', position: 'DEF', x: 25, y: 85, heroId: null },
      { id: 'cm1', position: 'MID', x: 50, y: 35, heroId: null },
      { id: 'cm2', position: 'MID', x: 50, y: 50, heroId: null },
      { id: 'cm3', position: 'MID', x: 50, y: 65, heroId: null },
      { id: 'lw', position: 'ATT', x: 75, y: 25, heroId: null },
      { id: 'st', position: 'ATT', x: 75, y: 50, heroId: null },
      { id: 'rw', position: 'ATT', x: 75, y: 75, heroId: null }
    ]
  },
  {
    id: '352',
    name: '3-5-2',
    description: '中场控制型阵型',
    positions: [
      { id: 'gk', position: 'GK', x: 10, y: 50, heroId: null },
      { id: 'cb1', position: 'DEF', x: 25, y: 30, heroId: null },
      { id: 'cb2', position: 'DEF', x: 25, y: 50, heroId: null },
      { id: 'cb3', position: 'DEF', x: 25, y: 70, heroId: null },
      { id: 'lwb', position: 'MID', x: 45, y: 15, heroId: null },
      { id: 'cm1', position: 'MID', x: 50, y: 35, heroId: null },
      { id: 'cm2', position: 'MID', x: 50, y: 50, heroId: null },
      { id: 'cm3', position: 'MID', x: 50, y: 65, heroId: null },
      { id: 'rwb', position: 'MID', x: 45, y: 85, heroId: null },
      { id: 'st1', position: 'ATT', x: 75, y: 40, heroId: null },
      { id: 'st2', position: 'ATT', x: 75, y: 60, heroId: null }
    ]
  }
])

// 计算属性
const formationPositions = computed(() => currentFormation.value.positions)
const availableHeroes = computed(() => heroStore.getAvailableHeroes())

// 方法
const selectPosition = (position: FormationPosition) => {
  selectedPositionId.value = position.id
}

const getPositionStyle = (position: FormationPosition) => {
  return {
    left: `${position.x}%`,
    top: `${position.y}%`
  }
}

const getMiniPositionStyle = (position: FormationPosition) => {
  return {
    left: `${position.x * 0.8}%`,
    top: `${position.y * 0.6}%`
  }
}

const getPlayerName = (heroId: string | null): string => {
  if (!heroId) return ''
  const hero = heroStore.getHeroById(heroId)
  return hero?.name || ''
}

const getPlayerOverall = (heroId: string | null): number => {
  if (!heroId) return 0
  const hero = heroStore.getHeroById(heroId)
  return hero?.overall || 0
}

const handleDragStart = (event: DragEvent, position: FormationPosition | null, hero?: Hero) => {
  if (hero) {
    // 从替补席拖拽
    event.dataTransfer?.setData('heroId', hero.heroId)
    event.dataTransfer?.setData('source', 'bench')
  } else if (position?.heroId) {
    // 从阵型位置拖拽
    event.dataTransfer?.setData('heroId', position.heroId)
    event.dataTransfer?.setData('source', 'formation')
    event.dataTransfer?.setData('sourcePositionId', position.id)
  }
}

const handleDrop = (event: DragEvent, targetPosition: FormationPosition) => {
  event.preventDefault()
  
  const heroId = event.dataTransfer?.getData('heroId')
  const source = event.dataTransfer?.getData('source')
  const sourcePositionId = event.dataTransfer?.getData('sourcePositionId')
  
  if (!heroId) return
  
  const hero = heroStore.getHeroById(heroId)
  if (!hero) return
  
  // 检查位置适应性
  if (!isPositionCompatible(hero, targetPosition)) {
    globalStore.addNotification({
      type: 'warning',
      title: '位置不适合',
      message: `${hero.name} 不适合 ${targetPosition.position} 位置`
    })
    return
  }
  
  // 如果目标位置已有球员，交换位置
  if (targetPosition.heroId && source === 'formation' && sourcePositionId) {
    const sourcePosition = formationPositions.value.find(p => p.id === sourcePositionId)
    if (sourcePosition) {
      const tempHeroId = targetPosition.heroId
      targetPosition.heroId = heroId
      sourcePosition.heroId = tempHeroId
    }
  } else {
    // 清除原位置
    if (source === 'formation' && sourcePositionId) {
      const sourcePosition = formationPositions.value.find(p => p.id === sourcePositionId)
      if (sourcePosition) {
        sourcePosition.heroId = null
      }
    }
    
    // 设置新位置
    targetPosition.heroId = heroId
  }
}

const isPositionCompatible = (hero: Hero, position: FormationPosition): boolean => {
  // 简单的位置兼容性检查
  return hero.position === position.position
}

const applyFormation = () => {
  const selectedFormation = availableFormations.value.find(f => f.id === selectedFormationId.value)
  if (selectedFormation) {
    // 清除所有球员位置
    selectedFormation.positions.forEach(pos => {
      pos.heroId = null
    })
    
    currentFormation.value = { ...selectedFormation }
    showFormationSelector.value = false
    
    globalStore.addNotification({
      type: 'success',
      title: '阵型已更改',
      message: `已切换到 ${selectedFormation.name} 阵型`
    })
  }
}

const cancelFormationChange = () => {
  selectedFormationId.value = currentFormation.value.id
}

const saveFormation = async () => {
  try {
    isSaving.value = true
    
    // TODO: 调用保存阵型的API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    globalStore.addNotification({
      type: 'success',
      title: '保存成功',
      message: '战术配置已保存'
    })
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '保存失败',
      message: error.message
    })
  } finally {
    isSaving.value = false
  }
}

// 生命周期
onMounted(async () => {
  await heroStore.fetchHeroes()
})
</script>

<style lang="less" scoped>
.formations-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .formation-info {
        .current-formation {
          color: @text-color-secondary;
          font-size: @font-size-sm;
        }
      }
    }
  }
  
  .formation-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: @margin-lg;
    
    .football-field {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-base;
      
      .field-background {
        position: relative;
        width: 100%;
        height: 600px;
        background-color: #2d5a2d;
        border-radius: @border-radius-base;
        overflow: hidden;
        
        .field-lines {
          position: absolute;
          width: 100%;
          height: 100%;
          
          .center-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: white;
            transform: translateX(-50%);
          }
          
          .center-circle {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 120px;
            height: 120px;
            border: 2px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
          }
          
          .penalty-area {
            position: absolute;
            width: 120px;
            height: 200px;
            border: 2px solid white;
            top: 50%;
            transform: translateY(-50%);
            
            &.left {
              left: 0;
              border-right: none;
            }
            
            &.right {
              right: 0;
              border-left: none;
            }
          }
          
          .goal-area {
            position: absolute;
            width: 60px;
            height: 100px;
            border: 2px solid white;
            top: 50%;
            transform: translateY(-50%);
            
            &.left {
              left: 0;
              border-right: none;
            }
            
            &.right {
              right: 0;
              border-left: none;
            }
          }
        }
        
        .player-positions {
          position: relative;
          width: 100%;
          height: 100%;
          
          .position-slot {
            position: absolute;
            width: 60px;
            height: 60px;
            transform: translate(-50%, -50%);
            
            .player-card {
              width: 100%;
              height: 100%;
              background-color: @primary-color;
              border: 2px solid white;
              border-radius: 50%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all @transition-duration;
              
              &:hover {
                transform: scale(1.1);
              }
              
              &.selected {
                border-color: @warning-color;
                box-shadow: 0 0 10px @warning-color;
              }
              
              .player-name {
                font-size: @font-size-xs;
                color: white;
                font-weight: bold;
                text-align: center;
                line-height: 1;
              }
              
              .player-overall {
                font-size: @font-size-xs;
                color: white;
                font-weight: bold;
              }
              
              .player-position {
                font-size: @font-size-xs;
                color: rgba(255, 255, 255, 0.8);
              }
            }
            
            .empty-position {
              width: 100%;
              height: 100%;
              border: 2px dashed rgba(255, 255, 255, 0.5);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all @transition-duration;
              
              &:hover {
                border-color: @primary-color;
                background-color: rgba(82, 196, 26, 0.1);
              }
              
              .position-label {
                font-size: @font-size-xs;
                color: rgba(255, 255, 255, 0.7);
                font-weight: bold;
              }
            }
          }
        }
      }
    }
    
    .substitutes-section {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-base;
      
      .section-title {
        color: @text-color;
        margin: 0 0 @margin-base 0;
      }
      
      .substitutes-list {
        display: flex;
        flex-direction: column;
        gap: @margin-sm;
        max-height: 500px;
        overflow-y: auto;
        
        .substitute-card {
          background-color: rgba(0, 0, 0, 0.2);
          border: 1px solid @border-color;
          border-radius: @border-radius-base;
          padding: @padding-sm;
          cursor: grab;
          transition: all @transition-duration;
          
          &:hover {
            border-color: @primary-color;
            transform: translateX(5px);
          }
          
          &:active {
            cursor: grabbing;
          }
          
          .hero-name {
            color: @text-color;
            font-weight: bold;
            margin-bottom: @margin-xs;
          }
          
          .hero-overall {
            color: @primary-color;
            font-weight: bold;
          }
          
          .hero-position {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
      }
    }
  }
}

// 阵型选择模态框样式
.formation-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: @margin-base;
  
  .formation-option {
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    cursor: pointer;
    transition: all @transition-duration;
    
    &:hover {
      border-color: @primary-color;
    }
    
    &.selected {
      border-color: @primary-color;
      background-color: rgba(82, 196, 26, 0.1);
    }
    
    .formation-name {
      font-weight: bold;
      margin-bottom: @margin-xs;
    }
    
    .formation-description {
      font-size: @font-size-sm;
      color: @text-color-secondary;
      margin-bottom: @margin-sm;
    }
    
    .formation-preview {
      .mini-field {
        position: relative;
        width: 100%;
        height: 80px;
        background-color: #2d5a2d;
        border-radius: @border-radius-base;
        
        .mini-position {
          position: absolute;
          width: 12px;
          height: 12px;
          background-color: @primary-color;
          border-radius: 50%;
          font-size: 8px;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .formations-view {
    .formation-content {
      grid-template-columns: 1fr;
      
      .substitutes-section {
        .substitutes-list {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          max-height: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .formations-view {
    padding: @padding-base;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-base;
    }
    
    .football-field .field-background {
      height: 400px;
      
      .position-slot {
        width: 40px;
        height: 40px;
        
        .player-card {
          .player-name,
          .player-overall,
          .player-position {
            font-size: 8px;
          }
        }
      }
    }
  }
}
</style>
