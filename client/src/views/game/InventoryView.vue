<template>
  <div class="inventory-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">背包</h2>
        <div class="capacity-info">
          <span class="capacity-text">
            容量: {{ usedSlots }}/{{ totalCapacity }}
          </span>
          <a-progress
            :percent="usageRate"
            :stroke-color="getCapacityColor(usageRate)"
            size="small"
            :show-info="false"
            class="capacity-bar"
          />
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="expandInventory" :disabled="!canExpand">
            <PlusOutlined />
            扩展背包
          </a-button>
          <a-button @click="sortItems">
            <SortAscendingOutlined />
            整理背包
          </a-button>
          <a-button @click="refreshInventory">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 背包标签页 -->
    <div class="inventory-tabs">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane
          v-for="tab in inventoryTabs"
          :key="tab.bookMarkId"
          :tab="tab.name"
        >
          <!-- 搜索和筛选 -->
          <div class="tab-filters">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-input
                  v-model:value="searchText"
                  placeholder="搜索道具名称"
                  allow-clear
                  @change="handleSearch"
                >
                  <template #prefix>
                    <SearchOutlined />
                  </template>
                </a-input>
              </a-col>
              <a-col :span="6">
                <a-select
                  v-model:value="rarityFilter"
                  placeholder="稀有度筛选"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option value="">全部稀有度</a-select-option>
                  <a-select-option value="common">普通</a-select-option>
                  <a-select-option value="uncommon">优秀</a-select-option>
                  <a-select-option value="rare">稀有</a-select-option>
                  <a-select-option value="epic">史诗</a-select-option>
                  <a-select-option value="legendary">传奇</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-select
                  v-model:value="sortBy"
                  @change="handleSort"
                >
                  <a-select-option value="name">名称</a-select-option>
                  <a-select-option value="quantity">数量</a-select-option>
                  <a-select-option value="rarity">稀有度</a-select-option>
                  <a-select-option value="obtainTime">获得时间</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-checkbox v-model:checked="showOnlyUsable">
                  仅显示可用
                </a-checkbox>
              </a-col>
            </a-row>
          </div>

          <!-- 道具网格 -->
          <div class="items-grid">
            <div class="grid-container">
              <!-- 道具槽位 -->
              <div
                v-for="(item, index) in getTabItems(tab.bookMarkId)"
                :key="`${tab.bookMarkId}-${index}`"
                class="item-slot"
                :class="{
                  'has-item': item,
                  'selected': selectedItems.includes(item?.itemId),
                  'usable': item && canUseItem(item)
                }"
                @click="handleItemClick(item, index)"
                @contextmenu.prevent="handleItemRightClick(item, $event)"
              >
                <div v-if="item" class="item-content">
                  <!-- 道具图标 -->
                  <div class="item-icon" :class="`rarity-${item.rarity || 'common'}`">
                    <span class="item-symbol">{{ getItemSymbol(item.configId) }}</span>
                  </div>
                  
                  <!-- 道具数量 -->
                  <div v-if="item.quantity > 1" class="item-quantity">
                    {{ formatQuantity(item.quantity) }}
                  </div>
                  
                  <!-- 道具状态 -->
                  <div v-if="item.bind" class="item-bind">
                    绑定
                  </div>
                  
                  <!-- 冷却时间 -->
                  <div v-if="item.cooldown && item.cooldown > Date.now()" class="item-cooldown">
                    <a-progress
                      type="circle"
                      :percent="getCooldownPercent(item)"
                      :width="20"
                      :stroke-width="8"
                      :show-info="false"
                    />
                  </div>
                </div>
                
                <!-- 空槽位 -->
                <div v-else class="empty-slot">
                  <PlusOutlined />
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 道具详情面板 -->
    <div v-if="selectedItem" class="item-detail-panel">
      <ItemDetailPanel
        :item="selectedItem"
        :show-actions="true"
        @use-item="handleUseItem"
        @sell-item="handleSellItem"
        @drop-item="handleDropItem"
        @split-item="handleSplitItem"
      />
    </div>

    <!-- 批量操作工具栏 -->
    <div v-if="selectedItems.length > 0" class="batch-actions">
      <div class="selected-info">
        已选择 {{ selectedItems.length }} 个道具
      </div>
      <a-space>
        <a-button @click="batchUseItems" :disabled="!canBatchUse">
          批量使用
        </a-button>
        <a-button @click="batchSellItems">
          批量出售
        </a-button>
        <a-button @click="batchDropItems" danger>
          批量丢弃
        </a-button>
        <a-button @click="clearSelection">
          取消选择
        </a-button>
      </a-space>
    </div>

    <!-- 使用道具模态框 -->
    <UseItemModal
      v-model:visible="showUseModal"
      :item="itemToUse"
      @use-success="handleUseSuccess"
    />

    <!-- 分割道具模态框 -->
    <SplitItemModal
      v-model:visible="showSplitModal"
      :item="itemToSplit"
      @split-success="handleSplitSuccess"
    />

    <!-- 右键菜单 -->
    <a-dropdown
      v-model:open="showContextMenu"
      :trigger="['contextmenu']"
      :get-popup-container="() => $el"
    >
      <div></div>
      <template #overlay>
        <a-menu @click="handleContextMenuClick">
          <a-menu-item key="use" :disabled="!contextMenuItem || !canUseItem(contextMenuItem)">
            使用
          </a-menu-item>
          <a-menu-item key="detail">
            查看详情
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="split" :disabled="!contextMenuItem || contextMenuItem.quantity <= 1">
            分割
          </a-menu-item>
          <a-menu-item key="sell">
            出售
          </a-menu-item>
          <a-menu-item key="drop" danger>
            丢弃
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import {
  PlusOutlined,
  SortAscendingOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import type { InventoryItem, InventoryTab } from '@/types'
import { useInventoryStore } from '@/stores/inventory'
import { useGlobalStore } from '@/stores/global'
// 注意：以下组件需要创建
// import ItemDetailPanel from '@/components/game/ItemDetailPanel.vue'
// import UseItemModal from '@/components/game/UseItemModal.vue'
// import SplitItemModal from '@/components/game/SplitItemModal.vue'

// 状态
const inventoryStore = useInventoryStore()
const globalStore = useGlobalStore()

const loading = ref(false)
const activeTab = ref('1')
const searchText = ref('')
const rarityFilter = ref('')
const sortBy = ref('name')
const showOnlyUsable = ref(false)

// 选择状态
const selectedItems = ref<string[]>([])
const selectedItem = ref<InventoryItem | null>(null)

// 模态框状态
const showUseModal = ref(false)
const showSplitModal = ref(false)
const itemToUse = ref<InventoryItem | null>(null)
const itemToSplit = ref<InventoryItem | null>(null)

// 右键菜单状态
const showContextMenu = ref(false)
const contextMenuItem = ref<InventoryItem | null>(null)

// 计算属性
const inventory = computed(() => inventoryStore.inventory)
const inventoryTabs = computed(() => inventoryStore.inventoryTabs)
const usedSlots = computed(() => inventoryStore.usedSlots)
const totalCapacity = computed(() => inventoryStore.totalCapacity)
const usageRate = computed(() => inventoryStore.usageRate)
const canExpand = computed(() => inventoryStore.canExpand)

const canBatchUse = computed(() => {
  return selectedItems.value.some(itemId => {
    const item = inventoryStore.getItemById(itemId)
    return item && canUseItem(item)
  })
})

// 方法
const refreshInventory = async () => {
  loading.value = true
  try {
    await inventoryStore.fetchInventory()
  } finally {
    loading.value = false
  }
}

const expandInventory = async () => {
  try {
    await inventoryStore.expandInventory()
    globalStore.addNotification({
      type: 'success',
      title: '背包扩展成功',
      message: '背包容量已增加'
    })
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: '背包扩展失败',
      message: '背包扩展失败，请稍后重试'
    })
  }
}

const sortItems = async () => {
  try {
    await inventoryStore.sortItems()
    globalStore.addNotification({
      type: 'success',
      title: '背包整理完成',
      message: '道具已按类型和稀有度排序'
    })
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: '背包整理失败',
      message: '背包整理失败，请稍后重试'
    })
  }
}

const handleTabChange = (key: string) => {
  activeTab.value = key
  clearSelection()
}

const handleSearch = () => {
  // 搜索逻辑
}

const handleFilter = () => {
  // 筛选逻辑
}

const handleSort = () => {
  // 排序逻辑
}

const getTabItems = (bookMarkId: string) => {
  const tab = inventoryTabs.value.find(t => t.bookMarkId === bookMarkId)
  if (!tab) return []
  
  let items = tab.items || []
  
  // 搜索筛选
  if (searchText.value) {
    items = items.filter(item => 
      item && item.name?.toLowerCase().includes(searchText.value.toLowerCase())
    )
  }
  
  // 稀有度筛选
  if (rarityFilter.value) {
    items = items.filter(item => item && item.rarity === rarityFilter.value)
  }
  
  // 可用性筛选
  if (showOnlyUsable.value) {
    items = items.filter(item => item && canUseItem(item))
  }
  
  // 排序
  items.sort((a, b) => {
    if (!a && !b) return 0
    if (!a) return 1
    if (!b) return -1
    
    switch (sortBy.value) {
      case 'name':
        return (a.name || '').localeCompare(b.name || '')
      case 'quantity':
        return (b.quantity || 0) - (a.quantity || 0)
      case 'rarity':
        return getRarityOrder(b.rarity) - getRarityOrder(a.rarity)
      case 'obtainTime':
        return (b.obtainTime || 0) - (a.obtainTime || 0)
      default:
        return 0
    }
  })
  
  return items
}

const handleItemClick = (item: InventoryItem | null, index: number) => {
  if (!item) return
  
  if (selectedItems.value.includes(item.itemId)) {
    // 取消选择
    selectedItems.value = selectedItems.value.filter(id => id !== item.itemId)
    if (selectedItem.value?.itemId === item.itemId) {
      selectedItem.value = null
    }
  } else {
    // 选择道具
    selectedItems.value.push(item.itemId)
    selectedItem.value = item
  }
}

const handleItemRightClick = (item: InventoryItem | null, event: MouseEvent) => {
  if (!item) return
  
  contextMenuItem.value = item
  showContextMenu.value = true
  
  // 设置右键菜单位置
  nextTick(() => {
    const menu = document.querySelector('.ant-dropdown') as HTMLElement
    if (menu) {
      menu.style.left = `${event.clientX}px`
      menu.style.top = `${event.clientY}px`
    }
  })
}

const handleContextMenuClick = ({ key }: { key: string }) => {
  if (!contextMenuItem.value) return
  
  switch (key) {
    case 'use':
      handleUseItem(contextMenuItem.value)
      break
    case 'detail':
      selectedItem.value = contextMenuItem.value
      break
    case 'split':
      handleSplitItem(contextMenuItem.value)
      break
    case 'sell':
      handleSellItem(contextMenuItem.value)
      break
    case 'drop':
      handleDropItem(contextMenuItem.value)
      break
  }
  
  showContextMenu.value = false
  contextMenuItem.value = null
}

const handleUseItem = (item: InventoryItem) => {
  if (!canUseItem(item)) {
    globalStore.addNotification({
      type: 'warning',
      title: '无法使用',
      message: '该道具当前无法使用'
    })
    return
  }
  
  itemToUse.value = item
  showUseModal.value = true
}

const handleSellItem = async (item: InventoryItem) => {
  try {
    await inventoryStore.sellItem(item.itemId, 1)
    globalStore.addNotification({
      type: 'success',
      title: '出售成功',
      message: `已出售 ${item.name}`
    })
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: '出售失败',
      message: '道具出售失败，请稍后重试'
    })
  }
}

const handleDropItem = async (item: InventoryItem) => {
  try {
    await inventoryStore.dropItem(item.itemId, 1)
    globalStore.addNotification({
      type: 'success',
      title: '丢弃成功',
      message: `已丢弃 ${item.name}`
    })
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: '丢弃失败',
      message: '道具丢弃失败，请稍后重试'
    })
  }
}

const handleSplitItem = (item: InventoryItem) => {
  if (item.quantity <= 1) {
    globalStore.addNotification({
      type: 'warning',
      title: '无法分割',
      message: '该道具数量不足，无法分割'
    })
    return
  }
  
  itemToSplit.value = item
  showSplitModal.value = true
}

const batchUseItems = async () => {
  // 批量使用道具逻辑
  globalStore.addNotification({
    type: 'info',
    title: '批量使用',
    message: '正在批量使用选中的道具...'
  })
}

const batchSellItems = async () => {
  // 批量出售道具逻辑
  globalStore.addNotification({
    type: 'info',
    title: '批量出售',
    message: '正在批量出售选中的道具...'
  })
}

const batchDropItems = async () => {
  // 批量丢弃道具逻辑
  globalStore.addNotification({
    type: 'warning',
    title: '批量丢弃',
    message: '正在批量丢弃选中的道具...'
  })
}

const clearSelection = () => {
  selectedItems.value = []
  selectedItem.value = null
}

const handleUseSuccess = (result: any) => {
  globalStore.addNotification({
    type: 'success',
    title: '使用成功',
    message: result.message || '道具使用成功'
  })
  refreshInventory()
}

const handleSplitSuccess = () => {
  globalStore.addNotification({
    type: 'success',
    title: '分割成功',
    message: '道具分割成功'
  })
  refreshInventory()
}

// 工具函数
const canUseItem = (item: InventoryItem): boolean => {
  if (!item) return false
  if (item.cooldown && item.cooldown > Date.now()) return false
  // 其他使用条件检查
  return true
}

const getItemSymbol = (configId: number): string => {
  // 根据配置ID返回道具符号
  const symbols: Record<number, string> = {
    1001: '⚔️', // 武器
    1002: '🛡️', // 防具
    1003: '💊', // 药品
    1004: '📜', // 卷轴
    1005: '💎', // 宝石
  }
  return symbols[configId] || '📦'
}

const formatQuantity = (quantity: number): string => {
  if (quantity >= 1000000) {
    return `${(quantity / 1000000).toFixed(1)}M`
  } else if (quantity >= 1000) {
    return `${(quantity / 1000).toFixed(1)}K`
  }
  return quantity.toString()
}

const getCooldownPercent = (item: InventoryItem): number => {
  if (!item.cooldown) return 0
  const now = Date.now()
  const total = item.cooldown - (item.lastUseTime || 0)
  const remaining = item.cooldown - now
  return Math.max(0, Math.min(100, (remaining / total) * 100))
}

const getCapacityColor = (percent: number): string => {
  if (percent >= 90) return '#ff4d4f'
  if (percent >= 70) return '#faad14'
  return '#52c41a'
}

const getRarityOrder = (rarity?: string): number => {
  const orders: Record<string, number> = {
    common: 1,
    uncommon: 2,
    rare: 3,
    epic: 4,
    legendary: 5
  }
  return orders[rarity || 'common'] || 0
}

// 生命周期
onMounted(() => {
  refreshInventory()
})
</script>

<style lang="less" scoped>
.inventory-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .capacity-info {
        display: flex;
        align-items: center;
        gap: @margin-sm;
        
        .capacity-text {
          color: @text-color-secondary;
          font-size: @font-size-sm;
        }
        
        .capacity-bar {
          width: 200px;
        }
      }
    }
  }
  
  .inventory-tabs {
    background-color: @card-bg;
    border-radius: @border-radius-base;
    border: 1px solid @border-color;
    
    :deep(.ant-tabs) {
      .ant-tabs-tab {
        color: @text-color-secondary;
        
        &.ant-tabs-tab-active {
          color: @primary-color;
        }
      }
      
      .ant-tabs-content-holder {
        padding: @padding-base;
      }
    }
    
    .tab-filters {
      margin-bottom: @margin-base;
      padding: @padding-base;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: @border-radius-base;
    }
    
    .items-grid {
      .grid-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, 64px);
        gap: 4px;
        justify-content: start;
      }
      
      .item-slot {
        width: 64px;
        height: 64px;
        border: 2px solid @border-color;
        border-radius: @border-radius-base;
        position: relative;
        cursor: pointer;
        transition: all @transition-duration;
        
        &:hover {
          border-color: @primary-color;
        }
        
        &.selected {
          border-color: @primary-color;
          box-shadow: 0 0 8px rgba(82, 196, 26, 0.5);
        }
        
        &.usable {
          .item-icon {
            animation: pulse 2s infinite;
          }
        }
        
        .item-content {
          width: 100%;
          height: 100%;
          position: relative;
          
          .item-icon {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            
            &.rarity-common { background-color: rgba(255, 255, 255, 0.1); }
            &.rarity-uncommon { background-color: rgba(30, 255, 0, 0.1); }
            &.rarity-rare { background-color: rgba(0, 112, 221, 0.1); }
            &.rarity-epic { background-color: rgba(163, 53, 238, 0.1); }
            &.rarity-legendary { background-color: rgba(255, 128, 0, 0.1); }
          }
          
          .item-quantity {
            position: absolute;
            bottom: 2px;
            right: 2px;
            background-color: rgba(0, 0, 0, 0.8);
            color: @text-color;
            font-size: @font-size-xs;
            padding: 1px 4px;
            border-radius: 2px;
          }
          
          .item-bind {
            position: absolute;
            top: 2px;
            left: 2px;
            background-color: @error-color;
            color: @text-color;
            font-size: @font-size-xs;
            padding: 1px 3px;
            border-radius: 2px;
          }
          
          .item-cooldown {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
        
        .empty-slot {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: @text-color-secondary;
          opacity: 0.3;
        }
      }
    }
  }
  
  .item-detail-panel {
    margin-top: @margin-lg;
  }
  
  .batch-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    display: flex;
    align-items: center;
    gap: @margin-base;
    box-shadow: @box-shadow-elevated;
    z-index: 1000;
    
    .selected-info {
      color: @text-color;
      font-weight: bold;
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

// 响应式设计
@media (max-width: 768px) {
  .inventory-view {
    padding: @padding-base;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-base;
    }
    
    .items-grid .grid-container {
      grid-template-columns: repeat(auto-fill, 56px);
      
      .item-slot {
        width: 56px;
        height: 56px;
      }
    }
    
    .batch-actions {
      position: relative;
      bottom: auto;
      left: auto;
      transform: none;
      margin-top: @margin-lg;
      flex-direction: column;
      align-items: stretch;
    }
  }
}
</style>
