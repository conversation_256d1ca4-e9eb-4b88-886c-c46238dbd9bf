<template>
  <div class="hero-detail-view">
    <div class="page-header">
      <div class="header-left">
        <a-button @click="goBack" type="text">
          <ArrowLeftOutlined />
          返回
        </a-button>
        <h2 v-if="hero" class="page-title">{{ hero.name }}</h2>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="refreshHero">
            <ReloadOutlined />
            刷新
          </a-button>
          <a-button v-if="hero" @click="showTrainModal = true" type="primary">
            <ThunderboltOutlined />
            训练
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="hero" class="hero-content">
      <!-- 基本信息 -->
      <div class="hero-basic-info">
        <div class="hero-avatar">
          <div class="avatar-placeholder">
            <span class="hero-symbol">{{ getHeroSymbol(hero.position) }}</span>
          </div>
          <div class="hero-quality" :class="`quality-${hero.quality}`">
            {{ getQualityText(hero.quality) }}
          </div>
        </div>
        
        <div class="hero-info">
          <h3 class="hero-name">{{ hero.name }}</h3>
          <div class="hero-meta">
            <div class="meta-item">
              <span class="label">位置:</span>
              <span class="value">{{ hero.position }}</span>
            </div>
            <div class="meta-item">
              <span class="label">等级:</span>
              <span class="value">{{ hero.level }}</span>
            </div>
            <div class="meta-item">
              <span class="label">总评:</span>
              <span class="value overall">{{ hero.overall }}</span>
            </div>
            <div class="meta-item">
              <span class="label">年龄:</span>
              <span class="value">{{ hero.age || 'N/A' }}</span>
            </div>
            <div class="meta-item">
              <span class="label">国籍:</span>
              <span class="value">{{ hero.nationality || 'N/A' }}</span>
            </div>
            <div class="meta-item">
              <span class="label">状态:</span>
              <span class="value" :class="`status-${hero.status}`">
                {{ getStatusText(hero.status || 'normal') }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 属性面板 -->
      <div class="hero-attributes">
        <h4 class="section-title">球员属性</h4>
        <div class="attributes-grid">
          <div class="attribute-item">
            <div class="attribute-name">速度</div>
            <div class="attribute-bar">
              <div class="bar-bg">
                <div 
                  class="bar-fill" 
                  :style="{ width: hero.attributes.pace + '%' }"
                ></div>
              </div>
              <div class="attribute-value">{{ hero.attributes.pace }}</div>
            </div>
          </div>
          
          <div class="attribute-item">
            <div class="attribute-name">射门</div>
            <div class="attribute-bar">
              <div class="bar-bg">
                <div 
                  class="bar-fill" 
                  :style="{ width: hero.attributes.shooting + '%' }"
                ></div>
              </div>
              <div class="attribute-value">{{ hero.attributes.shooting }}</div>
            </div>
          </div>
          
          <div class="attribute-item">
            <div class="attribute-name">传球</div>
            <div class="attribute-bar">
              <div class="bar-bg">
                <div 
                  class="bar-fill" 
                  :style="{ width: hero.attributes.passing + '%' }"
                ></div>
              </div>
              <div class="attribute-value">{{ hero.attributes.passing }}</div>
            </div>
          </div>
          
          <div class="attribute-item">
            <div class="attribute-name">盘带</div>
            <div class="attribute-bar">
              <div class="bar-bg">
                <div 
                  class="bar-fill" 
                  :style="{ width: hero.attributes.dribbling + '%' }"
                ></div>
              </div>
              <div class="attribute-value">{{ hero.attributes.dribbling }}</div>
            </div>
          </div>
          
          <div class="attribute-item">
            <div class="attribute-name">防守</div>
            <div class="attribute-bar">
              <div class="bar-bg">
                <div 
                  class="bar-fill" 
                  :style="{ width: hero.attributes.defending + '%' }"
                ></div>
              </div>
              <div class="attribute-value">{{ hero.attributes.defending }}</div>
            </div>
          </div>
          
          <div class="attribute-item">
            <div class="attribute-name">体能</div>
            <div class="attribute-bar">
              <div class="bar-bg">
                <div 
                  class="bar-fill" 
                  :style="{ width: hero.attributes.physical + '%' }"
                ></div>
              </div>
              <div class="attribute-value">{{ hero.attributes.physical }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 技能列表 -->
      <div v-if="hero.skills && hero.skills.length > 0" class="hero-skills">
        <h4 class="section-title">球员技能</h4>
        <div class="skills-list">
          <div
            v-for="skill in hero.skills"
            :key="skill.id"
            class="skill-item"
          >
            <div class="skill-info">
              <div class="skill-name">{{ skill.name }}</div>
              <div class="skill-description">{{ skill.description }}</div>
            </div>
            <div class="skill-level">
              <div class="level-text">Lv.{{ skill.level }}</div>
              <div class="level-progress">
                <div 
                  class="progress-fill" 
                  :style="{ width: (skill.level / skill.maxLevel) * 100 + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 训练信息 -->
      <div v-if="hero.training" class="hero-training">
        <h4 class="section-title">训练信息</h4>
        <div class="training-info">
          <div class="info-item">
            <span class="label">训练次数:</span>
            <span class="value">{{ hero.training.trainingCount || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">最后训练:</span>
            <span class="value">
              {{ hero.training.lastTrainingTime ? formatTime(hero.training.lastTrainingTime) : '从未训练' }}
            </span>
          </div>
          <div v-if="hero.training.trainingCooldown" class="info-item">
            <span class="label">冷却时间:</span>
            <span class="value">{{ getCooldownText(hero.training.trainingCooldown) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="error-container">
      <a-result
        status="404"
        title="球员不存在"
        sub-title="找不到指定的球员信息"
      >
        <template #extra>
          <a-button type="primary" @click="goBack">返回球员列表</a-button>
        </template>
      </a-result>
    </div>

    <!-- 训练模态框 -->
    <a-modal
      v-model:open="showTrainModal"
      title="训练球员"
      @ok="confirmTraining"
      @cancel="cancelTraining"
      :confirm-loading="isTraining"
    >
      <div v-if="hero" class="train-form">
        <div class="hero-preview">
          <div class="hero-name">{{ hero.name }}</div>
          <div class="hero-overall">总评: {{ hero.overall }}</div>
        </div>
        
        <div class="form-item">
          <label>训练类型:</label>
          <a-select v-model:value="trainForm.type" style="width: 100%">
            <a-select-option value="basic">基础训练</a-select-option>
            <a-select-option value="technical">技术训练</a-select-option>
            <a-select-option value="physical">体能训练</a-select-option>
          </a-select>
        </div>
        
        <div class="form-item">
          <label>训练次数:</label>
          <a-input-number
            v-model:value="trainForm.count"
            :min="1"
            :max="10"
            style="width: 100%"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue'
import { useHeroStore } from '@/stores/hero'
import { useGlobalStore } from '@/stores/global'
import type { Hero } from '@/types'

// 路由
const route = useRoute()
const router = useRouter()

// 状态
const heroStore = useHeroStore()
const globalStore = useGlobalStore()

const isLoading = ref(false)
const isTraining = ref(false)
const showTrainModal = ref(false)
const hero = ref<Hero | null>(null)

// 训练表单
const trainForm = reactive({
  type: 'basic',
  count: 1
})

// 方法
const loadHero = async () => {
  const heroId = route.params.id as string
  if (!heroId) return
  
  try {
    isLoading.value = true
    hero.value = await heroStore.fetchHeroDetail(heroId)
  } catch (error: any) {
    await globalStore.addNotification({
      type: 'error',
      title: '加载失败',
      message: error.message
    })
  } finally {
    isLoading.value = false
  }
}

const refreshHero = () => {
  loadHero()
}

const goBack = () => {
  router.back()
}

const confirmTraining = async () => {
  if (!hero.value) return
  
  try {
    isTraining.value = true
    
    const result = await heroStore.trainHero({
      heroId: hero.value.heroId,
      trainType: 1, // 简化处理
      trainCount: trainForm.count
    })
    
    if (result) {
      await globalStore.addNotification({
        type: 'success',
        title: '训练完成',
        message: `${hero.value.name} 训练完成`
      })
      
      // 刷新球员数据
      await loadHero()
    }
    
    showTrainModal.value = false
  } catch (error: any) {
    await globalStore.addNotification({
      type: 'error',
      title: '训练失败',
      message: error.message
    })
  } finally {
    isTraining.value = false
  }
}

const cancelTraining = () => {
  trainForm.type = 'basic'
  trainForm.count = 1
}

// 工具函数
const getHeroSymbol = (position: string): string => {
  const symbols: Record<string, string> = {
    GK: '🥅',
    DEF: '🛡️',
    MID: '⚽',
    ATT: '⚡'
  }
  return symbols[position] || '👤'
}

const getQualityText = (quality: number): string => {
  const qualities = ['', '普通', '优秀', '稀有', '史诗', '传奇']
  return qualities[quality] || '未知'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: '正常',
    injured: '受伤',
    suspended: '停赛',
    retired: '退役'
  }
  return statusMap[status] || status
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

const getCooldownText = (cooldown: number): string => {
  const now = Date.now()
  const remaining = cooldown - now
  
  if (remaining <= 0) return '已完成'
  
  const hours = Math.floor(remaining / (1000 * 60 * 60))
  const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))
  
  return `${hours}小时${minutes}分钟`
}

// 生命周期
onMounted(() => {
  loadHero()
})
</script>

<style lang="less" scoped>
.hero-detail-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: @margin-base;
      
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0;
      }
    }
  }
  
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }
  
  .hero-content {
    display: grid;
    gap: @margin-lg;
    
    .hero-basic-info {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      display: flex;
      gap: @margin-lg;
      
      .hero-avatar {
        position: relative;
        
        .avatar-placeholder {
          width: 120px;
          height: 120px;
          background-color: @primary-color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 3rem;
        }
        
        .hero-quality {
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          padding: 4px 12px;
          border-radius: 12px;
          font-size: @font-size-xs;
          font-weight: bold;
          color: white;
          
          &.quality-1 { background-color: @text-color-secondary; }
          &.quality-2 { background-color: @success-color; }
          &.quality-3 { background-color: @info-color; }
          &.quality-4 { background-color: @warning-color; }
          &.quality-5 { background-color: @error-color; }
        }
      }
      
      .hero-info {
        flex: 1;
        
        .hero-name {
          color: @text-color;
          margin-bottom: @margin-base;
        }
        
        .hero-meta {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: @margin-sm;
          
          .meta-item {
            display: flex;
            justify-content: space-between;
            
            .label {
              color: @text-color-secondary;
            }
            
            .value {
              color: @text-color;
              font-weight: bold;
              
              &.overall {
                color: @primary-color;
                font-size: @font-size-lg;
              }
              
              &.status-active { color: @success-color; }
              &.status-injured { color: @error-color; }
              &.status-suspended { color: @warning-color; }
            }
          }
        }
      }
    }
    
    .hero-attributes {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      
      .section-title {
        color: @text-color;
        margin-bottom: @margin-base;
      }
      
      .attributes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: @margin-base;
        
        .attribute-item {
          .attribute-name {
            color: @text-color-secondary;
            margin-bottom: @margin-xs;
          }
          
          .attribute-bar {
            display: flex;
            align-items: center;
            gap: @margin-sm;
            
            .bar-bg {
              flex: 1;
              height: 8px;
              background-color: @border-color;
              border-radius: 4px;
              overflow: hidden;
              
              .bar-fill {
                height: 100%;
                background-color: @primary-color;
                transition: width @transition-duration;
              }
            }
            
            .attribute-value {
              color: @text-color;
              font-weight: bold;
              min-width: 30px;
              text-align: right;
            }
          }
        }
      }
    }
    
    .hero-skills {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      
      .section-title {
        color: @text-color;
        margin-bottom: @margin-base;
      }
      
      .skills-list {
        display: grid;
        gap: @margin-base;
        
        .skill-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: @padding-base;
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: @border-radius-base;
          
          .skill-info {
            flex: 1;
            
            .skill-name {
              color: @text-color;
              font-weight: bold;
              margin-bottom: @margin-xs;
            }
            
            .skill-description {
              color: @text-color-secondary;
              font-size: @font-size-sm;
            }
          }
          
          .skill-level {
            text-align: center;
            
            .level-text {
              color: @primary-color;
              font-weight: bold;
              margin-bottom: @margin-xs;
            }
            
            .level-progress {
              width: 60px;
              height: 4px;
              background-color: @border-color;
              border-radius: 2px;
              overflow: hidden;
              
              .progress-fill {
                height: 100%;
                background-color: @primary-color;
                transition: width @transition-duration;
              }
            }
          }
        }
      }
    }
    
    .hero-training {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      
      .section-title {
        color: @text-color;
        margin-bottom: @margin-base;
      }
      
      .training-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: @margin-base;
        
        .info-item {
          display: flex;
          justify-content: space-between;
          
          .label {
            color: @text-color-secondary;
          }
          
          .value {
            color: @text-color;
            font-weight: bold;
          }
        }
      }
    }
  }
}

// 训练模态框样式
.train-form {
  .hero-preview {
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    margin-bottom: @margin-base;
    text-align: center;
    
    .hero-name {
      color: @text-color;
      font-weight: bold;
      margin-bottom: @margin-xs;
    }
    
    .hero-overall {
      color: @primary-color;
    }
  }
  
  .form-item {
    margin-bottom: @margin-base;
    
    label {
      display: block;
      color: @text-color;
      margin-bottom: @margin-xs;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hero-detail-view {
    padding: @padding-base;
    
    .hero-basic-info {
      flex-direction: column;
      text-align: center;
    }
    
    .attributes-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
