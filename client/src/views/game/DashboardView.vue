<template>
  <div class="dashboard-view">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <h1 class="welcome-title">欢迎回来，{{ characterName }}！</h1>
        <p class="welcome-subtitle">{{ getWelcomeMessage() }}</p>
      </div>
      <div class="banner-stats">
        <div class="stat-card">
          <div class="stat-value">{{ teamOverall }}</div>
          <div class="stat-label">球队总评</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ winRate }}%</div>
          <div class="stat-label">胜率</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ currentLeagueRank }}</div>
          <div class="stat-label">联赛排名</div>
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <h2 class="section-title">快速操作</h2>
      <div class="quick-actions-grid">
        <div class="action-card" @click="$router.push('/game/matches')">
          <div class="action-icon">⚽</div>
          <div class="action-title">快速比赛</div>
          <div class="action-desc">开始一场快速比赛</div>
        </div>
        
        <div class="action-card" @click="$router.push('/game/heroes')">
          <div class="action-icon">👥</div>
          <div class="action-title">球员管理</div>
          <div class="action-desc">查看和管理球员</div>
        </div>
        
        <div class="action-card" @click="$router.push('/game/training')">
          <div class="action-icon">🏋️</div>
          <div class="action-title">球员训练</div>
          <div class="action-desc">提升球员能力</div>
        </div>
        
        <div class="action-card" @click="$router.push('/game/market')">
          <div class="action-icon">🏪</div>
          <div class="action-title">转会市场</div>
          <div class="action-desc">买卖球员</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧内容 -->
      <div class="left-content">
        <!-- 球队状态 -->
        <div class="team-status-card">
          <h3 class="card-title">球队状态</h3>
          <div class="team-formation">
            <div class="formation-title">当前阵型: {{ currentFormation }}</div>
            <div class="formation-grid">
              <div
                v-for="position in formationPositions"
                :key="position.id"
                class="position-slot"
                :class="{ 'has-player': position.heroId }"
              >
                <div v-if="position.heroId" class="player-info">
                  <div class="player-name">{{ getPlayerName(position.heroId) }}</div>
                  <div class="player-overall">{{ getPlayerOverall(position.heroId) }}</div>
                </div>
                <div v-else class="empty-position">
                  <span>{{ position.position }}</span>
                </div>
              </div>
            </div>
            <div class="formation-actions">
              <a-button @click="$router.push('/game/formations')">
                调整阵容
              </a-button>
            </div>
          </div>
        </div>

        <!-- 最近比赛 -->
        <div class="recent-matches-card">
          <h3 class="card-title">最近比赛</h3>
          <div class="matches-list">
            <div
              v-for="match in recentMatches"
              :key="match.id"
              class="match-item"
              :class="getMatchResultClass(match)"
            >
              <div class="match-teams">
                <span class="home-team">{{ match.homeTeam }}</span>
                <span class="match-score">{{ match.homeScore }} - {{ match.awayScore }}</span>
                <span class="away-team">{{ match.awayTeam }}</span>
              </div>
              <div class="match-info">
                <span class="match-date">{{ formatDate(match.startTime) }}</span>
                <span class="match-result">{{ getMatchResultText(match) }}</span>
              </div>
            </div>
          </div>
          <div class="matches-actions">
            <a-button @click="$router.push('/game/matches')">
              查看更多
            </a-button>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <!-- 每日任务 -->
        <div class="daily-tasks-card">
          <h3 class="card-title">每日任务</h3>
          <div class="tasks-list">
            <div
              v-for="task in dailyTasks"
              :key="task.id"
              class="task-item"
              :class="{ 'completed': task.completed }"
            >
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-progress">
                  <a-progress
                    :percent="getTaskProgress(task)"
                    size="small"
                    :show-info="false"
                  />
                  <span class="progress-text">{{ task.current }}/{{ task.target }}</span>
                </div>
              </div>
              <div class="task-reward">
                <span class="reward-icon">{{ task.reward.icon }}</span>
                <span class="reward-amount">{{ task.reward.amount }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知消息 -->
        <div class="notifications-card">
          <h3 class="card-title">最新消息</h3>
          <div class="notifications-list">
            <div
              v-for="notification in recentNotifications"
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.read }"
            >
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.message }}</div>
                <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
              </div>
            </div>
          </div>
          <div class="notifications-actions">
            <a-button @click="showAllNotifications">
              查看全部
            </a-button>
          </div>
        </div>

        <!-- 资源状态 -->
        <div class="resources-card">
          <h3 class="card-title">资源状态</h3>
          <div class="resources-list">
            <div class="resource-item">
              <div class="resource-icon">💰</div>
              <div class="resource-info">
                <div class="resource-name">金币</div>
                <div class="resource-amount">{{ formatNumber(resources.coins) }}</div>
              </div>
            </div>
            <div class="resource-item">
              <div class="resource-icon">💎</div>
              <div class="resource-info">
                <div class="resource-name">钻石</div>
                <div class="resource-amount">{{ formatNumber(resources.gems) }}</div>
              </div>
            </div>
            <div class="resource-item">
              <div class="resource-icon">⚡</div>
              <div class="resource-info">
                <div class="resource-name">体力</div>
                <div class="resource-amount">{{ resources.energy }}/{{ resources.maxEnergy }}</div>
              </div>
              <div class="resource-progress">
                <a-progress
                  :percent="(resources.energy / resources.maxEnergy) * 100"
                  size="small"
                  :show-info="false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useHeroStore } from '@/stores/hero'
import { useMatchStore } from '@/stores/match'
import { useGlobalStore } from '@/stores/global'
import type { Match, Hero, DailyTask, GameNotification } from '@/types'

// 状态
const authStore = useAuthStore()
const heroStore = useHeroStore()
const matchStore = useMatchStore()
const globalStore = useGlobalStore()

// 数据
const dailyTasks = ref<DailyTask[]>([
  {
    id: '1',
    name: '完成3场比赛',
    current: 1,
    target: 3,
    completed: false,
    reward: { icon: '💰', amount: 1000 }
  },
  {
    id: '2',
    name: '训练球员5次',
    current: 3,
    target: 5,
    completed: false,
    reward: { icon: '⭐', amount: 100 }
  },
  {
    id: '3',
    name: '登录游戏',
    current: 1,
    target: 1,
    completed: true,
    reward: { icon: '💎', amount: 10 }
  }
])

const resources = ref({
  coins: 125000,
  gems: 250,
  energy: 80,
  maxEnergy: 100
})

// 计算属性
const characterName = computed(() => authStore.selectedCharacter?.name || '教练')
const teamOverall = computed(() => {
  const mainTeam = heroStore.mainTeamHeroes
  if (mainTeam.length === 0) return 0
  const total = mainTeam.reduce((sum, hero) => sum + hero.overall, 0)
  return Math.round(total / mainTeam.length)
})
const winRate = computed(() => matchStore.winRate)
const currentLeagueRank = computed(() => 15) // 模拟数据
const currentFormation = computed(() => '4-4-2') // 模拟数据
const formationPositions = computed(() => [
  { id: 1, position: 'GK', heroId: null },
  { id: 2, position: 'DEF', heroId: 'hero1' },
  { id: 3, position: 'DEF', heroId: 'hero2' },
  { id: 4, position: 'MID', heroId: null },
  { id: 5, position: 'ATT', heroId: 'hero3' }
])
const recentMatches = computed(() => matchStore.matches.slice(0, 5))
const recentNotifications = computed(() => globalStore.notifications.slice(0, 5))

// 方法
const getWelcomeMessage = (): string => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好！准备开始新的一天吗？'
  if (hour < 18) return '下午好！球队训练进展如何？'
  return '晚上好！今天的比赛表现不错！'
}

const getPlayerName = (heroId: string): string => {
  const hero = heroStore.getHeroById(heroId)
  return hero?.name || '未知球员'
}

const getPlayerOverall = (heroId: string): number => {
  const hero = heroStore.getHeroById(heroId)
  return hero?.overall || 0
}

const getMatchResultClass = (match: Match): string => {
  if (match.homeScore > match.awayScore) return 'win'
  if (match.homeScore < match.awayScore) return 'loss'
  return 'draw'
}

const getMatchResultText = (match: Match): string => {
  if (match.homeScore > match.awayScore) return '胜'
  if (match.homeScore < match.awayScore) return '负'
  return '平'
}

const getTaskProgress = (task: DailyTask): number => {
  return Math.min((task.current / task.target) * 100, 100)
}

const formatDate = (timestamp: string): string => {
  return new Date(timestamp).toLocaleDateString()
}

const formatTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString()
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
  return num.toString()
}

const showAllNotifications = () => {
  // 显示所有通知的逻辑
  globalStore.addNotification({
    type: 'info',
    title: '通知中心',
    message: '查看所有通知消息'
  })
}

// 生命周期
onMounted(async () => {
  // 加载仪表板数据
  try {
    await Promise.all([
      heroStore.fetchHeroes(),
      matchStore.fetchMatches()
    ])
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
})
</script>

<style lang="less" scoped>
.dashboard-view {
  padding: @padding-lg;
  
  .welcome-banner {
    background: linear-gradient(135deg, @card-bg 0%, rgba(82, 196, 26, 0.1) 100%);
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-lg;
    margin-bottom: @margin-lg;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .banner-content {
      .welcome-title {
        font-size: @font-size-xxl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .welcome-subtitle {
        color: @text-color-secondary;
        margin: 0;
      }
    }
    
    .banner-stats {
      display: flex;
      gap: @margin-lg;
      
      .stat-card {
        text-align: center;
        
        .stat-value {
          font-size: @font-size-xl;
          font-weight: bold;
          color: @primary-color;
        }
        
        .stat-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }
  
  .quick-actions-section {
    margin-bottom: @margin-lg;
    
    .section-title {
      color: @text-color;
      margin-bottom: @margin-base;
    }
    
    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: @margin-base;
      
      .action-card {
        background-color: @card-bg;
        border: 1px solid @border-color;
        border-radius: @border-radius-base;
        padding: @padding-base;
        text-align: center;
        cursor: pointer;
        transition: all @transition-duration;
        
        &:hover {
          border-color: @primary-color;
          transform: translateY(-2px);
        }
        
        .action-icon {
          font-size: 2rem;
          margin-bottom: @margin-sm;
        }
        
        .action-title {
          font-weight: bold;
          color: @text-color;
          margin-bottom: @margin-xs;
        }
        
        .action-desc {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }
  
  .main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: @margin-lg;
    
    .left-content,
    .right-content {
      display: flex;
      flex-direction: column;
      gap: @margin-base;
    }
  }
  
  // 卡片通用样式
  .team-status-card,
  .recent-matches-card,
  .daily-tasks-card,
  .notifications-card,
  .resources-card {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    
    .card-title {
      color: @text-color;
      margin: 0 0 @margin-base 0;
      font-size: @font-size-lg;
    }
  }
  
  // 球队状态卡片
  .team-status-card {
    .formation-title {
      color: @text-color;
      margin-bottom: @margin-sm;
      font-weight: bold;
    }
    
    .formation-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: @margin-xs;
      margin-bottom: @margin-base;
      
      .position-slot {
        aspect-ratio: 1;
        border: 1px solid @border-color;
        border-radius: @border-radius-base;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: @font-size-xs;
        
        &.has-player {
          background-color: rgba(82, 196, 26, 0.1);
          border-color: @primary-color;
        }
        
        .player-info {
          text-align: center;
          
          .player-name {
            color: @text-color;
            font-weight: bold;
          }
          
          .player-overall {
            color: @primary-color;
          }
        }
        
        .empty-position {
          color: @text-color-secondary;
        }
      }
    }
  }
  
  // 最近比赛卡片
  .recent-matches-card {
    .matches-list {
      .match-item {
        padding: @padding-sm;
        border-bottom: 1px solid @border-color;
        
        &:last-child {
          border-bottom: none;
        }
        
        &.win {
          border-left: 3px solid @success-color;
        }
        
        &.loss {
          border-left: 3px solid @error-color;
        }
        
        &.draw {
          border-left: 3px solid @warning-color;
        }
        
        .match-teams {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: @margin-xs;
          
          .home-team,
          .away-team {
            color: @text-color;
            font-weight: bold;
          }
          
          .match-score {
            color: @primary-color;
            font-weight: bold;
          }
        }
        
        .match-info {
          display: flex;
          justify-content: space-between;
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }
  
  // 每日任务卡片
  .daily-tasks-card {
    .tasks-list {
      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: @padding-sm;
        border-bottom: 1px solid @border-color;
        
        &:last-child {
          border-bottom: none;
        }
        
        &.completed {
          opacity: 0.6;
          
          .task-name {
            text-decoration: line-through;
          }
        }
        
        .task-info {
          flex: 1;
          
          .task-name {
            color: @text-color;
            margin-bottom: @margin-xs;
          }
          
          .task-progress {
            display: flex;
            align-items: center;
            gap: @margin-xs;
            
            .progress-text {
              font-size: @font-size-sm;
              color: @text-color-secondary;
            }
          }
        }
        
        .task-reward {
          display: flex;
          align-items: center;
          gap: @margin-xs;
          
          .reward-amount {
            color: @primary-color;
            font-weight: bold;
          }
        }
      }
    }
  }
  
  // 通知卡片
  .notifications-card {
    .notifications-list {
      .notification-item {
        padding: @padding-sm;
        border-bottom: 1px solid @border-color;
        
        &:last-child {
          border-bottom: none;
        }
        
        &.unread {
          background-color: rgba(82, 196, 26, 0.05);
          border-left: 3px solid @primary-color;
        }
        
        .notification-title {
          color: @text-color;
          font-weight: bold;
          margin-bottom: @margin-xs;
        }
        
        .notification-message {
          color: @text-color-secondary;
          font-size: @font-size-sm;
          margin-bottom: @margin-xs;
        }
        
        .notification-time {
          color: @text-color-secondary;
          font-size: @font-size-xs;
        }
      }
    }
  }
  
  // 资源卡片
  .resources-card {
    .resources-list {
      .resource-item {
        display: flex;
        align-items: center;
        gap: @margin-sm;
        padding: @padding-sm 0;
        border-bottom: 1px solid @border-color;
        
        &:last-child {
          border-bottom: none;
        }
        
        .resource-icon {
          font-size: @font-size-lg;
        }
        
        .resource-info {
          flex: 1;
          
          .resource-name {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
          
          .resource-amount {
            color: @text-color;
            font-weight: bold;
          }
        }
        
        .resource-progress {
          width: 60px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-view {
    padding: @padding-base;
    
    .welcome-banner {
      flex-direction: column;
      text-align: center;
      gap: @margin-base;
    }
    
    .quick-actions-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .main-content {
      grid-template-columns: 1fr;
    }
  }
}
</style>
