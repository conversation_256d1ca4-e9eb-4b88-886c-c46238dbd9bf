<template>
  <div class="heroes-view">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">球员管理</h2>
        <div class="hero-stats">
          <span class="stat-item">
            总数: <strong>{{ heroes.length }}</strong>
          </span>
          <span class="stat-item">
            主力: <strong>{{ mainTeamCount }}</strong>
          </span>
          <span class="stat-item">
            替补: <strong>{{ benchCount }}</strong>
          </span>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="showRecruitModal = true">
            <PlusOutlined />
            招募球员
          </a-button>
          <a-button @click="showBatchTrainModal = true">
            <ThunderboltOutlined />
            批量训练
          </a-button>
          <a-button @click="refreshHeroes">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchText"
            placeholder="搜索球员姓名"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="positionFilter"
            placeholder="位置筛选"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option value="">全部位置</a-select-option>
            <a-select-option value="GK">门将</a-select-option>
            <a-select-option value="DEF">后卫</a-select-option>
            <a-select-option value="MID">中场</a-select-option>
            <a-select-option value="ATT">前锋</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="qualityFilter"
            placeholder="品质筛选"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option value="">全部品质</a-select-option>
            <a-select-option :value="1">普通</a-select-option>
            <a-select-option :value="2">优秀</a-select-option>
            <a-select-option :value="3">精英</a-select-option>
            <a-select-option :value="4">史诗</a-select-option>
            <a-select-option :value="5">传奇</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="active">正常</a-select-option>
            <a-select-option value="injured">受伤</a-select-option>
            <a-select-option value="suspended">停赛</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-space>
            <span>排序:</span>
            <a-select v-model:value="sortBy" @change="handleSort" style="width: 120px">
              <a-select-option value="overall">总评</a-select-option>
              <a-select-option value="level">等级</a-select-option>
              <a-select-option value="marketValue">身价</a-select-option>
              <a-select-option value="age">年龄</a-select-option>
            </a-select>
            <a-button @click="toggleSortOrder">
              {{ sortOrder === 'desc' ? '↓' : '↑' }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 球员列表 -->
    <div class="heroes-content">
      <!-- 列表视图切换 -->
      <div class="view-toggle">
        <a-radio-group v-model:value="viewMode" @change="handleViewModeChange">
          <a-radio-button value="grid">
            <AppstoreOutlined />
            卡片视图
          </a-radio-button>
          <a-radio-button value="table">
            <UnorderedListOutlined />
            表格视图
          </a-radio-button>
        </a-radio-group>
      </div>

      <!-- 卡片视图 -->
      <div v-if="viewMode === 'grid'" class="heroes-grid">
        <div class="grid-container">
          <HeroCard
            v-for="hero in filteredHeroes"
            :key="hero.heroId"
            :hero="hero"
            :show-actions="true"
            :show-more-actions="true"
            @view-detail="handleViewDetail"
            @train="handleTrain"
            @sell="handleSell"
            @loan="handleLoan"
            @release="handleRelease"
          />
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-else class="heroes-table">
        <a-table
          :columns="tableColumns"
          :data-source="filteredHeroes"
          :pagination="tablePagination"
          :loading="loading"
          row-key="heroId"
          size="small"
          :scroll="{ x: 1200 }"
        >
          <!-- 球员名称列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="hero-name-cell">
                <span class="hero-name">{{ record.name }}</span>
                <div class="hero-meta">
                  <a-tag :color="getPositionColor(record.position)" size="small">
                    {{ record.position }}
                  </a-tag>
                  <a-tag :color="getQualityColor(record.quality)" size="small">
                    {{ getQualityText(record.quality) }}
                  </a-tag>
                </div>
              </div>
            </template>
            
            <!-- 属性列 -->
            <template v-else-if="column.key === 'attributes'">
              <div class="attributes-mini">
                <div class="attr-row">
                  <span>速度: {{ record.attributes?.pace || 0 }}</span>
                  <span>射门: {{ record.attributes?.shooting || 0 }}</span>
                </div>
                <div class="attr-row">
                  <span>传球: {{ record.attributes?.passing || 0 }}</span>
                  <span>防守: {{ record.attributes?.defending || 0 }}</span>
                </div>
              </div>
            </template>
            
            <!-- 状态列 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <a-button size="small" @click="handleViewDetail(record)">
                  详情
                </a-button>
                <a-button size="small" @click="handleTrain(record)">
                  训练
                </a-button>
                <a-dropdown>
                  <a-button size="small">
                    更多
                    <DownOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="({ key }: {key: string}) => handleTableAction(key, record)">
                      <a-menu-item key="sell">出售</a-menu-item>
                      <a-menu-item key="loan">租借</a-menu-item>
                      <a-menu-item key="release">释放</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 招募球员模态框 -->
    <!-- TODO: 创建 RecruitHeroModal 组件 -->
    <!--
    <RecruitHeroModal
      v-model:visible="showRecruitModal"
      @recruit-success="handleRecruitSuccess"
    />
    -->

    <!-- 批量训练模态框 -->
    <!-- TODO: 创建 BatchTrainModal 组件 -->
    <!--
    <BatchTrainModal
      v-model:visible="showBatchTrainModal"
      :selected-heroes="selectedHeroes"
      @train-success="handleBatchTrainSuccess"
    />
    -->

    <!-- 球员详情模态框 -->
    <!-- TODO: 创建 HeroDetailModal 组件 -->
    <!--
    <HeroDetailModal
      v-model:visible="showDetailModal"
      :hero="selectedHero"
      @hero-updated="handleHeroUpdated"
    />
    -->

    <!-- 训练模态框 -->
    <!-- TODO: 创建 TrainHeroModal 组件 -->
    <!--
    <TrainHeroModal
      v-model:visible="showTrainModal"
      :hero="selectedHero"
      @train-success="handleTrainSuccess"
    />
    -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  PlusOutlined,
  ThunderboltOutlined,
  ReloadOutlined,
  SearchOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { Hero, TableColumn, Pagination } from '@/types'
import { useHeroStore } from '@/stores/hero'
import { useGlobalStore } from '@/stores/global'
import HeroCard from '@/components/game/HeroCard.vue'
// 注意：以下组件需要创建
// import RecruitHeroModal from '@/components/game/RecruitHeroModal.vue'
// import BatchTrainModal from '@/components/game/BatchTrainModal.vue'
// import HeroDetailModal from '@/components/game/HeroDetailModal.vue'
// import TrainHeroModal from '@/components/game/TrainHeroModal.vue'

// 状态
const heroStore = useHeroStore()
const globalStore = useGlobalStore()

const loading = ref(false)
const viewMode = ref<'grid' | 'table'>('grid')
const searchText = ref('')
const positionFilter = ref('')
const qualityFilter = ref<number | ''>('')
const statusFilter = ref('')
const sortBy = ref('overall')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 模态框状态
const showRecruitModal = ref(false)
const showBatchTrainModal = ref(false)
const showDetailModal = ref(false)
const showTrainModal = ref(false)
const selectedHero = ref<Hero | null>(null)
const selectedHeroes = ref<Hero[]>([])

// 计算属性
const heroes = computed(() => heroStore.heroes)
const mainTeamCount = computed(() => heroes.value.filter(h => h.isInFormation).length)
const benchCount = computed(() => heroes.value.filter(h => !h.isInFormation && h.status === 'active').length)

// 筛选后的球员列表
const filteredHeroes = computed(() => {
  let result = [...heroes.value]

  // 搜索筛选
  if (searchText.value) {
    result = result.filter(hero => 
      hero.name.toLowerCase().includes(searchText.value.toLowerCase())
    )
  }

  // 位置筛选
  if (positionFilter.value) {
    result = result.filter(hero => hero.position === positionFilter.value)
  }

  // 品质筛选
  if (qualityFilter.value) {
    result = result.filter(hero => hero.quality === qualityFilter.value)
  }

  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(hero => hero.status === statusFilter.value)
  }

  // 排序
  result.sort((a, b) => {
    const aValue = a[sortBy.value as keyof Hero] as number
    const bValue = b[sortBy.value as keyof Hero] as number
    
    if (sortOrder.value === 'desc') {
      return bValue - aValue
    } else {
      return aValue - bValue
    }
  })

  return result
})

// 表格配置
const tableColumns: TableColumn[] = [
  {
    title: '球员',
    key: 'name',
    dataIndex: 'name',
    width: 200
  },
  {
    title: '总评',
    key: 'overall',
    dataIndex: 'overall',
    width: 80,
    sorter: true
  },
  {
    title: '等级',
    key: 'level',
    dataIndex: 'level',
    width: 80
  },
  {
    title: '年龄',
    key: 'age',
    dataIndex: 'age',
    width: 80
  },
  {
    title: '属性',
    key: 'attributes',
    dataIndex: 'attributes',
    width: 160
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '身价',
    key: 'marketValue',
    dataIndex: 'marketValue',
    width: 120,
    render: (value: number) => formatCurrency(value)
  },
  {
    title: '操作',
    key: 'actions',
    dataIndex: 'actions',
    width: 180
  }
]

const tablePagination: Pagination = {
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}

// 方法
const refreshHeroes = async () => {
  loading.value = true
  try {
    await heroStore.fetchHeroes()
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const handleSort = () => {
  // 排序逻辑已在计算属性中处理
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
}

const handleViewModeChange = () => {
  // 视图模式切换
}

const handleViewDetail = (hero: Hero) => {
  selectedHero.value = hero
  showDetailModal.value = true
}

const handleTrain = (hero: Hero) => {
  selectedHero.value = hero
  showTrainModal.value = true
}

const handleSell = (hero: Hero) => {
  // 出售球员逻辑
  globalStore.addNotification({
    type: 'info',
    title: '出售球员',
    message: `正在出售球员 ${hero.name}`
  })
}

const handleLoan = (hero: Hero) => {
  // 租借球员逻辑
  globalStore.addNotification({
    type: 'info',
    title: '租借球员',
    message: `正在租借球员 ${hero.name}`
  })
}

const handleRelease = (hero: Hero) => {
  // 释放球员逻辑
  globalStore.addNotification({
    type: 'warning',
    title: '释放球员',
    message: `正在释放球员 ${hero.name}`
  })
}

const handleTableAction = (action: string, hero: Hero) => {
  switch (action) {
    case 'sell':
      handleSell(hero)
      break
    case 'loan':
      handleLoan(hero)
      break
    case 'release':
      handleRelease(hero)
      break
  }
}

const handleRecruitSuccess = (newHero: Hero) => {
  heroStore.addHero(newHero)
  globalStore.addNotification({
    type: 'success',
    title: '招募成功',
    message: `成功招募球员 ${newHero.name}`
  })
}

const handleBatchTrainSuccess = () => {
  refreshHeroes()
  globalStore.addNotification({
    type: 'success',
    title: '批量训练完成',
    message: '所有选中球员训练完成'
  })
}

const handleHeroUpdated = (updatedHero: Hero) => {
  heroStore.updateHero(updatedHero)
}

const handleTrainSuccess = (result: any) => {
  refreshHeroes()
  globalStore.addNotification({
    type: 'success',
    title: '训练完成',
    message: `球员训练完成，属性提升`
  })
}

// 工具函数
const formatCurrency = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toString()
}

const getPositionColor = (position: string): string => {
  const colors: Record<string, string> = {
    GK: 'red',
    DEF: 'blue',
    MID: 'green',
    ATT: 'orange'
  }
  return colors[position] || 'default'
}

const getQualityColor = (quality: number): string => {
  const colors = ['', 'default', 'green', 'blue', 'purple', 'gold']
  return colors[quality] || 'default'
}

const getQualityText = (quality: number): string => {
  const texts = ['', '普通', '优秀', '精英', '史诗', '传奇']
  return texts[quality] || '未知'
}

const getStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    active: 'green',
    injured: 'red',
    suspended: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string): string => {
  const texts: Record<string, string> = {
    active: '正常',
    injured: '受伤',
    suspended: '停赛'
  }
  return texts[status] || status
}

// 生命周期
onMounted(() => {
  refreshHeroes()
})
</script>

<style lang="less" scoped>
.heroes-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .hero-stats {
        display: flex;
        gap: @margin-base;
        
        .stat-item {
          color: @text-color-secondary;
          font-size: @font-size-sm;
          
          strong {
            color: @primary-color;
          }
        }
      }
    }
  }
  
  .filter-section {
    background-color: @card-bg;
    padding: @padding-base;
    border-radius: @border-radius-base;
    margin-bottom: @margin-lg;
    border: 1px solid @border-color;
  }
  
  .heroes-content {
    .view-toggle {
      margin-bottom: @margin-base;
      text-align: right;
    }
    
    .heroes-grid {
      .grid-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: @margin-base;
      }
    }
    
    .heroes-table {
      background-color: @card-bg;
      border-radius: @border-radius-base;
      border: 1px solid @border-color;
      
      :deep(.ant-table) {
        background-color: transparent;
        
        .ant-table-thead > tr > th {
          background-color: @card-bg;
          color: @text-color;
          border-bottom: 1px solid @border-color;
        }
        
        .ant-table-tbody > tr > td {
          border-bottom: 1px solid @border-color;
          color: @text-color-secondary;
        }
        
        .ant-table-tbody > tr:hover > td {
          background-color: rgba(82, 196, 26, 0.05);
        }
      }
      
      .hero-name-cell {
        .hero-name {
          display: block;
          color: @text-color;
          font-weight: bold;
          margin-bottom: 4px;
        }
        
        .hero-meta {
          display: flex;
          gap: 4px;
        }
      }
      
      .attributes-mini {
        font-size: @font-size-xs;
        
        .attr-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2px;
          
          span {
            color: @text-color-secondary;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .heroes-view {
    padding: @padding-base;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-base;
    }
    
    .filter-section {
      :deep(.ant-row) {
        flex-direction: column;
        
        .ant-col {
          width: 100% !important;
          margin-bottom: @margin-sm;
        }
      }
    }
    
    .heroes-grid .grid-container {
      grid-template-columns: 1fr;
    }
  }
}
</style>
