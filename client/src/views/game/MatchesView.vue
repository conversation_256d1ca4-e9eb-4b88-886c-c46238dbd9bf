<template>
  <div class="matches-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">比赛中心</h2>
        <div class="match-stats">
          <span class="stat-item">
            今日比赛: <strong>{{ todayMatches }}</strong>
          </span>
          <span class="stat-item">
            胜率: <strong>{{ winRate }}%</strong>
          </span>
          <span class="stat-item">
            积分: <strong>{{ currentPoints }}</strong>
          </span>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button type="primary" @click="startQuickMatch" :loading="isMatching">
            <PlayCircleOutlined />
            快速比赛
          </a-button>
          <a-button @click="showMatchHistory = true">
            <HistoryOutlined />
            比赛记录
          </a-button>
          <a-button @click="refreshMatches">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 比赛类型标签 -->
    <div class="match-tabs">
      <a-tabs v-model:activeKey="activeMatchType" @change="handleMatchTypeChange">
        <!-- 快速比赛 -->
        <a-tab-pane key="quick" tab="快速比赛">
          <div class="quick-match-section">
            <div class="match-options">
              <a-row :gutter="24">
                <a-col :span="8">
                  <div class="option-card">
                    <h3>PVE挑战</h3>
                    <p>挑战AI对手，获得经验和奖励</p>
                    <a-button block @click="startPveMatch">
                      开始挑战
                    </a-button>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="option-card">
                    <h3>PVP对战</h3>
                    <p>与其他玩家实时对战</p>
                    <a-button block @click="startPvpMatch" :loading="isMatching">
                      寻找对手
                    </a-button>
                  </div>
                </a-col>
                <a-col :span="8">
                  <div class="option-card">
                    <h3>友谊赛</h3>
                    <p>与好友进行友谊比赛</p>
                    <a-button block @click="showFriendlyMatch = true">
                      邀请好友
                    </a-button>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>

        <!-- 联赛 -->
        <a-tab-pane key="league" tab="联赛">
          <div class="league-section">
            <div class="league-info">
              <div class="current-league">
                <h3>当前联赛: {{ currentLeague.name }}</h3>
                <div class="league-progress">
                  <a-progress
                    :percent="leagueProgress"
                    :stroke-color="getLeagueColor(currentLeague.level)"
                  />
                  <span class="progress-text">
                    {{ currentLeague.currentPoints }}/{{ currentLeague.requiredPoints }}
                  </span>
                </div>
              </div>
              
              <div class="league-rewards">
                <h4>联赛奖励</h4>
                <div class="rewards-list">
                  <div
                    v-for="reward in currentLeague.rewards"
                    :key="reward.id"
                    class="reward-item"
                  >
                    <span class="reward-icon">{{ reward.icon }}</span>
                    <span class="reward-name">{{ reward.name }}</span>
                    <span class="reward-amount">x{{ reward.amount }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="league-matches">
              <h4>联赛赛程</h4>
              <a-table
                :columns="leagueColumns"
                :data-source="leagueMatches"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'opponent'">
                    <div class="opponent-info">
                      <span class="opponent-name">{{ record.opponentName }}</span>
                      <span class="opponent-rating">{{ record.opponentRating }}</span>
                    </div>
                  </template>
                  
                  <template v-else-if="column.key === 'status'">
                    <a-tag :color="getMatchStatusColor(record.status)">
                      {{ getMatchStatusText(record.status) }}
                    </a-tag>
                  </template>
                  
                  <template v-else-if="column.key === 'actions'">
                    <a-button
                      v-if="record.status === 'ready'"
                      size="small"
                      type="primary"
                      @click="startLeagueMatch(record)"
                    >
                      开始比赛
                    </a-button>
                    <a-button
                      v-else-if="record.status === 'finished'"
                      size="small"
                      @click="viewMatchResult(record)"
                    >
                      查看结果
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 锦标赛 -->
        <a-tab-pane key="tournament" tab="锦标赛">
          <div class="tournament-section">
            <div class="tournament-list">
              <div
                v-for="tournament in tournaments"
                :key="tournament.id"
                class="tournament-card"
              >
                <div class="tournament-header">
                  <h3>{{ tournament.name }}</h3>
                  <a-tag :color="getTournamentStatusColor(tournament.status)">
                    {{ getTournamentStatusText(tournament.status) }}
                  </a-tag>
                </div>
                
                <div class="tournament-info">
                  <div class="info-item">
                    <span class="label">参赛费用:</span>
                    <span class="value">{{ tournament.entryCost }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">奖池:</span>
                    <span class="value">{{ tournament.prizePool }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">参赛人数:</span>
                    <span class="value">{{ tournament.participants }}/{{ tournament.maxParticipants }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">开始时间:</span>
                    <span class="value">{{ formatTime(tournament.startTime) }}</span>
                  </div>
                </div>
                
                <div class="tournament-actions">
                  <a-button
                    v-if="tournament.status === 'registration'"
                    type="primary"
                    @click="joinTournament(tournament)"
                    :disabled="!canJoinTournament(tournament)"
                  >
                    报名参赛
                  </a-button>
                  <a-button
                    v-else-if="tournament.status === 'ongoing' && tournament.isParticipant"
                    @click="viewTournamentBracket(tournament)"
                  >
                    查看对阵
                  </a-button>
                  <a-button
                    v-else-if="tournament.status === 'finished'"
                    @click="viewTournamentResults(tournament)"
                  >
                    查看结果
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 实时比赛界面 -->
    <LiveMatchModal
      v-model:visible="showLiveMatch"
      :match-data="currentMatch"
      @match-finished="handleMatchFinished"
    />

    <!-- 比赛结果模态框 -->
    <MatchResultModal
      v-model:visible="showMatchResult"
      :result="matchResult"
      @claim-rewards="handleClaimRewards"
    />

    <!-- 比赛历史模态框 -->
    <MatchHistoryModal
      v-model:visible="showMatchHistory"
      @replay-match="handleReplayMatch"
    />

    <!-- 友谊赛邀请模态框 -->
    <FriendlyMatchModal
      v-model:visible="showFriendlyMatch"
      @match-created="handleFriendlyMatchCreated"
    />

    <!-- 锦标赛对阵图模态框 -->
    <TournamentBracketModal
      v-model:visible="showTournamentBracket"
      :tournament="selectedTournament"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  PlayCircleOutlined,
  HistoryOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import type { Match, Tournament, League, MatchResult } from '@/types'
import { useMatchStore } from '@/stores/match'
import { useGlobalStore } from '@/stores/global'
import { wsService } from '@/services/websocket'
import LiveMatchModal from '@/components/game/LiveMatchModal.vue'
// 注意：以下组件需要创建
// import MatchResultModal from '@/components/game/MatchResultModal.vue'
// import MatchHistoryModal from '@/components/game/MatchHistoryModal.vue'
// import FriendlyMatchModal from '@/components/game/FriendlyMatchModal.vue'
// import TournamentBracketModal from '@/components/game/TournamentBracketModal.vue'

// 状态
const matchStore = useMatchStore()
const globalStore = useGlobalStore()

const loading = ref(false)
const isMatching = ref(false)
const activeMatchType = ref('quick')

// 模态框状态
const showLiveMatch = ref(false)
const showMatchResult = ref(false)
const showMatchHistory = ref(false)
const showFriendlyMatch = ref(false)
const showTournamentBracket = ref(false)

// 数据状态
const currentMatch = ref<Match | null>(null)
const matchResult = ref<MatchResult | null>(null)
const selectedTournament = ref<Tournament | null>(null)

// 计算属性
const todayMatches = computed(() => matchStore.todayMatchCount)
const winRate = computed(() => matchStore.winRate)
const currentPoints = computed(() => matchStore.currentPoints)
const currentLeague = computed(() => matchStore.currentLeague)
const leagueProgress = computed(() => matchStore.leagueProgress)
const leagueMatches = computed(() => matchStore.leagueMatches)
const tournaments = computed(() => matchStore.tournaments)

// 表格列配置
const leagueColumns = [
  {
    title: '轮次',
    key: 'round',
    dataIndex: 'round',
    width: 80
  },
  {
    title: '对手',
    key: 'opponent',
    width: 200
  },
  {
    title: '比分',
    key: 'score',
    dataIndex: 'score',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 方法
const refreshMatches = async () => {
  loading.value = true
  try {
    await matchStore.fetchMatches()
    await matchStore.fetchLeagueInfo()
    await matchStore.fetchTournaments()
  } finally {
    loading.value = false
  }
}

const handleMatchTypeChange = (key: string) => {
  activeMatchType.value = key
}

const startQuickMatch = async () => {
  // 开始快速匹配
  isMatching.value = true
  try {
    const match = await matchStore.startQuickMatch()
    if (match) {
      currentMatch.value = match
      showLiveMatch.value = true
    }
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: '匹配失败',
      message: '无法开始快速比赛，请稍后重试'
    })
  } finally {
    isMatching.value = false
  }
}

const startPveMatch = async () => {
  try {
    const match = await matchStore.startPveMatch()
    if (match) {
      currentMatch.value = match
      showLiveMatch.value = true
    }
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: 'PVE挑战失败',
      message: '无法开始PVE挑战，请稍后重试'
    })
  }
}

const startPvpMatch = async () => {
  isMatching.value = true
  try {
    // 开始PVP匹配
    await matchStore.startPvpMatching()
    
    globalStore.addNotification({
      type: 'info',
      title: '正在匹配',
      message: '正在寻找合适的对手...'
    })
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: 'PVP匹配失败',
      message: '无法开始PVP匹配，请稍后重试'
    })
  } finally {
    isMatching.value = false
  }
}

const startLeagueMatch = async (match: any) => {
  try {
    const liveMatch = await matchStore.startLeagueMatch(match.id)
    if (liveMatch) {
      currentMatch.value = liveMatch
      showLiveMatch.value = true
    }
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: '联赛比赛失败',
      message: '无法开始联赛比赛，请稍后重试'
    })
  }
}

const viewMatchResult = (match: any) => {
  matchResult.value = match.result
  showMatchResult.value = true
}

const joinTournament = async (tournament: Tournament) => {
  try {
    await matchStore.joinTournament(tournament.id)
    globalStore.addNotification({
      type: 'success',
      title: '报名成功',
      message: `成功报名参加 ${tournament.name}`
    })
    refreshMatches()
  } catch (error) {
    globalStore.addNotification({
      type: 'error',
      title: '报名失败',
      message: '锦标赛报名失败，请稍后重试'
    })
  }
}

const viewTournamentBracket = (tournament: Tournament) => {
  selectedTournament.value = tournament
  showTournamentBracket.value = true
}

const viewTournamentResults = (tournament: Tournament) => {
  // 查看锦标赛结果
  globalStore.addNotification({
    type: 'info',
    title: '锦标赛结果',
    message: `查看 ${tournament.name} 的比赛结果`
  })
}

const handleMatchFinished = (result: MatchResult) => {
  showLiveMatch.value = false
  matchResult.value = result
  showMatchResult.value = true
  
  // 更新统计数据
  refreshMatches()
}

const handleClaimRewards = (rewards: any[]) => {
  globalStore.addNotification({
    type: 'success',
    title: '奖励已领取',
    message: '比赛奖励已发放到背包'
  })
}

const handleReplayMatch = (match: Match) => {
  // 重播比赛
  currentMatch.value = match
  showLiveMatch.value = true
}

const handleFriendlyMatchCreated = (match: Match) => {
  globalStore.addNotification({
    type: 'success',
    title: '友谊赛创建成功',
    message: '已向好友发送比赛邀请'
  })
}

// 工具函数
const canJoinTournament = (tournament: Tournament): boolean => {
  return tournament.participants < tournament.maxParticipants &&
         tournament.status === 'registration'
}

const getLeagueColor = (level: number): string => {
  const colors = ['#8c8c8c', '#52c41a', '#1890ff', '#722ed1', '#fa8c16', '#f5222d']
  return colors[level] || colors[0]
}

const getMatchStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    ready: 'green',
    ongoing: 'blue',
    finished: 'default'
  }
  return colors[status] || 'default'
}

const getMatchStatusText = (status: string): string => {
  const texts: Record<string, string> = {
    ready: '准备中',
    ongoing: '进行中',
    finished: '已完成'
  }
  return texts[status] || status
}

const getTournamentStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    registration: 'blue',
    ongoing: 'green',
    finished: 'default'
  }
  return colors[status] || 'default'
}

const getTournamentStatusText = (status: string): string => {
  const texts: Record<string, string> = {
    registration: '报名中',
    ongoing: '进行中',
    finished: '已结束'
  }
  return texts[status] || status
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

// WebSocket事件监听
const setupWebSocketListeners = () => {
  // 监听匹配成功事件
  wsService.subscribe('match.found', (data) => {
    isMatching.value = false
    currentMatch.value = data.match
    showLiveMatch.value = true
    
    globalStore.addNotification({
      type: 'success',
      title: '匹配成功',
      message: '找到对手，比赛即将开始！'
    })
  })
  
  // 监听匹配失败事件
  wsService.subscribe('match.failed', (data) => {
    isMatching.value = false
    globalStore.addNotification({
      type: 'error',
      title: '匹配失败',
      message: data.message || '匹配超时，请重新尝试'
    })
  })
  
  // 监听比赛事件
  wsService.subscribe('match.event', (data) => {
    if (currentMatch.value && currentMatch.value.id === data.matchId) {
      // 更新比赛数据
      currentMatch.value = { ...currentMatch.value, ...data.updates }
    }
  })
}

const cleanupWebSocketListeners = () => {
  wsService.unsubscribe('match.found')
  wsService.unsubscribe('match.failed')
  wsService.unsubscribe('match.event')
}

// 生命周期
onMounted(() => {
  refreshMatches()
  setupWebSocketListeners()
})

onUnmounted(() => {
  cleanupWebSocketListeners()
})
</script>

<style lang="less" scoped>
.matches-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .match-stats {
        display: flex;
        gap: @margin-base;
        
        .stat-item {
          color: @text-color-secondary;
          font-size: @font-size-sm;
          
          strong {
            color: @primary-color;
          }
        }
      }
    }
  }
  
  .match-tabs {
    background-color: @card-bg;
    border-radius: @border-radius-base;
    border: 1px solid @border-color;
    
    :deep(.ant-tabs) {
      .ant-tabs-tab {
        color: @text-color-secondary;
        
        &.ant-tabs-tab-active {
          color: @primary-color;
        }
      }
      
      .ant-tabs-content-holder {
        padding: @padding-lg;
      }
    }
  }
  
  .quick-match-section {
    .match-options {
      .option-card {
        background-color: rgba(0, 0, 0, 0.2);
        border: 1px solid @border-color;
        border-radius: @border-radius-base;
        padding: @padding-lg;
        text-align: center;
        transition: all @transition-duration;
        
        &:hover {
          border-color: @primary-color;
          transform: translateY(-2px);
        }
        
        h3 {
          color: @text-color;
          margin-bottom: @margin-sm;
        }
        
        p {
          color: @text-color-secondary;
          margin-bottom: @margin-base;
        }
      }
    }
  }
  
  .league-section {
    .league-info {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: @margin-lg;
      margin-bottom: @margin-lg;
      
      .current-league {
        background-color: rgba(0, 0, 0, 0.2);
        border: 1px solid @border-color;
        border-radius: @border-radius-base;
        padding: @padding-base;
        
        h3 {
          color: @text-color;
          margin-bottom: @margin-sm;
        }
        
        .league-progress {
          display: flex;
          align-items: center;
          gap: @margin-sm;
          
          .progress-text {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
      }
      
      .league-rewards {
        background-color: rgba(0, 0, 0, 0.2);
        border: 1px solid @border-color;
        border-radius: @border-radius-base;
        padding: @padding-base;
        
        h4 {
          color: @text-color;
          margin-bottom: @margin-sm;
        }
        
        .rewards-list {
          display: flex;
          flex-direction: column;
          gap: @margin-xs;
          
          .reward-item {
            display: flex;
            align-items: center;
            gap: @margin-sm;
            
            .reward-icon {
              font-size: @font-size-lg;
            }
            
            .reward-name {
              flex: 1;
              color: @text-color-secondary;
            }
            
            .reward-amount {
              color: @primary-color;
              font-weight: bold;
            }
          }
        }
      }
    }
    
    .league-matches {
      h4 {
        color: @text-color;
        margin-bottom: @margin-base;
      }
      
      :deep(.ant-table) {
        background-color: transparent;
        
        .ant-table-thead > tr > th {
          background-color: @card-bg;
          color: @text-color;
          border-bottom: 1px solid @border-color;
        }
        
        .ant-table-tbody > tr > td {
          border-bottom: 1px solid @border-color;
          color: @text-color-secondary;
        }
        
        .ant-table-tbody > tr:hover > td {
          background-color: rgba(82, 196, 26, 0.05);
        }
      }
      
      .opponent-info {
        display: flex;
        flex-direction: column;
        
        .opponent-name {
          color: @text-color;
          font-weight: bold;
        }
        
        .opponent-rating {
          color: @text-color-secondary;
          font-size: @font-size-sm;
        }
      }
    }
  }
  
  .tournament-section {
    .tournament-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: @margin-base;
      
      .tournament-card {
        background-color: rgba(0, 0, 0, 0.2);
        border: 1px solid @border-color;
        border-radius: @border-radius-base;
        padding: @padding-base;
        transition: all @transition-duration;
        
        &:hover {
          border-color: @primary-color;
          transform: translateY(-2px);
        }
        
        .tournament-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: @margin-base;
          
          h3 {
            color: @text-color;
            margin: 0;
          }
        }
        
        .tournament-info {
          margin-bottom: @margin-base;
          
          .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: @margin-xs;
            
            .label {
              color: @text-color-secondary;
            }
            
            .value {
              color: @text-color;
              font-weight: bold;
            }
          }
        }
        
        .tournament-actions {
          text-align: center;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .matches-view {
    padding: @padding-base;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-base;
    }
    
    .quick-match-section .match-options {
      :deep(.ant-row) {
        flex-direction: column;
        
        .ant-col {
          width: 100% !important;
          margin-bottom: @margin-base;
        }
      }
    }
    
    .league-section .league-info {
      grid-template-columns: 1fr;
    }
    
    .tournament-section .tournament-list {
      grid-template-columns: 1fr;
    }
  }
}
</style>
