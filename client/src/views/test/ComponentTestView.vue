<template>
  <div class="component-test-view">
    <h1>组件测试页面</h1>
    
    <!-- 测试MarketPlace组件 -->
    <div class="test-section">
      <h2>MarketPlace组件测试</h2>
      <MarketPlace />
    </div>
    
    <!-- 测试MatchSimulator组件 -->
    <div class="test-section">
      <h2>MatchSimulator组件测试</h2>
      <MatchSimulator :match="testMatch" />
    </div>
    
    <!-- 测试TrainingCenter组件 -->
    <div class="test-section">
      <h2>TrainingCenter组件测试</h2>
      <TrainingCenter />
    </div>
    
    <!-- 测试FormationField组件 -->
    <div class="test-section">
      <h2>FormationField组件测试</h2>
      <FormationField 
        :formation="testFormation" 
        :heroes="testHeroes"
        :interactive="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MarketPlace from '@/components/game/MarketPlace.vue'
import MatchSimulator from '@/components/game/MatchSimulator.vue'
import TrainingCenter from '@/components/game/TrainingCenter.vue'
import FormationField from '@/components/game/FormationField.vue'
import type { Match, Formation, Hero } from '@/types'

// 测试数据
const testMatch = ref<Match>({
  id: 'test-match-1',
  homeTeam: {
    id: 'team-1',
    name: '测试主队',
    rating: 85
  },
  awayTeam: {
    id: 'team-2', 
    name: '测试客队',
    rating: 82
  },
  homeScore: 2,
  awayScore: 1,
  currentTime: 75,
  status: 'live',
  events: [],
  stats: {
    shots: { home: 12, away: 8 },
    shotsOnTarget: { home: 6, away: 4 },
    possession: { home: 58, away: 42 }
  }
})

const testFormation = ref<Formation>({
  uid: 'test-formation-1',
  characterId: 'test-character',
  serverId: 'test-server',
  currTeamFormationId: 'team-1',
  teamFormations: [{
    uid: 'team-1',
    resId: 1,
    name: '4-3-3',
    attack: 85,
    defend: 78,
    actualStrength: 82,
    isInitName: 0,
    isInitFormation: 1,
    isLeague: 0,
    teamId: 1,
    teamType: 1,
    positionToHerosObject: {
      GK: [],
      DL: [],
      DC: [],
      DR: [],
      ML: [],
      MC: [],
      MR: [],
      WL: [],
      ST: [],
      WR: [],
      AM: [],
      DM: []
    },
    freeKickHero: '',
    penaltiesHero: '',
    cornerKickHero: '',
    useTactics: 0,
    useDefTactics: 0,
    scenceUse: [],
    inspireRate: 0,
    type: 1
  }]
})

const testHeroes = ref<Hero[]>([
  {
    heroId: 'hero-1',
    characterId: 'test-character',
    serverId: 'test-server',
    resId: 1001,
    name: '测试球员1',
    position: 'GK',
    quality: 3,
    level: 10,
    exp: 1500,
    experience: 1500,
    attributes: {
      pace: 65,
      shooting: 30,
      passing: 70,
      dribbling: 45,
      defending: 85,
      physical: 80
    }
  }
])
</script>

<style lang="less" scoped>
.component-test-view {
  padding: 20px;
  
  .test-section {
    margin-bottom: 40px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 20px;
    
    h2 {
      color: #333;
      margin-bottom: 20px;
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 10px;
    }
  }
}
</style>
