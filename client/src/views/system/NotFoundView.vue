<template>
  <div class="not-found-view">
    <div class="not-found-container">
      <div class="error-content">
        <div class="error-code">404</div>
        <div class="error-title">页面未找到</div>
        <div class="error-description">
          抱歉，您访问的页面不存在或已被移除。
        </div>
        
        <div class="error-actions">
          <a-space>
            <a-button type="primary" @click="goHome">
              <HomeOutlined />
              返回首页
            </a-button>
            <a-button @click="goBack">
              <ArrowLeftOutlined />
              返回上页
            </a-button>
          </a-space>
        </div>
        
        <div class="error-suggestions">
          <h4>您可以尝试：</h4>
          <ul>
            <li>检查网址是否正确</li>
            <li>返回上一页重新操作</li>
            <li>访问游戏主页</li>
            <li>联系客服获取帮助</li>
          </ul>
        </div>
      </div>
      
      <div class="error-illustration">
        <div class="ascii-art">
          <pre>
    ⚽
   /|\
    |
   / \
  
  球员迷路了...
          </pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/game')
}

const goBack = () => {
  router.back()
}
</script>

<style lang="less" scoped>
.not-found-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, @bg-color 0%, #002140 100%);
  padding: @padding-lg;
}

.not-found-container {
  display: flex;
  align-items: center;
  gap: @margin-xl;
  max-width: 800px;
  width: 100%;
}

.error-content {
  flex: 1;
  text-align: center;
  
  .error-code {
    font-size: 8rem;
    font-weight: bold;
    color: @primary-color;
    font-family: @font-family-mono;
    line-height: 1;
    margin-bottom: @margin-base;
    text-shadow: 0 0 20px rgba(82, 196, 26, 0.3);
  }
  
  .error-title {
    font-size: @font-size-xxl;
    font-weight: bold;
    color: @text-color;
    margin-bottom: @margin-base;
  }
  
  .error-description {
    font-size: @font-size-lg;
    color: @text-color-secondary;
    margin-bottom: @margin-xl;
    line-height: 1.6;
  }
  
  .error-actions {
    margin-bottom: @margin-xl;
  }
  
  .error-suggestions {
    text-align: left;
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-lg;
    
    h4 {
      color: @text-color;
      margin-bottom: @margin-base;
      font-size: @font-size-lg;
    }
    
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        color: @text-color-secondary;
        margin-bottom: @margin-sm;
        padding-left: @padding-base;
        position: relative;
        
        &:before {
          content: '•';
          color: @primary-color;
          position: absolute;
          left: 0;
          font-weight: bold;
        }
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.error-illustration {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .ascii-art {
    font-family: @font-family-mono;
    color: @primary-color;
    font-size: @font-size-lg;
    line-height: 1.2;
    text-align: center;
    
    pre {
      margin: 0;
      white-space: pre;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .not-found-view {
    padding: @padding-base;
  }
  
  .not-found-container {
    flex-direction: column;
    text-align: center;
  }
  
  .error-content {
    .error-code {
      font-size: 6rem;
    }
    
    .error-title {
      font-size: @font-size-xl;
    }
    
    .error-description {
      font-size: @font-size-base;
    }
    
    .error-actions {
      .ant-space {
        flex-direction: column;
        width: 100%;
        
        .ant-btn {
          width: 100%;
        }
      }
    }
  }
  
  .error-illustration {
    .ascii-art {
      font-size: @font-size-base;
    }
  }
}

@media (max-width: 480px) {
  .error-content {
    .error-code {
      font-size: 4rem;
    }
    
    .error-suggestions {
      padding: @padding-base;
      
      h4 {
        font-size: @font-size-base;
      }
      
      ul li {
        font-size: @font-size-sm;
      }
    }
  }
}
</style>
