<template>
  <div class="character-selection-view">
    <div class="character-selection-container">
      <div class="selection-header">
        <h1 class="selection-title">选择角色</h1>
        <p class="selection-subtitle">
          服务器: {{ selectedServer?.name }} | 选择一个角色进入游戏
        </p>
      </div>

      <div class="characters-section">
        <div class="characters-list" v-loading="isLoading">
          <div
            v-for="character in characters"
            :key="character.characterId"
            class="character-card"
            :class="{ 'selected': selectedCharacterId === character.characterId }"
            @click="selectCharacter(character)"
          >
            <div class="character-header">
              <div class="character-name">{{ character.name }}</div>
              <div class="character-level">Lv.{{ character.level }}</div>
            </div>

            <div class="character-info">
              <div class="info-item">
                <span class="info-label">经验:</span>
                <span class="info-value">{{ character.exp }}/{{ character.maxExp }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">创建时间:</span>
                <span class="info-value">{{ formatDate(character.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后登录:</span>
                <span class="info-value">
                  {{ character.lastLoginAt ? formatDate(character.lastLoginAt) : '从未登录' }}
                </span>
              </div>
            </div>

            <div class="character-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: getExpPercentage(character) + '%' }"
                ></div>
              </div>
            </div>
          </div>

          <!-- 创建新角色卡片 -->
          <div 
            v-if="characters.length < maxCharacters"
            class="character-card create-card"
            @click="showCreateModal = true"
          >
            <div class="create-content">
              <div class="create-icon">+</div>
              <div class="create-text">创建新角色</div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!isLoading && characters.length === 0" class="empty-characters">
            <div class="empty-icon">👤</div>
            <div class="empty-text">您还没有角色</div>
            <a-button type="primary" @click="showCreateModal = true">
              创建第一个角色
            </a-button>
          </div>
        </div>
      </div>

      <div class="selection-actions">
        <a-button @click="goBack">
          返回服务器选择
        </a-button>
        <a-button @click="refreshCharacters" :loading="isLoading">
          刷新角色列表
        </a-button>
        <a-button
          type="primary"
          :disabled="!selectedCharacterId"
          @click="confirmSelection"
          :loading="isConfirming"
        >
          进入游戏
        </a-button>
      </div>
    </div>

    <!-- 创建角色模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      title="创建新角色"
      @ok="handleCreateCharacter"
      @cancel="resetCreateForm"
      :confirm-loading="isCreating"
    >
      <a-form
        :model="createForm"
        :rules="createRules"
        layout="vertical"
        ref="createFormRef"
      >
        <a-form-item label="角色名称" name="name">
          <a-input
            v-model:value="createForm.name"
            placeholder="请输入角色名称"
            maxlength="20"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'
import type { Character, CreateCharacterDto } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const globalStore = useGlobalStore()

// 状态
const isLoading = ref(false)
const isConfirming = ref(false)
const isCreating = ref(false)
const characters = ref<Character[]>([])
const selectedCharacterId = ref<string>('')
const maxCharacters = ref(3) // 最大角色数量

// 创建角色相关
const showCreateModal = ref(false)
const createFormRef = ref()
const createForm = reactive<CreateCharacterDto>({
  name: '',
  serverId: ''
})

// 计算属性
const selectedServer = computed(() => authStore.selectedServer)

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 20, message: '角色名称长度为2-20个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
      message: '角色名称只能包含中文、英文和数字',
      trigger: 'blur'
    }
  ]
}

// 方法
const refreshCharacters = async () => {
  if (!selectedServer.value) return
  
  try {
    isLoading.value = true
    characters.value = await authStore.fetchCharacterList(selectedServer.value.serverId)
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '获取角色列表失败',
      message: error.message
    })
  } finally {
    isLoading.value = false
  }
}

const selectCharacter = (character: Character) => {
  selectedCharacterId.value = character.characterId
}

const confirmSelection = async () => {
  if (!selectedCharacterId.value) return
  
  try {
    isConfirming.value = true
    
    const selectedCharacter = characters.value.find(c => c.characterId === selectedCharacterId.value)
    if (selectedCharacter) {
      await authStore.selectCharacter(selectedCharacter)
      
      globalStore.addNotification({
        type: 'success',
        title: '角色选择成功',
        message: `欢迎回来，${selectedCharacter.name}！`
      })
      
      router.push('/game')
    }
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '选择角色失败',
      message: error.message
    })
  } finally {
    isConfirming.value = false
  }
}

const handleCreateCharacter = async () => {
  if (!selectedServer.value) return
  
  try {
    await createFormRef.value.validate()
    isCreating.value = true
    
    createForm.serverId = selectedServer.value.serverId
    const newCharacter = await authStore.createCharacter(createForm)
    
    characters.value.push(newCharacter)
    selectedCharacterId.value = newCharacter.characterId
    
    globalStore.addNotification({
      type: 'success',
      title: '角色创建成功',
      message: `角色 ${newCharacter.name} 创建成功！`
    })
    
    showCreateModal.value = false
    resetCreateForm()
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '创建角色失败',
      message: error.message
    })
  } finally {
    isCreating.value = false
  }
}

const resetCreateForm = () => {
  createForm.name = ''
  createFormRef.value?.resetFields()
}

const goBack = () => {
  router.push('/server-selection')
}

// 工具函数
const getExpPercentage = (character: Character): number => {
  if (character.maxExp === 0) return 0
  return Math.round((character.exp / character.maxExp) * 100)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString()
}

// 生命周期
onMounted(() => {
  if (!selectedServer.value) {
    router.push('/server-selection')
    return
  }
  refreshCharacters()
})
</script>

<style lang="less" scoped>
.character-selection-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, @bg-color 0%, #002140 100%);
  padding: @padding-lg;
}

.character-selection-container {
  width: 100%;
  max-width: 900px;
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-xl;
  box-shadow: @box-shadow-elevated;
}

.selection-header {
  text-align: center;
  margin-bottom: @margin-xl;

  .selection-title {
    font-size: @font-size-xxl;
    color: @text-color;
    margin: 0 0 @margin-sm 0;
    font-family: @font-family-mono;
  }

  .selection-subtitle {
    color: @text-color-secondary;
    margin: 0;
  }
}

.characters-section {
  margin-bottom: @margin-xl;

  .characters-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: @margin-base;
    min-height: 200px;

    .character-card {
      background-color: rgba(0, 0, 0, 0.2);
      border: 2px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-base;
      cursor: pointer;
      transition: all @transition-duration;

      &:hover {
        border-color: @primary-color;
        transform: translateY(-2px);
      }

      &.selected {
        border-color: @primary-color;
        background-color: rgba(82, 196, 26, 0.1);
      }

      .character-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @margin-sm;

        .character-name {
          font-size: @font-size-lg;
          font-weight: bold;
          color: @text-color;
        }

        .character-level {
          background-color: @primary-color;
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: @font-size-xs;
          font-weight: bold;
        }
      }

      .character-info {
        margin-bottom: @margin-sm;

        .info-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: @margin-xs;

          .info-label {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }

          .info-value {
            color: @text-color;
            font-size: @font-size-sm;
          }
        }
      }

      .character-progress {
        .progress-bar {
          height: 4px;
          background-color: @border-color;
          border-radius: 2px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background-color: @primary-color;
            transition: width @transition-duration;
          }
        }
      }

      &.create-card {
        display: flex;
        align-items: center;
        justify-content: center;
        border-style: dashed;
        min-height: 120px;

        .create-content {
          text-align: center;

          .create-icon {
            font-size: 2rem;
            color: @text-color-secondary;
            margin-bottom: @margin-sm;
          }

          .create-text {
            color: @text-color-secondary;
          }
        }

        &:hover {
          border-color: @primary-color;

          .create-content {
            .create-icon,
            .create-text {
              color: @primary-color;
            }
          }
        }
      }
    }

    .empty-characters {
      grid-column: 1 / -1;
      text-align: center;
      padding: @padding-xl;

      .empty-icon {
        font-size: 3rem;
        margin-bottom: @margin-base;
      }

      .empty-text {
        color: @text-color-secondary;
        margin-bottom: @margin-base;
      }
    }
  }
}

.selection-actions {
  display: flex;
  justify-content: space-between;
  gap: @margin-base;

  .ant-btn {
    flex: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .character-selection-view {
    padding: @padding-base;
  }

  .character-selection-container {
    padding: @padding-lg;
  }

  .characters-list {
    grid-template-columns: 1fr;
  }

  .selection-actions {
    flex-direction: column;
  }
}
</style>
