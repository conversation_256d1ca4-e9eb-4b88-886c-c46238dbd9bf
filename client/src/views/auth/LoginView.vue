<template>
  <div class="login-view">
    <div class="login-container">
      <!-- 游戏标题 -->
      <div class="game-title">
        <h1 class="title-text">足球游戏</h1>
        <p class="subtitle-text">Football Manager</p>
      </div>

      <!-- 登录表单 -->
      <div class="login-form">
        <a-form
          :model="formData"
          :rules="rules"
          @finish="handleLogin"
          layout="vertical"
          size="large"
        >
          <a-form-item name="username" label="用户名">
            <a-input
              v-model:value="formData.username"
              placeholder="请输入用户名"
              :prefix="h(UserOutlined)"
              class="game-input"
            />
          </a-form-item>

          <a-form-item name="password" label="密码">
            <a-input-password
              v-model:value="formData.password"
              placeholder="请输入密码"
              :prefix="h(LockOutlined)"
              class="game-input"
            />
          </a-form-item>

          <a-form-item>
            <a-checkbox v-model:checked="rememberMe">
              记住我
            </a-checkbox>
          </a-form-item>

          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              :loading="isLoading"
              block
              size="large"
              class="login-button"
            >
              登录游戏
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 其他操作 -->
        <div class="login-actions">
          <a-space direction="vertical" style="width: 100%">
            <div class="text-center">
              <a-button type="link" @click="$router.push('/register')">
                还没有账号？立即注册
              </a-button>
            </div>
            <div class="text-center">
              <a-button type="link" size="small">
                忘记密码？
              </a-button>
            </div>
          </a-space>
        </div>
      </div>

      <!-- 服务器状态 -->
      <div class="server-status">
        <div class="status-item">
          <span class="status-label">服务器状态:</span>
          <span class="status-value online">在线</span>
        </div>
        <div class="status-item">
          <span class="status-label">在线玩家:</span>
          <span class="status-value">1,234</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, h } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import type { LoginCredentials } from '@/types'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'

// 状态
const authStore = useAuthStore()
const globalStore = useGlobalStore()
const isLoading = computed(() => globalStore.isLoading)

// 表单数据
const formData = reactive<LoginCredentials>({
  username: '',
  password: ''
})

const rememberMe = ref(false)

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  try {
    await authStore.login(formData)
    
    // 如果选择记住我，保存用户名
    if (rememberMe.value) {
      localStorage.setItem('rememberedUsername', formData.username)
    } else {
      localStorage.removeItem('rememberedUsername')
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 组件挂载时恢复记住的用户名
onMounted(() => {
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  if (rememberedUsername) {
    formData.username = rememberedUsername
    rememberMe.value = true
  }
})
</script>

<style lang="less" scoped>
.login-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #001529 0%, #002140 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  background-color: rgba(0, 33, 64, 0.8);
  border: 1px solid @border-color;
  border-radius: @border-radius-lg;
  padding: 40px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.game-title {
  text-align: center;
  margin-bottom: 40px;
  
  .title-text {
    font-size: 32px;
    font-weight: bold;
    color: @primary-color;
    margin-bottom: 8px;
    font-family: @font-family-mono;
    text-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
  }
  
  .subtitle-text {
    font-size: 14px;
    color: @text-color-secondary;
    font-family: @font-family-mono;
    letter-spacing: 2px;
  }
}

.login-form {
  margin-bottom: 30px;
  
  :deep(.ant-form-item-label > label) {
    color: @text-color;
    font-family: @font-family-mono;
  }
  
  .game-input {
    background-color: rgba(0, 21, 41, 0.6);
    border-color: @border-color;
    color: @text-color;
    
    &:hover {
      border-color: @primary-color;
    }
    
    &:focus {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    }
    
    :deep(.ant-input) {
      background-color: transparent;
      color: @text-color;
      
      &::placeholder {
        color: @text-color-placeholder;
      }
    }
    
    :deep(.anticon) {
      color: @text-color-secondary;
    }
  }
  
  .login-button {
    background-color: @primary-color;
    border-color: @primary-color;
    font-family: @font-family-mono;
    font-weight: bold;
    height: 48px;
    
    &:hover {
      background-color: @primary-color-hover;
      border-color: @primary-color-hover;
    }
  }
  
  :deep(.ant-checkbox-wrapper) {
    color: @text-color-secondary;
    font-family: @font-family-mono;
    
    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: @primary-color;
      border-color: @primary-color;
    }
  }
}

.login-actions {
  :deep(.ant-btn-link) {
    color: @text-color-secondary;
    font-family: @font-family-mono;
    
    &:hover {
      color: @primary-color;
    }
  }
}

.server-status {
  border-top: 1px solid @border-color;
  padding-top: 20px;
  display: flex;
  justify-content: space-between;
  font-family: @font-family-mono;
  font-size: 12px;
  
  .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .status-label {
      color: @text-color-secondary;
      margin-bottom: 4px;
    }
    
    .status-value {
      color: @text-color;
      font-weight: bold;
      
      &.online {
        color: @success-color;
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .game-title .title-text {
    font-size: 24px;
  }
}
</style>
