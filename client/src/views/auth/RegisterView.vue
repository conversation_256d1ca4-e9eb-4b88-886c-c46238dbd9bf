<template>
  <div class="register-view">
    <div class="register-container">
      <div class="register-header">
        <h1 class="register-title">注册账号</h1>
        <p class="register-subtitle">创建您的足球游戏账号</p>
      </div>

      <a-form
        :model="registerForm"
        :rules="registerRules"
        @finish="handleRegister"
        layout="vertical"
        class="register-form"
      >
        <a-form-item label="用户名" name="username">
          <a-input
            v-model:value="registerForm.username"
            placeholder="请输入用户名"
            size="large"
          />
        </a-form-item>

        <a-form-item label="邮箱" name="email">
          <a-input
            v-model:value="registerForm.email"
            type="email"
            placeholder="请输入邮箱地址"
            size="large"
          />
        </a-form-item>

        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="registerForm.password"
            placeholder="请输入密码"
            size="large"
          />
        </a-form-item>

        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password
            v-model:value="registerForm.confirmPassword"
            placeholder="请再次输入密码"
            size="large"
          />
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="isLoading"
          >
            注册
          </a-button>
        </a-form-item>
      </a-form>

      <div class="register-footer">
        <span>已有账号？</span>
        <router-link to="/login" class="login-link">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'
import type { RegisterData } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const globalStore = useGlobalStore()

// 状态
const isLoading = ref(false)

// 表单数据
const registerForm = reactive<RegisterData & { confirmPassword: string }>({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value !== registerForm.password) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 处理注册
const handleRegister = async () => {
  try {
    isLoading.value = true

    const registerData: RegisterData = {
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password,
      confirmPassword: registerForm.confirmPassword
    }

    await authStore.register(registerData)

    globalStore.addNotification({
      type: 'success',
      title: '注册成功',
      message: '账号注册成功，请登录'
    })

    router.push('/login')
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '注册失败',
      message: error.message || '注册失败，请重试'
    })
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.register-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, @bg-color 0%, #002140 100%);
  padding: @padding-lg;
}

.register-container {
  width: 100%;
  max-width: 400px;
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-xl;
  box-shadow: @box-shadow-elevated;
}

.register-header {
  text-align: center;
  margin-bottom: @margin-xl;

  .register-title {
    font-size: @font-size-xxl;
    color: @text-color;
    margin: 0 0 @margin-sm 0;
    font-family: @font-family-mono;
  }

  .register-subtitle {
    color: @text-color-secondary;
    margin: 0;
  }
}

.register-form {
  :deep(.ant-form-item-label) {
    label {
      color: @text-color;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-password) {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: @border-color;
    color: @text-color;

    &:hover,
    &:focus {
      border-color: @primary-color;
    }

    &::placeholder {
      color: @text-color-secondary;
    }
  }

  :deep(.ant-btn-primary) {
    background-color: @primary-color;
    border-color: @primary-color;
    font-family: @font-family-mono;
    font-weight: bold;

    &:hover {
      background-color: @primary-color-hover;
      border-color: @primary-color-hover;
    }
  }
}

.register-footer {
  text-align: center;
  margin-top: @margin-lg;
  color: @text-color-secondary;

  .login-link {
    color: @primary-color;
    text-decoration: none;
    margin-left: @margin-xs;

    &:hover {
      color: @primary-color-hover;
      text-decoration: underline;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .register-view {
    padding: @padding-base;
  }

  .register-container {
    padding: @padding-lg;
  }
}
</style>
