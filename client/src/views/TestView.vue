<template>
  <div class="test-view">
    <h1>测试页面</h1>
    <p>这是一个测试页面，用于验证编译是否正常。</p>
    
    <div class="test-sections">
      <!-- WebSocket连接测试 -->
      <div class="test-section">
        <h2>WebSocket连接测试</h2>
        <p>连接状态: {{ connectionState }}</p>
        <a-button @click="testWebSocketConnection" :loading="isConnecting">
          测试WebSocket连接
        </a-button>
      </div>

      <!-- HTTP请求测试 -->
      <div class="test-section">
        <h2>HTTP请求测试</h2>
        <a-button @click="testHttpRequest" :loading="isHttpTesting">
          测试HTTP请求
        </a-button>
        <div v-if="httpResult" class="test-result">
          <pre>{{ JSON.stringify(httpResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 状态管理测试 -->
      <div class="test-section">
        <h2>状态管理测试</h2>
        <p>全局状态测试</p>
        <a-button @click="testGlobalStore">
          添加通知
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { wsService } from '@/services/websocket'
import { httpService } from '@/services/http'
import { useGlobalStore } from '@/stores/global'

// 状态
const globalStore = useGlobalStore()
const connectionState = ref('未连接')
const isConnecting = ref(false)
const isHttpTesting = ref(false)
const httpResult = ref<any>(null)

// WebSocket连接测试
const testWebSocketConnection = async () => {
  try {
    isConnecting.value = true
    connectionState.value = '连接中...'
    
    await wsService.connect()
    connectionState.value = '已连接'
    
    globalStore.addNotification({
      type: 'success',
      title: 'WebSocket连接成功',
      message: 'WebSocket连接测试成功'
    })
  } catch (error: any) {
    connectionState.value = '连接失败'
    globalStore.addNotification({
      type: 'error',
      title: 'WebSocket连接失败',
      message: error.message
    })
  } finally {
    isConnecting.value = false
  }
}

// HTTP请求测试
const testHttpRequest = async () => {
  try {
    isHttpTesting.value = true
    
    // 测试一个简单的HTTP请求
    const response = await httpService.get('/api/health')
    httpResult.value = response
    
    globalStore.addNotification({
      type: 'success',
      title: 'HTTP请求成功',
      message: 'HTTP请求测试成功'
    })
  } catch (error: any) {
    httpResult.value = { error: error.message }
    globalStore.addNotification({
      type: 'error',
      title: 'HTTP请求失败',
      message: error.message
    })
  } finally {
    isHttpTesting.value = false
  }
}

// 状态管理测试
const testGlobalStore = () => {
  globalStore.addNotification({
    type: 'info',
    title: '状态管理测试',
    message: '这是一个测试通知，用于验证状态管理是否正常工作'
  })
}
</script>

<style lang="less" scoped>
.test-view {
  padding: @padding-lg;
  max-width: 800px;
  margin: 0 auto;
  
  h1 {
    color: @text-color;
    text-align: center;
    margin-bottom: @margin-lg;
  }
  
  .test-sections {
    display: flex;
    flex-direction: column;
    gap: @margin-lg;
  }
  
  .test-section {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    
    h2 {
      color: @text-color;
      margin-bottom: @margin-base;
    }
    
    p {
      color: @text-color-secondary;
      margin-bottom: @margin-base;
    }
    
    .test-result {
      margin-top: @margin-base;
      background-color: rgba(0, 0, 0, 0.3);
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-sm;
      
      pre {
        color: @text-color;
        font-family: @font-family-mono;
        font-size: @font-size-sm;
        margin: 0;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
</style>
