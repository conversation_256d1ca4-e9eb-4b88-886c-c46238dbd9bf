/**
 * 球员属性管理组合式函数
 * 解决嵌套数据操作的Vue3最佳实践
 */

import { computed, ref, watch, type Ref } from 'vue'
import type { Hero } from '@/types/generated/hero'

export interface AttributeUpdate {
  attribute: keyof Hero['attributes']
  oldValue: number
  newValue: number
  change: number
}

export interface AttributeHistory {
  timestamp: number
  updates: AttributeUpdate[]
  reason: string // 'training' | 'evolution' | 'equipment' | 'formation'
}

/**
 * 球员属性管理组合式函数
 */
export function useHeroAttributes(hero: Ref<Hero | null>) {
  // 属性变更历史
  const attributeHistory = ref<AttributeHistory[]>([])
  
  // 计算属性 - 总评分
  const overallRating = computed(() => {
    if (!hero.value?.attributes) return 0
    
    const attrs = hero.value.attributes
    const weights = {
      pace: 0.15,
      shooting: 0.20,
      passing: 0.15,
      dribbling: 0.15,
      defending: 0.15,
      physical: 0.15,
      goalkeeping: hero.value.position === 'GK' ? 0.05 : 0
    }
    
    return Math.round(
      (attrs.pace || 0) * weights.pace +
      (attrs.shooting || 0) * weights.shooting +
      (attrs.passing || 0) * weights.passing +
      (attrs.dribbling || 0) * weights.dribbling +
      (attrs.defending || 0) * weights.defending +
      (attrs.physical || 0) * weights.physical +
      (attrs.goalkeeping || 0) * weights.goalkeeping
    )
  })
  
  // 计算属性 - 位置适应性
  const positionFit = computed(() => {
    if (!hero.value?.attributes || !hero.value.position) return 0
    
    const attrs = hero.value.attributes
    const position = hero.value.position
    
    switch (position) {
      case 'GK':
        return Math.round((attrs.goalkeeping || 0) * 0.8 + (attrs.physical || 0) * 0.2)
      case 'DEF':
        return Math.round((attrs.defending || 0) * 0.4 + (attrs.physical || 0) * 0.3 + (attrs.pace || 0) * 0.3)
      case 'MID':
        return Math.round((attrs.passing || 0) * 0.3 + (attrs.dribbling || 0) * 0.3 + (attrs.physical || 0) * 0.2 + (attrs.pace || 0) * 0.2)
      case 'ATT':
        return Math.round((attrs.shooting || 0) * 0.4 + (attrs.dribbling || 0) * 0.3 + (attrs.pace || 0) * 0.3)
      default:
        return overallRating.value
    }
  })
  
  // 计算属性 - 属性分布
  const attributeDistribution = computed(() => {
    if (!hero.value?.attributes) return []
    
    const attrs = hero.value.attributes
    return [
      { name: '速度', value: attrs.pace || 0, color: '#ff6b6b' },
      { name: '射门', value: attrs.shooting || 0, color: '#4ecdc4' },
      { name: '传球', value: attrs.passing || 0, color: '#45b7d1' },
      { name: '盘带', value: attrs.dribbling || 0, color: '#f9ca24' },
      { name: '防守', value: attrs.defending || 0, color: '#6c5ce7' },
      { name: '身体', value: attrs.physical || 0, color: '#a0e7e5' },
      ...(hero.value.position === 'GK' ? [
        { name: '门将', value: attrs.goalkeeping || 0, color: '#fd79a8' }
      ] : [])
    ]
  })
  
  /**
   * 更新单个属性 - 响应式安全
   */
  const updateAttribute = (
    attribute: keyof Hero['attributes'], 
    newValue: number, 
    reason: string = 'manual'
  ) => {
    if (!hero.value?.attributes) return
    
    const oldValue = hero.value.attributes[attribute] || 0
    const change = newValue - oldValue
    
    // 记录变更历史
    const update: AttributeUpdate = {
      attribute,
      oldValue,
      newValue,
      change
    }
    
    addAttributeHistory([update], reason)
    
    // 触发响应式更新
    hero.value = {
      ...hero.value,
      attributes: {
        ...hero.value.attributes,
        [attribute]: newValue
      }
    }
  }
  
  /**
   * 批量更新属性 - 响应式安全
   */
  const updateAttributes = (
    updates: Partial<Hero['attributes']>, 
    reason: string = 'batch'
  ) => {
    if (!hero.value?.attributes) return
    
    const attributeUpdates: AttributeUpdate[] = []
    
    // 收集所有变更
    Object.entries(updates).forEach(([key, newValue]) => {
      const attribute = key as keyof Hero['attributes']
      const oldValue = hero.value!.attributes[attribute] || 0
      const change = (newValue || 0) - oldValue
      
      if (change !== 0) {
        attributeUpdates.push({
          attribute,
          oldValue,
          newValue: newValue || 0,
          change
        })
      }
    })
    
    if (attributeUpdates.length > 0) {
      addAttributeHistory(attributeUpdates, reason)
      
      // 触发响应式更新
      hero.value = {
        ...hero.value,
        attributes: {
          ...hero.value.attributes,
          ...updates
        }
      }
    }
  }
  
  /**
   * 添加属性变更历史
   */
  const addAttributeHistory = (updates: AttributeUpdate[], reason: string) => {
    attributeHistory.value.unshift({
      timestamp: Date.now(),
      updates,
      reason
    })
    
    // 保留最近100条记录
    if (attributeHistory.value.length > 100) {
      attributeHistory.value = attributeHistory.value.slice(0, 100)
    }
  }
  
  /**
   * 获取属性变更趋势
   */
  const getAttributeTrend = (attribute: keyof Hero['attributes'], days: number = 7) => {
    const cutoff = Date.now() - (days * 24 * 60 * 60 * 1000)
    
    return attributeHistory.value
      .filter(h => h.timestamp >= cutoff)
      .flatMap(h => h.updates)
      .filter(u => u.attribute === attribute)
      .reduce((sum, u) => sum + u.change, 0)
  }
  
  /**
   * 重置属性历史
   */
  const clearAttributeHistory = () => {
    attributeHistory.value = []
  }
  
  // 监听hero变化，自动清理历史
  watch(
    () => hero.value?.heroId,
    (newHeroId, oldHeroId) => {
      if (newHeroId !== oldHeroId) {
        clearAttributeHistory()
      }
    }
  )
  
  return {
    // 计算属性
    overallRating,
    positionFit,
    attributeDistribution,
    
    // 状态
    attributeHistory: readonly(attributeHistory),
    
    // 方法
    updateAttribute,
    updateAttributes,
    getAttributeTrend,
    clearAttributeHistory
  }
}
