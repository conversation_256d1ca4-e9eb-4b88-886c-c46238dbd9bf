import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import type { RouteMeta } from '@/types'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: {
      title: '登录',
      layout: 'auth',
      requiresAuth: false
    } as RouteMeta
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/RegisterView.vue'),
    meta: {
      title: '注册',
      layout: 'auth',
      requiresAuth: false
    } as RouteMeta
  },
  {
    path: '/server-selection',
    name: 'ServerSelection',
    component: () => import('@/views/auth/ServerSelectionView.vue'),
    meta: {
      title: '选择服务器',
      layout: 'auth',
      requiresAuth: true
    } as RouteMeta
  },
  {
    path: '/character-selection',
    name: 'CharacterSelection',
    component: () => import('@/views/auth/CharacterSelectionView.vue'),
    meta: {
      title: '选择角色',
      layout: 'auth',
      requiresAuth: true
    } as RouteMeta
  },
  {
    path: '/game',
    name: 'Game',
    component: () => import('@/views/game/GameLayout.vue'),
    meta: {
      title: '游戏',
      layout: 'game',
      requiresAuth: true,
      requiresCharacter: true
    } as RouteMeta,
    children: [
      {
        path: '',
        redirect: '/game/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/game/DashboardView.vue'),
        meta: {
          title: '主页',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'heroes',
        name: 'Heroes',
        component: () => import('@/views/game/HeroesView.vue'),
        meta: {
          title: '球员管理',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'heroes/:id',
        name: 'HeroDetail',
        component: () => import('@/views/game/HeroDetailView.vue'),
        meta: {
          title: '球员详情'
        } as RouteMeta
      },
      {
        path: 'formations',
        name: 'Formations',
        component: () => import('@/views/game/FormationsView.vue'),
        meta: {
          title: '战术配置',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'matches',
        name: 'Matches',
        component: () => import('@/views/game/MatchesView.vue'),
        meta: {
          title: '比赛',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'matches/:id',
        name: 'MatchDetail',
        component: () => import('@/views/game/MatchDetailView.vue'),
        meta: {
          title: '比赛详情'
        } as RouteMeta
      },
      {
        path: 'inventory',
        name: 'Inventory',
        component: () => import('@/views/game/InventoryView.vue'),
        meta: {
          title: '背包',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'training',
        name: 'Training',
        component: () => import('@/views/game/TrainingView.vue'),
        meta: {
          title: '训练',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'market',
        name: 'Market',
        component: () => import('@/views/game/MarketView.vue'),
        meta: {
          title: '转会市场',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'guild',
        name: 'Guild',
        component: () => import('@/views/game/GuildView.vue'),
        meta: {
          title: '公会',
          keepAlive: true
        } as RouteMeta
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/game/SettingsView.vue'),
        meta: {
          title: '设置'
        } as RouteMeta
      },
      {
        path: 'test',
        name: 'Test',
        component: () => import('@/views/TestView.vue'),
        meta: {
          title: '测试页面'
        } as RouteMeta
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/system/NotFoundView.vue'),
    meta: {
      title: '页面不存在',
      layout: 'default'
    } as RouteMeta
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const globalStore = useGlobalStore()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 足球游戏`
  }

  // 检查认证要求
  if (to.meta?.requiresAuth && !authStore.isAuthenticated) {
    globalStore.addNotification({
      type: 'warning',
      title: '需要登录',
      message: '请先登录您的账号'
    })
    next('/login')
    return
  }

  // 检查角色选择要求
  if (to.meta?.requiresCharacter && !authStore.hasSelectedCharacter) {
    if (!authStore.hasSelectedServer) {
      next('/server-selection')
    } else {
      next('/character-selection')
    }
    return
  }

  // 已登录用户访问认证页面时重定向
  if (authStore.isAuthenticated && ['Login', 'Register'].includes(to.name as string)) {
    if (authStore.canEnterGame) {
      next('/game')
    } else if (authStore.hasSelectedServer) {
      next('/character-selection')
    } else {
      next('/server-selection')
    }
    return
  }

  next()
})

router.afterEach((to, from) => {
  // 路由切换后的处理
  console.log(`[Router] 从 ${from.path} 导航到 ${to.path}`)
})

export default router
