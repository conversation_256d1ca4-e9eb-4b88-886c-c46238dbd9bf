// 导入变量和混合
@import './variables.less';
@import './mixins.less';

// 全局样式重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: @font-family-base;
  background-color: @bg-color;
  color: @text-color;
  line-height: 1.6;
}

#app {
  height: 100%;
  min-height: 100vh;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: @bg-color;
}

::-webkit-scrollbar-thumb {
  background: @border-color;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: @primary-color;
}

// 全局文字样式
h1, h2, h3, h4, h5, h6 {
  color: @text-color;
  font-family: @font-family-mono;
  margin: 0;
}

p {
  color: @text-color-secondary;
  margin: 0 0 @margin-base 0;
}

a {
  color: @primary-color;
  text-decoration: none;
  transition: color @transition-duration;
}

a:hover {
  color: @primary-color-hover;
}

// 游戏专用样式类
.game-container {
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base;
  margin-bottom: @margin-base;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @margin-base;
  padding-bottom: @padding-sm;
  border-bottom: 1px solid @border-color;
}

.game-header .header-title {
  font-size: @font-size-lg;
  font-weight: bold;
  color: @text-color;
  font-family: @font-family-mono;
}

.game-header .header-actions {
  display: flex;
  gap: @margin-sm;
}

.game-content {
  color: @text-color-secondary;
  line-height: 1.6;
}

.game-grid {
  display: grid;
  gap: @margin-base;
}

.game-grid.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.game-grid.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.game-grid.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.game-grid.grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.game-card {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base;
  transition: all @transition-duration;
  cursor: pointer;
}

.game-card:hover {
  border-color: @primary-color;
  transform: translateY(-2px);
  box-shadow: @box-shadow-elevated;
}

.game-card.selected {
  border-color: @primary-color;
  background-color: rgba(82, 196, 26, 0.1);
}

.game-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.game-card.disabled:hover {
  transform: none;
  border-color: @border-color;
}

// 响应式工具类
@media (max-width: 768px) {
  .game-grid.grid-2,
  .game-grid.grid-3,
  .game-grid.grid-4 {
    grid-template-columns: 1fr;
  }
  
  .game-grid.grid-auto {
    grid-template-columns: 1fr;
  }
  
  .game-header {
    flex-direction: column;
    align-items: flex-start;
    gap: @margin-sm;
  }
}
