// 全局样式文件

// 导入变量
@import './variables.less';
@import './mixins.less';

// 全局重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: @font-family-base;
  font-size: @font-size-base;
  line-height: @line-height-base;
  color: @text-color;
  background-color: @bg-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: @border-color;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: @primary-color;
  border-radius: 4px;
  
  &:hover {
    background: @primary-color-hover;
  }
}

// 文字游戏风格基础类
.game-container {
  background-color: @bg-color;
  color: @text-color;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base;
  font-family: @font-family-mono;
}

.game-text {
  color: @text-color;
  font-family: @font-family-mono;
  line-height: @line-height-base;
  
  &.highlight {
    color: @highlight-color;
    font-weight: bold;
  }
  
  &.success {
    color: @success-color;
  }
  
  &.warning {
    color: @warning-color;
  }
  
  &.error {
    color: @error-color;
  }
  
  &.info {
    color: @info-color;
  }
}

// 按钮样式
.game-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: @padding-sm @padding-base;
  border: 1px solid @primary-color;
  background-color: transparent;
  color: @primary-color;
  font-family: @font-family-mono;
  font-size: @font-size-base;
  cursor: pointer;
  transition: all @transition-duration;
  border-radius: @border-radius-base;
  text-decoration: none;
  
  &:hover {
    background-color: @primary-color;
    color: @bg-color;
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background-color: transparent;
      color: @primary-color;
      transform: none;
    }
  }
  
  &.primary {
    background-color: @primary-color;
    color: @bg-color;
    
    &:hover {
      background-color: @primary-color-hover;
    }
  }
  
  &.secondary {
    border-color: @text-color-secondary;
    color: @text-color-secondary;
    
    &:hover {
      background-color: @text-color-secondary;
      color: @bg-color;
    }
  }
  
  &.danger {
    border-color: @error-color;
    color: @error-color;
    
    &:hover {
      background-color: @error-color;
      color: @bg-color;
    }
  }
  
  &.small {
    padding: @padding-xs @padding-sm;
    font-size: @font-size-sm;
  }
  
  &.large {
    padding: @padding-base @padding-lg;
    font-size: @font-size-lg;
  }
}

// 卡片样式
.game-card {
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-base;
  margin-bottom: @margin-base;
  transition: all @transition-duration;
  
  &:hover {
    border-color: @primary-color;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-sm;
    padding-bottom: @padding-sm;
    border-bottom: 1px solid @border-color;
    
    .card-title {
      font-size: @font-size-lg;
      font-weight: bold;
      color: @text-color;
    }
    
    .card-extra {
      color: @text-color-secondary;
      font-size: @font-size-sm;
    }
  }
  
  .card-content {
    color: @text-color-secondary;
    line-height: @line-height-base;
  }
  
  .card-actions {
    margin-top: @margin-base;
    padding-top: @padding-sm;
    border-top: 1px solid @border-color;
    display: flex;
    gap: @margin-sm;
    justify-content: flex-end;
  }
}

// 表格样式
.game-table {
  width: 100%;
  border-collapse: collapse;
  font-family: @font-family-mono;
  
  th, td {
    padding: @padding-sm @padding-base;
    text-align: left;
    border-bottom: 1px solid @border-color;
  }
  
  th {
    background-color: @card-bg;
    color: @text-color;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  td {
    color: @text-color-secondary;
  }
  
  tr {
    transition: background-color @transition-duration;
    
    &:hover {
      background-color: rgba(82, 196, 26, 0.05);
    }
  }
  
  .table-actions {
    display: flex;
    gap: @margin-xs;
    justify-content: center;
  }
}

// 表单样式
.game-form {
  .form-item {
    margin-bottom: @margin-base;
    
    .form-label {
      display: block;
      margin-bottom: @margin-xs;
      color: @text-color;
      font-weight: bold;
    }
    
    .form-input {
      width: 100%;
      padding: @padding-sm @padding-base;
      border: 1px solid @border-color;
      background-color: @bg-color;
      color: @text-color;
      font-family: @font-family-mono;
      border-radius: @border-radius-base;
      transition: border-color @transition-duration;
      
      &:focus {
        outline: none;
        border-color: @primary-color;
        box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
      }
      
      &::placeholder {
        color: @text-color-placeholder;
      }
    }
    
    .form-error {
      margin-top: @margin-xs;
      color: @error-color;
      font-size: @font-size-sm;
    }
  }
}

// 布局样式
.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.layout-header {
  flex-shrink: 0;
  background-color: @card-bg;
  border-bottom: 1px solid @border-color;
  padding: @padding-base @padding-lg;
}

.layout-content {
  flex: 1;
  overflow: auto;
  padding: @padding-lg;
}

.layout-sidebar {
  width: 280px;
  background-color: @card-bg;
  border-right: 1px solid @border-color;
  padding: @padding-base;
  overflow-y: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .layout-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid @border-color;
  }
  
  .game-card {
    margin-bottom: @margin-sm;
    padding: @padding-sm;
  }
  
  .game-table {
    font-size: @font-size-sm;
    
    th, td {
      padding: @padding-xs @padding-sm;
    }
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: @margin-xs; }
.mb-2 { margin-bottom: @margin-sm; }
.mb-3 { margin-bottom: @margin-base; }
.mb-4 { margin-bottom: @margin-lg; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: @margin-xs; }
.mt-2 { margin-top: @margin-sm; }
.mt-3 { margin-top: @margin-base; }
.mt-4 { margin-top: @margin-lg; }

.p-0 { padding: 0; }
.p-1 { padding: @padding-xs; }
.p-2 { padding: @padding-sm; }
.p-3 { padding: @padding-base; }
.p-4 { padding: @padding-lg; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-end { justify-content: flex-end; }

.w-full { width: 100%; }
.h-full { height: 100%; }
