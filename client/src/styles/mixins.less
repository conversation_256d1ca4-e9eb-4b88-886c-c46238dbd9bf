// Less混入函数

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
.text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略
.text-ellipsis-multiline(@lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 居中对齐
.center-absolute() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.center-flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 响应式断点 - 使用detached rulesets正确语法
.respond-to-xs(@rules) {
  @media (max-width: 767px) {
    @rules();
  }
}

.respond-to-sm(@rules) {
  @media (min-width: 768px) and (max-width: 991px) {
    @rules();
  }
}

.respond-to-md(@rules) {
  @media (min-width: 992px) and (max-width: 1199px) {
    @rules();
  }
}

.respond-to-lg(@rules) {
  @media (min-width: 1200px) and (max-width: 1599px) {
    @rules();
  }
}

.respond-to-xl(@rules) {
  @media (min-width: 1600px) {
    @rules();
  }
}

// 修复：将嵌套的混合器移出媒体查询块
// 按钮样式混入
.button-variant(@color; @bg; @border) {
  color: @color;
  background-color: @bg;
  border-color: @border;

  &:hover {
    color: @color;
    background-color: lighten(@bg, 10%);
    border-color: lighten(@border, 10%);
  }

  &:active {
    color: @color;
    background-color: darken(@bg, 5%);
    border-color: darken(@border, 5%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      background-color: @bg;
      border-color: @border;
    }
  }
}

// 卡片样式混入
.card-variant(@bg; @border) {
  background-color: @bg;
  border: 1px solid @border;
  border-radius: @border-radius-base;

  &:hover {
    border-color: @primary-color;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
  }
}

// 输入框样式混入
.input-variant(@bg; @border; @color) {
  background-color: @bg;
  border: 1px solid @border;
  color: @color;

  &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
  }

  &::placeholder {
    color: @text-color-placeholder;
  }
}

// 阴影混入
.box-shadow(@shadow) {
  box-shadow: @shadow;
}

// 过渡动画混入
.transition(@property: all; @duration: 0.3s; @timing: ease) {
  transition: @property @duration @timing;
}

// 渐变背景混入
.gradient-bg(@start-color; @end-color; @direction: to bottom) {
  background: linear-gradient(@direction, @start-color, @end-color);
}

// 游戏专用混合器
.game-card-hover() {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.game-button(@color: #52c41a) {
  background: linear-gradient(135deg, @color 0%, darken(@color, 10%) 100%);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px fade(@color, 40%);
  }

  &:active {
    transform: translateY(0);
  }
}

.game-panel() {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  color: white;
}

// 游戏特定混入
// 稀有度样式
.rarity-variant(@rarity) when (@rarity = common) {
  border-color: @rarity-common;
  color: @rarity-common;
}
.rarity-variant(@rarity) when (@rarity = uncommon) {
  border-color: @rarity-uncommon;
  color: @rarity-uncommon;
}
.rarity-variant(@rarity) when (@rarity = rare) {
  border-color: @rarity-rare;
  color: @rarity-rare;
}
.rarity-variant(@rarity) when (@rarity = epic) {
  border-color: @rarity-epic;
  color: @rarity-epic;
}
.rarity-variant(@rarity) when (@rarity = legendary) {
  border-color: @rarity-legendary;
  color: @rarity-legendary;
}
.rarity-variant(@rarity) when (@rarity = mythic) {
  border-color: @rarity-mythic;
  color: @rarity-mythic;
}

// 位置颜色样式
.position-variant(@position) when (@position = gk) {
  color: @position-gk;
  border-color: @position-gk;
}
.position-variant(@position) when (@position = def) {
  color: @position-def;
  border-color: @position-def;
}
.position-variant(@position) when (@position = mid) {
  color: @position-mid;
  border-color: @position-mid;
}
.position-variant(@position) when (@position = att) {
  color: @position-att;
  border-color: @position-att;
}

// 状态指示器
.status-indicator(@status) when (@status = online) {
  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: @status-online;
    margin-right: 6px;
  }
}
.status-indicator(@status) when (@status = offline) {
  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: @status-offline;
    margin-right: 6px;
  }
}

// 加载动画
.loading-spinner(@size: 20px; @color: @primary-color) {
  width: @size;
  height: @size;
  border: 2px solid fade(@color, 20%);
  border-top: 2px solid @color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 脉冲动画
.pulse-animation(@color: @primary-color) {
  animation: pulse 2s infinite;
}

// 将keyframes移到混入外部
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

// 打字机效果
.typewriter-effect(@duration: 3s) {
  overflow: hidden;
  border-right: 2px solid @primary-color;
  white-space: nowrap;
  animation: typing @duration steps(40, end), blink-caret 0.75s step-end infinite;

  @keyframes typing {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }

  @keyframes blink-caret {
    from, to {
      border-color: transparent;
    }
    50% {
      border-color: @primary-color;
    }
  }
}

// 滑入动画
.slide-in(@direction: left; @distance: 100px; @duration: 0.5s) {
  animation: ~"slide-in-@{direction}" @duration ease-out;

  @keyframes slide-in-left {
    from {
      transform: translateX(-@distance);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-right {
    from {
      transform: translateX(@distance);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-top {
    from {
      transform: translateY(-@distance);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-bottom {
    from {
      transform: translateY(@distance);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}