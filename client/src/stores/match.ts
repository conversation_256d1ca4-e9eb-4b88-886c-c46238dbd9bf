import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Match, MatchResult, Tournament, League, MatchEvent } from '@/types'
import { wsService } from '@/services/websocket'
import { useGlobalStore } from './global'

/**
 * 比赛状态管理
 * 管理比赛数据、实时比赛、联赛、锦标赛等功能
 */
export const useMatchStore = defineStore('match', () => {
  const globalStore = useGlobalStore()

  // 状态定义
  const matches = ref<Match[]>([])
  const currentMatch = ref<Match | null>(null)
  const matchHistory = ref<Match[]>([])
  const currentLeague = ref<League | null>(null)
  const leagueMatches = ref<any[]>([])
  const tournaments = ref<Tournament[]>([])
  const isLoading = ref(false)
  const isMatching = ref(false)

  // 统计数据
  const matchStats = ref({
    totalMatches: 0,
    wins: 0,
    draws: 0,
    losses: 0,
    goalsFor: 0,
    goalsAgainst: 0,
    currentPoints: 0,
    todayMatches: 0
  })

  // 计算属性
  const winRate = computed(() => {
    if (matchStats.value.totalMatches === 0) return 0
    return Math.round((matchStats.value.wins / matchStats.value.totalMatches) * 100)
  })

  const goalDifference = computed(() => {
    return matchStats.value.goalsFor - matchStats.value.goalsAgainst
  })

  const leagueProgress = computed(() => {
    if (!currentLeague.value) return 0
    return Math.round((currentLeague.value.currentPoints / currentLeague.value.requiredPoints) * 100)
  })

  const todayMatchCount = computed(() => matchStats.value.todayMatches)
  const currentPoints = computed(() => matchStats.value.currentPoints)

  // 动作方法

  /**
   * 获取比赛数据
   */
  const fetchMatches = async () => {
    try {
      isLoading.value = true
      
      const response = await wsService.sendMessage('match.getList', {})
      
      if (response.code === 0) {
        matches.value = response.data.matches || []
        matchStats.value = response.data.stats || matchStats.value
      } else {
        throw new Error(response.message || '获取比赛数据失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取比赛失败',
        message: error.message || '无法获取比赛数据'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取联赛信息
   */
  const fetchLeagueInfo = async () => {
    try {
      const response = await wsService.sendMessage('match.getLeagueInfo', {})
      
      if (response.code === 0) {
        currentLeague.value = response.data.league
        leagueMatches.value = response.data.matches || []
      } else {
        throw new Error(response.message || '获取联赛信息失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取联赛信息失败',
        message: error.message
      })
    }
  }

  /**
   * 获取锦标赛列表
   */
  const fetchTournaments = async () => {
    try {
      const response = await wsService.sendMessage('match.getTournaments', {})
      
      if (response.code === 0) {
        tournaments.value = response.data || []
      } else {
        throw new Error(response.message || '获取锦标赛列表失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取锦标赛失败',
        message: error.message
      })
    }
  }

  /**
   * 开始快速比赛
   */
  const startQuickMatch = async (): Promise<Match | null> => {
    try {
      isMatching.value = true
      
      const response = await wsService.sendMessage('match.startQuick', {})
      
      if (response.code === 0) {
        const match = response.data
        currentMatch.value = match
        
        globalStore.addNotification({
          type: 'success',
          title: '比赛开始',
          message: '快速比赛已开始'
        })
        
        return match
      } else {
        throw new Error(response.message || '开始快速比赛失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '比赛开始失败',
        message: error.message
      })
      return null
    } finally {
      isMatching.value = false
    }
  }

  /**
   * 开始PVE挑战
   */
  const startPveMatch = async (difficulty: number = 1): Promise<Match | null> => {
    try {
      const response = await wsService.sendMessage('match.startPve', { difficulty })
      
      if (response.code === 0) {
        const match = response.data
        currentMatch.value = match
        
        globalStore.addNotification({
          type: 'success',
          title: 'PVE挑战开始',
          message: '挑战赛已开始'
        })
        
        return match
      } else {
        throw new Error(response.message || '开始PVE挑战失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: 'PVE挑战失败',
        message: error.message
      })
      return null
    }
  }

  /**
   * 开始PVP匹配
   */
  const startPvpMatching = async (): Promise<boolean> => {
    try {
      isMatching.value = true
      
      const response = await wsService.sendMessage('match.startPvpMatching', {})
      
      if (response.code === 0) {
        globalStore.addNotification({
          type: 'info',
          title: '开始匹配',
          message: '正在寻找合适的对手...'
        })
        
        return true
      } else {
        throw new Error(response.message || '开始PVP匹配失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: 'PVP匹配失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 取消PVP匹配
   */
  const cancelPvpMatching = async (): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('match.cancelPvpMatching', {})
      
      if (response.code === 0) {
        isMatching.value = false
        
        globalStore.addNotification({
          type: 'info',
          title: '取消匹配',
          message: '已取消PVP匹配'
        })
        
        return true
      } else {
        throw new Error(response.message || '取消匹配失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '取消匹配失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 开始联赛比赛
   */
  const startLeagueMatch = async (matchId: string): Promise<Match | null> => {
    try {
      const response = await wsService.sendMessage('match.startLeague', { matchId })
      
      if (response.code === 0) {
        const match = response.data
        currentMatch.value = match
        
        globalStore.addNotification({
          type: 'success',
          title: '联赛比赛开始',
          message: '联赛比赛已开始'
        })
        
        return match
      } else {
        throw new Error(response.message || '开始联赛比赛失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '联赛比赛失败',
        message: error.message
      })
      return null
    }
  }

  /**
   * 参加锦标赛
   */
  const joinTournament = async (tournamentId: string): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('match.joinTournament', { tournamentId })
      
      if (response.code === 0) {
        // 更新锦标赛数据
        const tournament = tournaments.value.find(t => t.id === tournamentId)
        if (tournament) {
          tournament.participants += 1
          tournament.isParticipant = true
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '报名成功',
          message: '成功报名参加锦标赛'
        })
        
        return true
      } else {
        throw new Error(response.message || '报名锦标赛失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '报名失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 退出锦标赛
   */
  const leaveTournament = async (tournamentId: string): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('match.leaveTournament', { tournamentId })
      
      if (response.code === 0) {
        // 更新锦标赛数据
        const tournament = tournaments.value.find(t => t.id === tournamentId)
        if (tournament) {
          tournament.participants -= 1
          tournament.isParticipant = false
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '退出成功',
          message: '已退出锦标赛'
        })
        
        return true
      } else {
        throw new Error(response.message || '退出锦标赛失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '退出失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 获取比赛历史
   */
  const fetchMatchHistory = async (page: number = 1, pageSize: number = 20) => {
    try {
      const response = await wsService.sendMessage('match.getHistory', {
        page,
        pageSize
      })
      
      if (response.code === 0) {
        matchHistory.value = response.data.matches || []
        return response.data
      } else {
        throw new Error(response.message || '获取比赛历史失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取历史失败',
        message: error.message
      })
      return null
    }
  }

  /**
   * 获取比赛详情
   */
  const fetchMatchDetail = async (matchId: string): Promise<Match | null> => {
    try {
      const response = await wsService.sendMessage('match.getDetail', { matchId })
      
      if (response.code === 0) {
        return response.data
      } else {
        throw new Error(response.message || '获取比赛详情失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取比赛详情失败',
        message: error.message
      })
      return null
    }
  }

  /**
   * 处理比赛事件
   */
  const handleMatchEvent = (event: MatchEvent) => {
    if (currentMatch.value && currentMatch.value.id === event.matchId) {
      // 更新当前比赛数据
      if (!currentMatch.value.events) {
        currentMatch.value.events = []
      }
      currentMatch.value.events.push(event)
      
      // 更新比分
      if (event.type === 'goal') {
        if (event.team === 'home') {
          currentMatch.value.homeScore += 1
        } else {
          currentMatch.value.awayScore += 1
        }
      }
      
      // 更新比赛时间
      if (event.minute) {
        currentMatch.value.currentTime = event.minute
      }
    }
  }

  /**
   * 处理比赛结束
   */
  const handleMatchFinished = (result: MatchResult) => {
    if (currentMatch.value) {
      currentMatch.value.status = 'finished'
      currentMatch.value.result = result
      
      // 更新统计数据
      matchStats.value.totalMatches += 1
      matchStats.value.todayMatches += 1
      
      if (result.isWin) {
        matchStats.value.wins += 1
        matchStats.value.currentPoints += 3
      } else if (result.isDraw) {
        matchStats.value.draws += 1
        matchStats.value.currentPoints += 1
      } else {
        matchStats.value.losses += 1
      }
      
      matchStats.value.goalsFor += result.goalsFor || 0
      matchStats.value.goalsAgainst += result.goalsAgainst || 0
      
      // 添加到历史记录
      matchHistory.value.unshift({ ...currentMatch.value })
      
      // 清除当前比赛
      currentMatch.value = null
    }
  }

  /**
   * 设置当前比赛
   */
  const setCurrentMatch = (match: Match | null) => {
    currentMatch.value = match
  }

  /**
   * 清空比赛数据
   */
  const clearMatches = () => {
    matches.value = []
    currentMatch.value = null
    matchHistory.value = []
    currentLeague.value = null
    leagueMatches.value = []
    tournaments.value = []
    matchStats.value = {
      totalMatches: 0,
      wins: 0,
      draws: 0,
      losses: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      currentPoints: 0,
      todayMatches: 0
    }
  }

  return {
    // 状态
    matches: readonly(matches),
    currentMatch: readonly(currentMatch),
    matchHistory: readonly(matchHistory),
    currentLeague: readonly(currentLeague),
    leagueMatches: readonly(leagueMatches),
    tournaments: readonly(tournaments),
    matchStats: readonly(matchStats),
    isLoading: readonly(isLoading),
    isMatching: readonly(isMatching),
    
    // 计算属性
    winRate,
    goalDifference,
    leagueProgress,
    todayMatchCount,
    currentPoints,
    
    // 方法
    fetchMatches,
    fetchLeagueInfo,
    fetchTournaments,
    startQuickMatch,
    startPveMatch,
    startPvpMatching,
    cancelPvpMatching,
    startLeagueMatch,
    joinTournament,
    leaveTournament,
    fetchMatchHistory,
    fetchMatchDetail,
    handleMatchEvent,
    handleMatchFinished,
    setCurrentMatch,
    clearMatches
  }
})
