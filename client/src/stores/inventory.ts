import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { InventoryItem, InventoryTab, UseItemResult } from '@/types'
import { wsService } from '@/services/websocket'
import { useGlobalStore } from './global'

/**
 * 背包状态管理
 * 管理道具背包、物品使用、背包扩展等功能
 */
export const useInventoryStore = defineStore('inventory', () => {
  const globalStore = useGlobalStore()

  // 状态定义
  const inventory = ref<any>(null)
  const inventoryTabs = ref<InventoryTab[]>([])
  const isLoading = ref(false)

  // 计算属性
  const usedSlots = computed(() => {
    if (!inventory.value) return 0
    return inventoryTabs.value.reduce((total, tab) => {
      return total + (tab.items?.filter(item => item).length || 0)
    }, 0)
  })

  const totalCapacity = computed(() => {
    if (!inventory.value) return 0
    return inventoryTabs.value.reduce((total, tab) => {
      return total + (tab.capacity || 0)
    }, 0)
  })

  const usageRate = computed(() => {
    if (totalCapacity.value === 0) return 0
    return Math.round((usedSlots.value / totalCapacity.value) * 100)
  })

  const canExpand = computed(() => {
    return totalCapacity.value < 200 // 假设最大容量为200
  })

  // 按类型分组的道具
  const itemsByType = computed(() => {
    const grouped: Record<string, InventoryItem[]> = {}
    
    inventoryTabs.value.forEach(tab => {
      if (tab.items) {
        tab.items.forEach(item => {
          if (item && item.type) {
            if (!grouped[item.type]) {
              grouped[item.type] = []
            }
            grouped[item.type].push(item)
          }
        })
      }
    })
    
    return grouped
  })

  // 按稀有度分组的道具
  const itemsByRarity = computed(() => {
    const grouped: Record<string, InventoryItem[]> = {}
    
    inventoryTabs.value.forEach(tab => {
      if (tab.items) {
        tab.items.forEach(item => {
          if (item && item.rarity) {
            if (!grouped[item.rarity]) {
              grouped[item.rarity] = []
            }
            grouped[item.rarity].push(item)
          }
        })
      }
    })
    
    return grouped
  })

  // 动作方法

  /**
   * 获取背包数据
   */
  const fetchInventory = async () => {
    try {
      isLoading.value = true
      
      const response = await wsService.sendMessage('inventory.getBag', {})
      
      if (response.code === 0) {
        inventory.value = response.data
        
        // 转换背包数据为标签页格式
        if (response.data && response.data.bag) {
          inventoryTabs.value = response.data.bag.map((bookMark: any) => ({
            bookMarkId: bookMark.id.toString(),
            name: bookMark.name,
            type: bookMark.type,
            capacity: bookMark.capacity,
            usedSlots: bookMark.usedSlots,
            items: bookMark.itemUids?.map((itemUid: string) => {
              // 从items数组中找到对应的物品数据
              const itemData = response.data.items?.find((item: any) => item.itemId === itemUid)
              return itemData ? {
                itemId: itemData.itemId,
                configId: itemData.configId,
                name: itemData.name || `道具${itemData.configId}`,
                type: getItemType(itemData.configId),
                rarity: getItemRarity(itemData.configId),
                quantity: itemData.quantity,
                bind: itemData.bind,
                obtainTime: itemData.obtainTime,
                slot: itemData.slot,
                cooldown: itemData.cooldown,
                lastUseTime: itemData.lastUseTime
              } : null
            }).filter(Boolean) || []
          }))
        }
      } else {
        throw new Error(response.message || '获取背包数据失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取背包失败',
        message: error.message || '无法获取背包数据'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 使用道具
   */
  const useItem = async (
    itemId: string, 
    quantity: number = 1, 
    targetId?: string
  ): Promise<UseItemResult | null> => {
    try {
      const item = getItemById(itemId)
      if (!item) {
        throw new Error('道具不存在')
      }

      const bookMarkId = getItemBookMarkId(itemId)
      if (!bookMarkId) {
        throw new Error('无法确定道具所在页签')
      }

      const response = await wsService.sendMessage('inventory.useItem', {
        bookMarkId: parseInt(bookMarkId),
        itemId,
        configId: item.configId,
        useType: getItemUseType(item.configId),
        subType: 0,
        quantity,
        heroId: targetId
      })
      
      if (response.code === 0) {
        const result = response.data
        
        // 更新本地道具数量
        if (item.quantity > quantity) {
          item.quantity -= quantity
        } else {
          // 道具用完，从背包中移除
          removeItemFromInventory(itemId)
        }
        
        // 设置冷却时间
        if (result.cooldown) {
          item.cooldown = Date.now() + result.cooldown
          item.lastUseTime = Date.now()
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '使用成功',
          message: result.message || `成功使用 ${item.name}`
        })
        
        return {
          success: true,
          effects: result.effects || [],
          message: result.message,
          cooldown: result.cooldown
        }
      } else {
        throw new Error(response.message || '使用道具失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '使用失败',
        message: error.message
      })
      return null
    }
  }

  /**
   * 出售道具
   */
  const sellItem = async (itemId: string, quantity: number = 1): Promise<boolean> => {
    try {
      const item = getItemById(itemId)
      if (!item) {
        throw new Error('道具不存在')
      }

      const response = await wsService.sendMessage('inventory.sellItem', {
        itemId,
        quantity
      })
      
      if (response.code === 0) {
        const sellPrice = response.data.price || 0
        
        // 更新本地道具数量
        if (item.quantity > quantity) {
          item.quantity -= quantity
        } else {
          // 道具卖完，从背包中移除
          removeItemFromInventory(itemId)
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '出售成功',
          message: `成功出售 ${item.name}，获得 ${sellPrice} 金币`
        })
        
        return true
      } else {
        throw new Error(response.message || '出售道具失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '出售失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 丢弃道具
   */
  const dropItem = async (itemId: string, quantity: number = 1): Promise<boolean> => {
    try {
      const item = getItemById(itemId)
      if (!item) {
        throw new Error('道具不存在')
      }

      const response = await wsService.sendMessage('inventory.dropItem', {
        itemId,
        quantity
      })
      
      if (response.code === 0) {
        // 更新本地道具数量
        if (item.quantity > quantity) {
          item.quantity -= quantity
        } else {
          // 道具丢完，从背包中移除
          removeItemFromInventory(itemId)
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '丢弃成功',
          message: `成功丢弃 ${item.name}`
        })
        
        return true
      } else {
        throw new Error(response.message || '丢弃道具失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '丢弃失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 分割道具
   */
  const splitItem = async (itemId: string, splitQuantity: number): Promise<boolean> => {
    try {
      const item = getItemById(itemId)
      if (!item) {
        throw new Error('道具不存在')
      }

      if (item.quantity <= splitQuantity) {
        throw new Error('分割数量不能大于等于道具总数量')
      }

      const response = await wsService.sendMessage('inventory.splitItem', {
        itemId,
        splitQuantity
      })
      
      if (response.code === 0) {
        const newItem = response.data.newItem
        
        // 更新原道具数量
        item.quantity -= splitQuantity
        
        // 添加新道具到背包
        addItemToInventory(newItem)
        
        globalStore.addNotification({
          type: 'success',
          title: '分割成功',
          message: `成功分割 ${item.name}`
        })
        
        return true
      } else {
        throw new Error(response.message || '分割道具失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '分割失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 扩展背包
   */
  const expandInventory = async (): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('inventory.expandBag', {})
      
      if (response.code === 0) {
        // 更新背包容量
        const expandedTab = response.data.expandedTab
        const tab = inventoryTabs.value.find(t => t.bookMarkId === expandedTab.id.toString())
        if (tab) {
          tab.capacity = expandedTab.capacity
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '扩展成功',
          message: '背包容量已增加'
        })
        
        return true
      } else {
        throw new Error(response.message || '扩展背包失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '扩展失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 整理背包
   */
  const sortItems = async (): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('inventory.sortBag', {})
      
      if (response.code === 0) {
        // 重新获取背包数据
        await fetchInventory()
        
        globalStore.addNotification({
          type: 'success',
          title: '整理完成',
          message: '背包已按类型和稀有度排序'
        })
        
        return true
      } else {
        throw new Error(response.message || '整理背包失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '整理失败',
        message: error.message
      })
      return false
    }
  }

  // 辅助方法

  /**
   * 根据ID获取道具
   */
  const getItemById = (itemId: string): InventoryItem | null => {
    for (const tab of inventoryTabs.value) {
      if (tab.items) {
        const item = tab.items.find(item => item && item.itemId === itemId)
        if (item) return item
      }
    }
    return null
  }

  /**
   * 获取道具所在页签ID
   */
  const getItemBookMarkId = (itemId: string): string | null => {
    for (const tab of inventoryTabs.value) {
      if (tab.items && tab.items.some(item => item && item.itemId === itemId)) {
        return tab.bookMarkId
      }
    }
    return null
  }

  /**
   * 从背包中移除道具
   */
  const removeItemFromInventory = (itemId: string) => {
    inventoryTabs.value.forEach(tab => {
      if (tab.items) {
        const index = tab.items.findIndex(item => item && item.itemId === itemId)
        if (index !== -1) {
          tab.items.splice(index, 1)
          tab.usedSlots = Math.max(0, (tab.usedSlots || 0) - 1)
        }
      }
    })
  }

  /**
   * 添加道具到背包
   */
  const addItemToInventory = (item: InventoryItem) => {
    // 找到合适的页签添加道具
    const targetTab = inventoryTabs.value.find(tab => 
      tab.type === getItemTabType(item.configId) && 
      (tab.usedSlots || 0) < (tab.capacity || 0)
    )
    
    if (targetTab && targetTab.items) {
      targetTab.items.push(item)
      targetTab.usedSlots = (targetTab.usedSlots || 0) + 1
    }
  }

  /**
   * 清空背包数据
   */
  const clearInventory = () => {
    inventory.value = null
    inventoryTabs.value = []
  }

  // 工具函数
  const getItemType = (configId: number): string => {
    // 根据配置ID确定道具类型
    if (configId >= 1000 && configId < 2000) return 'equipment'
    if (configId >= 2000 && configId < 3000) return 'consumable'
    if (configId >= 3000 && configId < 4000) return 'material'
    return 'misc'
  }

  const getItemRarity = (configId: number): string => {
    // 根据配置ID确定道具稀有度
    const rarities = ['common', 'uncommon', 'rare', 'epic', 'legendary']
    return rarities[configId % 5] || 'common'
  }

  const getItemUseType = (configId: number): number => {
    // 根据配置ID确定使用类型
    return Math.floor(configId / 1000) + 1
  }

  const getItemTabType = (configId: number): number => {
    // 根据配置ID确定所属页签类型
    return Math.floor(configId / 1000) + 1
  }

  return {
    // 状态
    inventory: readonly(inventory),
    inventoryTabs: readonly(inventoryTabs),
    isLoading: readonly(isLoading),
    
    // 计算属性
    usedSlots,
    totalCapacity,
    usageRate,
    canExpand,
    itemsByType,
    itemsByRarity,
    
    // 方法
    fetchInventory,
    useItem,
    sellItem,
    dropItem,
    splitItem,
    expandInventory,
    sortItems,
    getItemById,
    getItemBookMarkId,
    clearInventory
  }
})
