import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { AppConfig, GameConfig, Notification } from '@/types'

/**
 * 全局状态管理
 * 管理应用级别的状态，如加载状态、配置信息、通知等
 */
export const useGlobalStore = defineStore('global', () => {
  // 状态定义
  const isLoading = ref(false)
  const appConfig = ref<AppConfig>({
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
    wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:3000',
    version: '1.0.0',
    environment: (import.meta.env.MODE as any) || 'development'
  })
  
  const gameConfig = ref<GameConfig>({
    maxHeroesPerTeam: 25,
    maxFormations: 10,
    matchDuration: 90,
    currencies: [
      { id: 'coins', name: '金币', symbol: 'C', icon: '💰' },
      { id: 'gems', name: '钻石', symbol: 'G', icon: '💎' },
      { id: 'exp', name: '经验', symbol: 'EXP', icon: '⭐' }
    ]
  })
  
  const notifications = ref<Notification[]>([])
  const isInitialized = ref(false)

  // 计算属性
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read)
  )
  
  const isDevelopment = computed(() => 
    appConfig.value.environment === 'development'
  )

  // 动作方法
  
  /**
   * 初始化应用
   */
  const initializeApp = async () => {
    if (isInitialized.value) return
    
    setLoading(true)
    try {
      // 加载应用配置
      await loadAppConfig()
      
      // 加载游戏配置
      await loadGameConfig()
      
      // 初始化其他服务
      await initializeServices()
      
      isInitialized.value = true
      console.log('应用初始化完成')
    } catch (error) {
      console.error('应用初始化失败:', error)
      addNotification({
        type: 'error',
        title: '初始化失败',
        message: '应用初始化过程中发生错误，请刷新页面重试'
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 加载应用配置
   */
  const loadAppConfig = async () => {
    // 从环境变量或API加载配置
    // 这里可以根据需要从服务器获取动态配置
    console.log('加载应用配置:', appConfig.value)
  }

  /**
   * 加载游戏配置
   */
  const loadGameConfig = async () => {
    // 从服务器加载游戏配置
    // 这里可以调用API获取最新的游戏配置
    console.log('加载游戏配置:', gameConfig.value)
  }

  /**
   * 初始化服务
   */
  const initializeServices = async () => {
    // 初始化WebSocket连接、缓存等服务
    console.log('初始化服务完成')
  }

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  /**
   * 添加通知
   */
  const addNotification = async (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    }
    
    notifications.value.unshift(newNotification)
    
    // 限制通知数量
    if (notifications.value.length > 50) {
      notifications.value = notifications.value.slice(0, 50)
    }
    
    // 使用Ant Design的通知组件显示
    if (typeof window !== 'undefined') {
      const { notification: antNotification } = await import('ant-design-vue')
      antNotification[notification.type]({
        message: notification.title,
        description: notification.message,
        duration: notification.type === 'error' ? 0 : 4.5
      })
    }
  }

  /**
   * 标记通知为已读
   */
  const markNotificationAsRead = (id: string) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  /**
   * 清除所有通知
   */
  const clearNotifications = () => {
    notifications.value = []
  }

  /**
   * 更新应用配置
   */
  const updateAppConfig = (config: Partial<AppConfig>) => {
    appConfig.value = { ...appConfig.value, ...config }
  }

  /**
   * 更新游戏配置
   */
  const updateGameConfig = (config: Partial<GameConfig>) => {
    gameConfig.value = { ...gameConfig.value, ...config }
  }

  return {
    // 状态
    isLoading: readonly(isLoading),
    appConfig: readonly(appConfig),
    gameConfig: readonly(gameConfig),
    notifications: readonly(notifications),
    isInitialized: readonly(isInitialized),
    
    // 计算属性
    unreadNotifications,
    isDevelopment,
    
    // 方法
    initializeApp,
    setLoading,
    addNotification,
    markNotificationAsRead,
    clearNotifications,
    updateAppConfig,
    updateGameConfig
  }
})
