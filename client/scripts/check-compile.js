#!/usr/bin/env node

/**
 * 编译检查脚本
 * 检查TypeScript编译是否通过
 */

const { spawn } = require('child_process')
const chalk = require('chalk')
const path = require('path')

console.log(chalk.blue('🔍 开始检查TypeScript编译...\n'))

// 运行TypeScript编译检查
const tscProcess = spawn('npx', ['vue-tsc', '--noEmit'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
})

tscProcess.on('close', (code) => {
  if (code === 0) {
    console.log(chalk.green('\n✅ TypeScript编译检查通过！'))
  } else {
    console.log(chalk.red('\n❌ TypeScript编译检查失败！'))
    console.log(chalk.yellow('请修复上述错误后重试。'))
  }
  process.exit(code)
})

tscProcess.on('error', (error) => {
  console.error(chalk.red('❌ 无法运行TypeScript编译检查:'), error.message)
  process.exit(1)
})
