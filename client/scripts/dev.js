#!/usr/bin/env node

/**
 * 开发环境启动脚本
 * 提供更好的开发体验
 */

const { spawn } = require('child_process')
const chalk = require('chalk')
const path = require('path')

console.log(chalk.green('🚀 启动足球游戏客户端开发服务器...\n'))

// 启动Vite开发服务器
const viteProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
})

// 处理进程退出
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n📦 正在关闭开发服务器...'))
  viteProcess.kill('SIGINT')
  process.exit(0)
})

process.on('SIGTERM', () => {
  viteProcess.kill('SIGTERM')
  process.exit(0)
})

viteProcess.on('close', (code) => {
  if (code !== 0) {
    console.log(chalk.red(`❌ 开发服务器异常退出，退出码: ${code}`))
  } else {
    console.log(chalk.green('✅ 开发服务器已关闭'))
  }
  process.exit(code)
})
