#!/usr/bin/env node

/**
 * 前端类型生成工具
 * 基于服务端Schema自动生成前端TypeScript类型定义
 * 解决前后端数据结构不一致的问题
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TypeGenerator {
  constructor() {
    this.serverRoot = path.resolve(__dirname, '../../');
    this.clientTypesDir = path.resolve(__dirname, '../src/types');
    this.generatedTypes = new Map();
  }

  /**
   * 扫描服务端Schema文件
   */
  async scanServerSchemas() {
    console.log('🔍 扫描服务端Schema文件...');
    
    const schemaFiles = glob.sync('apps/*/src/common/schemas/*.schema.ts', {
      cwd: this.serverRoot
    });

    const schemas = [];
    for (const file of schemaFiles) {
      const fullPath = path.join(this.serverRoot, file);
      const content = fs.readFileSync(fullPath, 'utf-8');
      const schemaInfo = this.parseSchemaFile(content, file);
      if (schemaInfo) {
        schemas.push(schemaInfo);
      }
    }

    console.log(`✅ 发现 ${schemas.length} 个Schema文件`);
    return schemas;
  }

  /**
   * 解析Schema文件
   */
  parseSchemaFile(content, filePath) {
    const fileName = path.basename(filePath, '.schema.ts');
    
    // 提取主要的Schema类
    const classMatch = content.match(/export class (\w+) \{([\s\S]*?)\n\}/);
    if (!classMatch) return null;

    const className = classMatch[1];
    const classBody = classMatch[2];

    // 提取字段定义
    const fields = this.extractFields(classBody);
    
    // 提取枚举定义
    const enums = this.extractEnums(content);

    return {
      fileName,
      className,
      fields,
      enums,
      filePath
    };
  }

  /**
   * 提取字段定义
   */
  extractFields(classBody) {
    const fields = [];
    const fieldRegex = /@Prop\((.*?)\)\s*(\w+):\s*([^;]+);/g;
    
    let match;
    while ((match = fieldRegex.exec(classBody)) !== null) {
      const propOptions = match[1];
      const fieldName = match[2];
      const fieldType = match[3].trim();

      // 解析Prop装饰器选项
      const isRequired = propOptions.includes('required: true');
      const hasDefault = propOptions.includes('default:');
      const isArray = fieldType.includes('[]') || propOptions.includes('type: [');

      fields.push({
        name: fieldName,
        type: this.convertToTSType(fieldType),
        required: isRequired && !hasDefault,
        isArray,
        originalType: fieldType
      });
    }

    return fields;
  }

  /**
   * 提取枚举定义
   */
  extractEnums(content) {
    const enums = [];
    const enumRegex = /export enum (\w+) \{([\s\S]*?)\}/g;
    
    let match;
    while ((match = enumRegex.exec(content)) !== null) {
      const enumName = match[1];
      const enumBody = match[2];
      
      const values = [];
      const valueRegex = /(\w+)\s*=\s*([^,\n]+)/g;
      let valueMatch;
      while ((valueMatch = valueRegex.exec(enumBody)) !== null) {
        values.push({
          key: valueMatch[1],
          value: valueMatch[2].trim()
        });
      }

      enums.push({
        name: enumName,
        values
      });
    }

    return enums;
  }

  /**
   * 转换为TypeScript类型
   */
  convertToTSType(mongooseType) {
    const typeMap = {
      'string': 'string',
      'String': 'string',
      'number': 'number',
      'Number': 'number',
      'boolean': 'boolean',
      'Boolean': 'boolean',
      'Date': 'Date',
      'any': 'any',
      'Object': 'Record<string, any>'
    };

    // 处理数组类型
    if (mongooseType.includes('[]')) {
      const baseType = mongooseType.replace(/\[\]/g, '');
      return `${this.convertToTSType(baseType)}[]`;
    }

    // 处理泛型类型
    if (mongooseType.includes('<')) {
      return mongooseType; // 保持原样，如 Array<string>
    }

    return typeMap[mongooseType] || mongooseType;
  }

  /**
   * 生成TypeScript接口
   */
  generateInterface(schema) {
    const { className, fields, enums } = schema;
    
    let output = '';

    // 生成枚举
    for (const enumDef of enums) {
      output += `export enum ${enumDef.name} {\n`;
      for (const value of enumDef.values) {
        output += `  ${value.key} = ${value.value},\n`;
      }
      output += '}\n\n';
    }

    // 生成接口
    output += `export interface ${className} {\n`;
    
    for (const field of fields) {
      const optional = field.required ? '' : '?';
      const readonly = field.isArray ? 'readonly ' : '';
      output += `  ${readonly}${field.name}${optional}: ${field.type};\n`;
    }
    
    output += '}\n\n';

    // 生成只读版本（用于从服务端接收的数据）
    output += `export interface Readonly${className} {\n`;
    for (const field of fields) {
      const optional = field.required ? '' : '?';
      output += `  readonly ${field.name}${optional}: ${field.type};\n`;
    }
    output += '}\n';

    return output;
  }

  /**
   * 生成所有类型文件
   */
  async generateAll() {
    console.log('🚀 开始生成前端类型定义...');
    console.log('服务端根目录:', this.serverRoot);
    console.log('客户端类型目录:', this.clientTypesDir);

    // 扫描服务端Schema
    const schemas = await this.scanServerSchemas();

    // 确保输出目录存在
    const generatedDir = path.join(this.clientTypesDir, 'generated');
    if (!fs.existsSync(generatedDir)) {
      fs.mkdirSync(generatedDir, { recursive: true });
    }

    // 生成各个模块的类型文件
    const moduleMap = {
      'character': ['character'],
      'hero': ['hero'],
      'formation': ['formation'],
      'inventory': ['inventory'],
      'match': ['match']
    };

    for (const [module, schemaNames] of Object.entries(moduleMap)) {
      const moduleSchemas = schemas.filter(s => 
        schemaNames.some(name => s.fileName.includes(name))
      );

      if (moduleSchemas.length > 0) {
        await this.generateModuleTypes(module, moduleSchemas);
      }
    }

    // 生成统一导出文件
    await this.generateIndexFile();

    console.log('✅ 类型生成完成！');
  }

  /**
   * 生成模块类型文件
   */
  async generateModuleTypes(moduleName, schemas) {
    let output = `// 自动生成的${moduleName}模块类型定义\n`;
    output += `// 基于服务端Schema生成，请勿手动修改\n\n`;

    for (const schema of schemas) {
      output += this.generateInterface(schema);
      output += '\n';
    }

    const filePath = path.join(this.clientTypesDir, 'generated', `${moduleName}.ts`);
    fs.writeFileSync(filePath, output);
    console.log(`✅ 生成 ${moduleName}.ts`);
  }

  /**
   * 生成统一导出文件
   */
  async generateIndexFile() {
    const generatedDir = path.join(this.clientTypesDir, 'generated');
    const files = fs.readdirSync(generatedDir).filter(f => f.endsWith('.ts') && f !== 'index.ts');

    let output = '// 自动生成的类型定义统一导出\n\n';
    
    for (const file of files) {
      const moduleName = path.basename(file, '.ts');
      output += `export * from './${moduleName}';\n`;
    }

    const indexPath = path.join(generatedDir, 'index.ts');
    fs.writeFileSync(indexPath, output);
    console.log('✅ 生成 index.ts');
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const generator = new TypeGenerator();
  generator.generateAll().catch(console.error);
}

export default TypeGenerator;
