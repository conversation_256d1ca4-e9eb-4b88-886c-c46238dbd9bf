# 依赖
node_modules/
.pnp
.pnp.js

# 构建输出
dist/
build/

# 环境变量
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
.cache/

# 测试覆盖率
coverage/
*.lcov

# TypeScript
*.tsbuildinfo

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# 热更新
.vite/
