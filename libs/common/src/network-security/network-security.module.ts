import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { securityConfig, SecurityConfigValidator } from './config/network-security.config';
import { IPWhitelistMiddleware } from './ip-whitelist.middleware';
import { ServiceAuthMiddleware } from './service-auth.middleware';
import { PortSecurityValidator } from './validators/port-security.validator';
import { PortSecurityMiddleware } from './middleware/port-security.middleware';
import { PortSecurityMonitorService } from './services/port-security-monitor.service';

@Global()
@Module({
  imports: [
    ConfigModule.forFeature(securityConfig),
  ],
  providers: [
    IPWhitelistMiddleware,
    ServiceAuthMiddleware,
    PortSecurityValidator,
    PortSecurityMiddleware,
    PortSecurityMonitorService,
    {
      provide: 'SECURITY_CONFIG_VALIDATOR',
      useFactory: (config) => {
        const errors = SecurityConfigValidator.validate(config);
        if (errors.length > 0) {
          throw new Error(`安全配置验证失败:\n${errors.join('\n')}`);
        }
        return config;
      },
      inject: [securityConfig.KEY],
    },
  ],
  exports: [
    IPWhitelistMiddleware,
    ServiceAuthMiddleware,
    PortSecurityValidator,
    PortSecurityMiddleware,
    PortSecurityMonitorService,
  ],
})
export class NetworkSecurityModule {}
