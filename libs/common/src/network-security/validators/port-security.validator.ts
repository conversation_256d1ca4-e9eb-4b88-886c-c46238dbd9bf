import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PortRange, NetworkSecurityConfig } from '../config/network-security.config';

/**
 * 端口安全验证器
 * 
 * 提供端口安全相关的验证功能：
 * 1. 端口范围验证
 * 2. 动态端口验证
 * 3. 未授权端口检测
 */
@Injectable()
export class PortSecurityValidator {
  private readonly logger = new Logger(PortSecurityValidator.name);
  private readonly config: NetworkSecurityConfig['portSecurity'];

  constructor(private readonly configService: ConfigService) {
    this.config = this.configService.get<NetworkSecurityConfig['portSecurity']>('security.portSecurity');
    
    if (this.config?.enabled) {
      this.logger.log(`端口安全验证器已启用，配置了 ${this.config.allowedPortRanges.length} 个端口范围`);
      this.logPortRanges();
    } else {
      this.logger.debug('端口安全验证器已禁用');
    }
  }

  /**
   * 验证端口是否在允许的范围内
   * @param port 要验证的端口号
   * @param serviceName 服务名称（用于日志）
   * @returns 是否在允许范围内
   */
  validatePortRange(port: number, serviceName?: string): boolean {
    // 如果端口安全未启用，直接通过
    if (!this.config?.enabled) {
      return true;
    }

    // 验证端口号有效性
    if (!this.isValidPortNumber(port)) {
      this.logPortViolation('INVALID_PORT', port, serviceName, '端口号无效');
      return false;
    }

    // 检查是否在允许的端口范围内
    const isInRange = this.isPortInAllowedRange(port);
    
    if (!isInRange) {
      this.logPortViolation('OUT_OF_RANGE', port, serviceName, '端口不在允许范围内');
      
      // 如果配置了阻断未授权端口，返回false
      if (this.config.blockUnauthorizedPorts) {
        return false;
      }
    }

    return true;
  }

  /**
   * 验证动态端口
   * @param port 动态计算的端口号
   * @param serverId 区服ID
   * @param serviceName 服务名称
   * @returns 是否验证通过
   */
  validateDynamicPort(port: number, serverId?: string, serviceName?: string): boolean {
    // 如果动态端口验证未启用，直接通过
    if (!this.config?.enabled || !this.config.validateDynamicPorts) {
      return true;
    }

    const context = `${serviceName || 'unknown'}@${serverId || 'unknown'}`;
    
    // 基础端口范围验证
    if (!this.validatePortRange(port, context)) {
      return false;
    }

    // 动态端口特殊验证
    if (!this.isDynamicPortValid(port, serverId, serviceName)) {
      this.logPortViolation('DYNAMIC_INVALID', port, context, '动态端口验证失败');
      return this.config.blockUnauthorizedPorts ? false : true;
    }

    this.logger.debug(`动态端口验证通过: ${context} -> ${port}`);
    return true;
  }

  /**
   * 检查端口是否在允许的范围内
   * @param port 端口号
   * @returns 是否在允许范围内
   */
  isPortInAllowedRange(port: number): boolean {
    if (!this.config?.allowedPortRanges || this.config.allowedPortRanges.length === 0) {
      return true; // 如果没有配置范围，默认允许
    }

    return this.config.allowedPortRanges.some(range => 
      port >= range.start && port <= range.end
    );
  }

  /**
   * 获取端口所在的范围描述
   * @param port 端口号
   * @returns 范围描述，如果不在任何范围内返回null
   */
  getPortRangeDescription(port: number): string | null {
    if (!this.config?.allowedPortRanges) {
      return null;
    }

    const range = this.config.allowedPortRanges.find(r => 
      port >= r.start && port <= r.end
    );

    return range?.description || null;
  }

  /**
   * 获取所有允许的端口范围
   * @returns 端口范围数组
   */
  getAllowedPortRanges(): PortRange[] {
    return this.config?.allowedPortRanges || [];
  }

  /**
   * 验证端口号的有效性
   * @param port 端口号
   * @returns 是否有效
   */
  private isValidPortNumber(port: number): boolean {
    return Number.isInteger(port) && port >= 1 && port <= 65535;
  }

  /**
   * 验证动态端口的特殊规则
   * @param port 端口号
   * @param serverId 区服ID
   * @param serviceName 服务名称
   * @returns 是否有效
   */
  private isDynamicPortValid(port: number, serverId?: string, serviceName?: string): boolean {
    // 检查端口是否符合动态端口的命名规则
    // 动态端口通常遵循特定的计算规则，这里可以添加额外的验证逻辑
    
    // 例如：检查端口是否符合预期的计算结果
    if (serviceName && serverId) {
      try {
        // 这里可以集成PortManager来验证端口计算是否正确
        // const expectedPort = PortManager.calculatePort(serviceName, serverId, 0);
        // return port === expectedPort || Math.abs(port - expectedPort) <= 10; // 允许一定范围的偏差
      } catch (error) {
        this.logger.warn(`动态端口计算验证失败: ${error.message}`);
      }
    }

    return true; // 默认通过，可以根据需要添加更严格的验证
  }

  /**
   * 记录端口违规日志
   * @param type 违规类型
   * @param port 端口号
   * @param context 上下文信息
   * @param reason 违规原因
   */
  private logPortViolation(type: string, port: number, context?: string, reason?: string): void {
    if (!this.config?.logPortViolations) {
      return;
    }

    const message = `端口安全违规 [${type}]: 端口 ${port}${context ? ` (${context})` : ''} - ${reason || '未知原因'}`;
    this.logger.warn(message);

    // 这里可以添加更多的监控和告警逻辑
    // 例如：发送到监控系统、记录到安全日志等
  }

  /**
   * 记录配置的端口范围
   */
  private logPortRanges(): void {
    if (!this.config?.allowedPortRanges || this.config.allowedPortRanges.length === 0) {
      this.logger.warn('未配置允许的端口范围，所有端口都将被允许');
      return;
    }

    this.logger.log('允许的端口范围:');
    this.config.allowedPortRanges.forEach((range, index) => {
      this.logger.log(`  ${index + 1}. ${range.start}-${range.end}${range.description ? ` (${range.description})` : ''}`);
    });
  }
}
