# Redis分布式锁使用场景详解

## 概述

本文档详细介绍了Redis分布式锁在足球经理游戏中的各种使用场景，包括具体的实现方式、最佳实践和注意事项。

## 游戏业务场景

### 1. 球员转会系统

#### 场景描述
在足球经理游戏中，球员转会是一个复杂的业务流程，涉及多个俱乐部同时竞价、球员状态更新、财务结算等操作。需要确保同一球员不会被多个俱乐部同时转会。

#### 实现方案

```typescript
@Injectable()
export class TransferService {
  constructor(private readonly lockService: RedisLockService) {}

  async initiateTransfer(playerId: string, fromClubId: string, toClubId: string, fee: number) {
    // 获取球员转会锁
    const lockId = await this.lockService.acquireTransferLock(playerId, {
      ttl: 300, // 5分钟转会窗口
      maxRetries: 3,
      retryDelay: 1000
    });

    if (!lockId) {
      throw new ConflictException('该球员正在进行其他转会谈判');
    }

    try {
      // 验证转会条件
      await this.validateTransferConditions(playerId, fromClubId, toClubId, fee);
      
      // 执行转会流程
      const transfer = await this.executeTransfer({
        playerId,
        fromClubId,
        toClubId,
        fee,
        timestamp: new Date()
      });

      // 更新球员状态
      await this.updatePlayerStatus(playerId, 'TRANSFERRED');
      
      // 财务结算
      await this.processFinancialSettlement(fromClubId, toClubId, fee);

      return transfer;
    } finally {
      // 确保释放锁
      await this.lockService.releaseLock(`transfer:player:${playerId}`, lockId);
    }
  }

  async bidForPlayer(playerId: string, clubId: string, bidAmount: number) {
    // 使用可重入锁支持同一俱乐部多次出价
    const lockId = await this.lockService.acquireReentrantLock(`bid:player:${playerId}`, {
      identifier: clubId,
      ttl: 60
    });

    if (!lockId) {
      throw new ConflictException('当前无法对该球员出价');
    }

    try {
      const currentBid = await this.getCurrentHighestBid(playerId);
      
      if (bidAmount <= currentBid.amount) {
        throw new BadRequestException('出价必须高于当前最高价');
      }

      return await this.placeBid(playerId, clubId, bidAmount);
    } finally {
      await this.lockService.releaseReentrantLock(`bid:player:${playerId}`, lockId);
    }
  }
}
```

#### 锁策略
- **锁粒度**: 单个球员级别 (`transfer:player:{playerId}`)
- **锁类型**: 基础锁（转会）+ 可重入锁（出价）
- **超时时间**: 转会5分钟，出价1分钟
- **重试策略**: 最多3次重试，间隔1秒

### 2. 俱乐部财务管理

#### 场景描述
俱乐部的财务操作包括球员工资发放、转会费支付、赞助收入等，需要确保财务数据的一致性，防止并发操作导致的资金计算错误。

#### 实现方案

```typescript
@Injectable()
export class ClubFinanceService {
  async processPayroll(clubId: string, payrollData: PayrollData[]) {
    const lockId = await this.lockService.acquireClubLock(clubId, 'payroll', {
      ttl: 120, // 2分钟
      maxRetries: 5,
      retryDelay: 500
    });

    if (!lockId) {
      throw new ServiceUnavailableException('俱乐部财务系统繁忙，请稍后重试');
    }

    try {
      // 获取当前俱乐部财务状况
      const currentBalance = await this.getClubBalance(clubId);
      const totalPayroll = payrollData.reduce((sum, item) => sum + item.amount, 0);

      if (currentBalance < totalPayroll) {
        throw new BadRequestException('俱乐部资金不足以支付工资');
      }

      // 批量处理工资发放
      const transactions = await this.batchProcessPayroll(clubId, payrollData);
      
      // 更新俱乐部余额
      await this.updateClubBalance(clubId, currentBalance - totalPayroll);

      return transactions;
    } finally {
      await this.lockService.releaseLock(`club:${clubId}:payroll`, lockId);
    }
  }

  async processTransferPayment(transferId: string, fromClubId: string, toClubId: string, amount: number) {
    // 需要同时锁定两个俱乐部的财务
    const locks = await this.acquireMultipleClubLocks([fromClubId, toClubId], 'transfer');
    
    if (locks.some(lock => !lock.acquired)) {
      throw new ConflictException('无法同时锁定相关俱乐部财务');
    }

    try {
      await this.executeTransferPayment(transferId, fromClubId, toClubId, amount);
    } finally {
      // 释放所有锁
      await this.releaseMultipleClubLocks(locks);
    }
  }

  private async acquireMultipleClubLocks(clubIds: string[], operation: string) {
    // 按ID排序避免死锁
    const sortedClubIds = clubIds.sort();
    const locks = [];

    for (const clubId of sortedClubIds) {
      const lockId = await this.lockService.acquireClubLock(clubId, operation, {
        ttl: 60,
        maxRetries: 1
      });
      
      locks.push({ clubId, lockId, acquired: !!lockId });
      
      if (!lockId) {
        // 如果获取失败，释放已获取的锁
        await this.releasePartialLocks(locks.slice(0, -1));
        break;
      }
    }

    return locks;
  }
}
```

### 3. 比赛模拟系统

#### 场景描述
比赛模拟是CPU密集型操作，需要确保同一场比赛不会被重复模拟，同时支持比赛暂停、恢复等操作。

#### 实现方案

```typescript
@Injectable()
export class MatchSimulationService {
  async simulateMatch(matchId: string, simulationOptions: SimulationOptions) {
    const lockId = await this.lockService.acquireMatchLock(matchId, {
      ttl: 1800, // 30分钟模拟时间
      maxRetries: 1 // 不重试，避免重复模拟
    });

    if (!lockId) {
      throw new ConflictException('比赛正在进行中或已完成');
    }

    try {
      // 检查比赛状态
      const match = await this.getMatch(matchId);
      if (match.status !== 'SCHEDULED') {
        throw new BadRequestException('比赛状态不允许模拟');
      }

      // 更新比赛状态为进行中
      await this.updateMatchStatus(matchId, 'IN_PROGRESS');

      // 执行比赛模拟
      const result = await this.runSimulation(matchId, simulationOptions);

      // 更新比赛结果
      await this.updateMatchResult(matchId, result);
      await this.updateMatchStatus(matchId, 'COMPLETED');

      // 处理比赛后续事务
      await this.processPostMatchEvents(matchId, result);

      return result;
    } catch (error) {
      // 模拟失败时恢复比赛状态
      await this.updateMatchStatus(matchId, 'SCHEDULED');
      throw error;
    } finally {
      await this.lockService.releaseLock(`match:simulation:${matchId}`, lockId);
    }
  }

  async pauseMatch(matchId: string, userId: string) {
    // 使用可重入锁支持暂停/恢复操作
    const lockId = await this.lockService.acquireReentrantLock(`match:control:${matchId}`, {
      identifier: userId,
      ttl: 300
    });

    if (!lockId) {
      throw new ConflictException('无法控制比赛状态');
    }

    try {
      await this.pauseSimulation(matchId);
      return { status: 'PAUSED', pausedBy: userId, timestamp: new Date() };
    } finally {
      await this.lockService.releaseReentrantLock(`match:control:${matchId}`, lockId);
    }
  }
}
```

### 4. 排行榜更新系统

#### 场景描述
排行榜需要实时更新，但频繁的更新操作可能导致性能问题。使用分布式锁来批量处理排行榜更新，确保数据一致性。

#### 实现方案

```typescript
@Injectable()
export class LeaderboardService {
  async updatePlayerRanking(playerId: string, newStats: PlayerStats) {
    const lockId = await this.lockService.acquireLeaderboardLock('player_ranking', {
      ttl: 120, // 2分钟批量更新窗口
      maxRetries: 10,
      retryDelay: 100
    });

    if (!lockId) {
      // 如果无法获取锁，将更新请求加入队列
      await this.queueRankingUpdate(playerId, newStats);
      return;
    }

    try {
      // 批量处理排行榜更新
      const pendingUpdates = await this.getPendingRankingUpdates();
      pendingUpdates.push({ playerId, newStats });

      // 重新计算排行榜
      const updatedRankings = await this.recalculateRankings(pendingUpdates);
      
      // 批量更新数据库
      await this.batchUpdateRankings(updatedRankings);
      
      // 清空待处理队列
      await this.clearPendingUpdates();

      // 广播排行榜更新事件
      await this.broadcastRankingUpdate(updatedRankings);

    } finally {
      await this.lockService.releaseLock('leaderboard:update:player_ranking', lockId);
    }
  }

  async updateClubRanking(clubId: string, seasonStats: SeasonStats) {
    // 使用withLock简化锁管理
    return await this.lockService.withLock(
      'leaderboard:update:club_ranking',
      async () => {
        const currentRankings = await this.getClubRankings();
        const updatedRankings = await this.calculateNewClubRankings(
          currentRankings,
          clubId,
          seasonStats
        );
        
        await this.saveClubRankings(updatedRankings);
        return updatedRankings;
      },
      {
        ttl: 60,
        maxRetries: 5,
        retryDelay: 200
      }
    );
  }
}
```

## 系统级场景

### 1. 用户会话管理

#### 场景描述
确保用户在多个设备上的登录状态一致性，防止并发登录冲突。

#### 实现方案

```typescript
@Injectable()
export class SessionService {
  async createUserSession(userId: string, deviceInfo: DeviceInfo) {
    const lockId = await this.lockService.acquireUserLock(userId, 'session', {
      ttl: 30,
      maxRetries: 3,
      retryDelay: 200
    });

    if (!lockId) {
      throw new ConflictException('用户会话正在处理中');
    }

    try {
      // 检查现有会话
      const existingSessions = await this.getUserSessions(userId);
      
      // 根据策略处理多设备登录
      if (existingSessions.length >= this.maxSessionsPerUser) {
        await this.invalidateOldestSession(userId);
      }

      // 创建新会话
      const session = await this.createSession(userId, deviceInfo);
      
      return session;
    } finally {
      await this.lockService.releaseLock(`user:${userId}:session`, lockId);
    }
  }

  async logoutUser(userId: string, sessionId: string) {
    const lockId = await this.lockService.acquireUserLock(userId, 'logout');
    
    if (lockId) {
      try {
        await this.invalidateSession(sessionId);
        await this.cleanupUserData(userId);
      } finally {
        await this.lockService.releaseLock(`user:${userId}:logout`, lockId);
      }
    }
  }
}
```

### 2. 缓存更新协调

#### 场景描述
在分布式环境中，当数据更新时需要协调多个服务实例的缓存失效，确保数据一致性。

#### 实现方案

```typescript
@Injectable()
export class CacheCoordinationService {
  async invalidatePlayerCache(playerId: string) {
    const lockId = await this.lockService.acquireLock(`cache:player:${playerId}`, {
      ttl: 10,
      maxRetries: 1
    });

    if (!lockId) {
      // 如果无法获取锁，说明其他实例正在处理
      return;
    }

    try {
      // 标记缓存失效
      await this.markCacheInvalid(`player:${playerId}`);
      
      // 通知其他服务实例
      await this.broadcastCacheInvalidation('player', playerId);
      
      // 预热关键缓存
      await this.preloadPlayerCache(playerId);
      
    } finally {
      await this.lockService.releaseLock(`cache:player:${playerId}`, lockId);
    }
  }

  async coordinatedCacheUpdate(cacheKey: string, updateFunction: () => Promise<any>) {
    return await this.lockService.withLock(
      `cache:update:${cacheKey}`,
      async () => {
        // 检查缓存是否仍需更新
        const currentVersion = await this.getCacheVersion(cacheKey);
        const latestVersion = await this.getLatestDataVersion(cacheKey);
        
        if (currentVersion >= latestVersion) {
          return await this.getFromCache(cacheKey);
        }

        // 执行更新
        const newData = await updateFunction();
        
        // 更新缓存
        await this.updateCache(cacheKey, newData, latestVersion);
        
        return newData;
      },
      { ttl: 30 }
    );
  }
}
```

### 3. 定时任务协调

#### 场景描述
在多实例部署中，确保定时任务只在一个实例上执行，避免重复处理。

#### 实现方案

```typescript
@Injectable()
export class ScheduledTaskService {
  @Cron('0 0 * * *') // 每天午夜执行
  async dailyMaintenanceTask() {
    const lockId = await this.lockService.acquireLock('task:daily_maintenance', {
      ttl: 3600, // 1小时执行时间
      maxRetries: 0 // 不重试，避免重复执行
    });

    if (!lockId) {
      this.logger.log('每日维护任务已在其他实例执行');
      return;
    }

    try {
      this.logger.log('开始执行每日维护任务');
      
      // 清理过期数据
      await this.cleanupExpiredData();
      
      // 更新统计数据
      await this.updateDailyStatistics();
      
      // 生成报告
      await this.generateDailyReports();
      
      this.logger.log('每日维护任务完成');
      
    } catch (error) {
      this.logger.error(`每日维护任务失败: ${error.message}`);
      throw error;
    } finally {
      await this.lockService.releaseLock('task:daily_maintenance', lockId);
    }
  }

  @Cron('*/5 * * * *') // 每5分钟执行
  async healthCheckTask() {
    const lockId = await this.lockService.acquireLock('task:health_check', {
      ttl: 60,
      maxRetries: 0
    });

    if (lockId) {
      try {
        await this.performHealthChecks();
      } finally {
        await this.lockService.releaseLock('task:health_check', lockId);
      }
    }
  }
}
```

## 高级使用模式

### 1. 分段锁模式

```typescript
// 用于高并发场景的分段锁
class SegmentedLockManager {
  private readonly segmentCount = 16;

  private getSegmentKey(resourceId: string): string {
    const hash = this.hashCode(resourceId);
    const segment = Math.abs(hash) % this.segmentCount;
    return `segment:${segment}`;
  }

  async acquireSegmentedLock(resourceId: string, operation: string) {
    const segmentKey = this.getSegmentKey(resourceId);
    const lockKey = `${segmentKey}:${operation}`;
    
    return await this.lockService.acquireLock(lockKey, {
      ttl: 30,
      maxRetries: 3,
      retryDelay: 100
    });
  }
}
```

### 2. 读写锁模式

```typescript
// 模拟读写锁行为
class ReadWriteLockManager {
  async acquireReadLock(resourceId: string) {
    // 读锁：多个读者可以同时持有
    const readCount = await this.incrementReadCount(resourceId);
    
    if (readCount === 1) {
      // 第一个读者需要获取写锁以阻止写者
      const lockId = await this.lockService.acquireLock(`write:${resourceId}`, {
        ttl: 300
      });
      
      if (!lockId) {
        await this.decrementReadCount(resourceId);
        throw new Error('无法获取读锁');
      }
    }
    
    return `read:${resourceId}:${Date.now()}`;
  }

  async acquireWriteLock(resourceId: string) {
    // 写锁：独占访问
    return await this.lockService.acquireLock(`write:${resourceId}`, {
      ttl: 300,
      maxRetries: 5,
      retryDelay: 200
    });
  }
}
```

### 3. 优先级锁模式

```typescript
// 支持优先级的锁管理
class PriorityLockManager {
  async acquirePriorityLock(
    resourceId: string, 
    priority: number, 
    clientId: string
  ) {
    // 将请求加入优先级队列
    await this.addToQueue(resourceId, priority, clientId);
    
    // 等待轮到当前客户端
    while (true) {
      const nextClient = await this.getNextInQueue(resourceId);
      
      if (nextClient === clientId) {
        const lockId = await this.lockService.acquireLock(`priority:${resourceId}`, {
          ttl: 60
        });
        
        if (lockId) {
          await this.removeFromQueue(resourceId, clientId);
          return lockId;
        }
      }
      
      // 等待一段时间后重试
      await this.sleep(100);
    }
  }
}
```

## 性能优化场景

### 1. 锁池化

```typescript
// 锁对象池，减少锁创建开销
class LockPool {
  private readonly pool = new Map<string, LockInstance[]>();
  private readonly maxPoolSize = 100;

  async acquireFromPool(lockKey: string): Promise<LockInstance | null> {
    const poolKey = this.getPoolKey(lockKey);
    const pool = this.pool.get(poolKey) || [];
    
    if (pool.length > 0) {
      return pool.pop();
    }
    
    return await this.createNewLock(lockKey);
  }

  async returnToPool(lockInstance: LockInstance) {
    const poolKey = this.getPoolKey(lockInstance.key);
    const pool = this.pool.get(poolKey) || [];
    
    if (pool.length < this.maxPoolSize) {
      pool.push(lockInstance);
      this.pool.set(poolKey, pool);
    }
  }
}
```

### 2. 批量锁操作

```typescript
// 批量锁操作优化
class BatchLockOperator {
  async batchAcquire(requests: LockRequest[]): Promise<LockResult[]> {
    // 使用Redis Pipeline批量执行
    const pipeline = this.redis.pipeline();
    
    requests.forEach(req => {
      pipeline.eval(
        this.lockService.acquireLockScript,
        1,
        req.key,
        req.identifier,
        req.ttl
      );
    });
    
    const results = await pipeline.exec();
    return this.processResults(results, requests);
  }

  async batchRelease(releases: LockRelease[]): Promise<boolean[]> {
    const pipeline = this.redis.pipeline();
    
    releases.forEach(rel => {
      pipeline.eval(
        this.lockService.releaseLockScript,
        1,
        rel.key,
        rel.identifier
      );
    });
    
    const results = await pipeline.exec();
    return results.map(result => result[1] === 1);
  }
}
```

## 监控和调试场景

### 1. 锁使用分析

```typescript
@Injectable()
export class LockAnalyticsService {
  async analyzeLockUsage(timeRange: TimeRange) {
    const stats = await this.lockService.getStats();
    const activeLocks = await this.lockService.getActiveLocks();
    
    return {
      summary: {
        totalOperations: stats.totalAcquired + stats.totalFailed,
        successRate: stats.totalAcquired / (stats.totalAcquired + stats.totalFailed),
        averageHoldTime: stats.averageHoldTime,
        currentActiveLocks: activeLocks.length
      },
      
      topResourcesByContention: await this.getTopContentionResources(timeRange),
      lockHoldTimeDistribution: await this.getLockHoldTimeDistribution(timeRange),
      failureAnalysis: await this.analyzeLockFailures(timeRange)
    };
  }

  async detectAnomalies() {
    const currentStats = await this.lockService.getStats();
    const historicalAverage = await this.getHistoricalAverage();
    
    const anomalies = [];
    
    // 检测异常高的失败率
    if (currentStats.totalFailed / currentStats.totalAcquired > historicalAverage.failureRate * 2) {
      anomalies.push({
        type: 'HIGH_FAILURE_RATE',
        current: currentStats.totalFailed / currentStats.totalAcquired,
        expected: historicalAverage.failureRate
      });
    }
    
    // 检测异常长的持有时间
    if (currentStats.averageHoldTime > historicalAverage.holdTime * 3) {
      anomalies.push({
        type: 'LONG_HOLD_TIME',
        current: currentStats.averageHoldTime,
        expected: historicalAverage.holdTime
      });
    }
    
    return anomalies;
  }
}
```

这些使用场景展示了Redis分布式锁在足球经理游戏中的广泛应用，从基础的数据一致性保护到复杂的业务流程协调，都能提供可靠的解决方案。
