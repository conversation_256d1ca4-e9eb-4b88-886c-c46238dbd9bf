#!/usr/bin/env node

/**
 * 批量修改MessagePattern方法的payload类型
 * 
 * 功能：
 * 1. 扫描所有controller文件
 * 2. 找到@MessagePattern装饰的方法
 * 3. 在payload类型中添加 injectedContext?: InjectedContext
 * 4. 添加必要的import语句
 * 
 * 使用方法：
 * node scripts/migrate-payload-types.js [--dry-run] [--target=path]
 */

const fs = require('fs');
const path = require('path');

class PayloadTypeMigrator {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.targetPath = options.targetPath || 'apps';
    this.stats = {
      filesScanned: 0,
      filesModified: 0,
      methodsModified: 0,
      errors: []
    };
  }

  /**
   * 执行迁移
   */
  async migrate() {
    console.log('🚀 开始Payload类型迁移...');
    console.log(`   目标路径: ${this.targetPath}`);
    console.log(`   模式: ${this.dryRun ? 'DRY RUN（预览模式）' : 'EXECUTE（执行模式）'}`);
    console.log('');

    await this.scanDirectory(this.targetPath);
    this.printSummary();
  }

  /**
   * 扫描目录
   */
  async scanDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      console.error(`❌ 目录不存在: ${dirPath}`);
      return;
    }

    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory()) {
        // 跳过node_modules和dist目录
        if (!['node_modules', 'dist', '.git'].includes(entry.name)) {
          await this.scanDirectory(fullPath);
        }
      } else if (entry.isFile() && this.isControllerFile(entry.name)) {
        await this.processFile(fullPath);
      }
    }
  }

  /**
   * 判断是否是controller文件
   */
  isControllerFile(filename) {
    return filename.endsWith('.controller.ts');
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    try {
      this.stats.filesScanned++;
      console.log(`📄 扫描文件: ${filePath}`);

      const content = fs.readFileSync(filePath, 'utf8');
      const modifiedContent = this.modifyFileContent(content, filePath);

      if (content !== modifiedContent) {
        this.stats.filesModified++;
        
        if (this.dryRun) {
          console.log(`   ✏️ [DRY RUN] 文件将被修改`);
          this.showDiff(content, modifiedContent);
        } else {
          fs.writeFileSync(filePath, modifiedContent, 'utf8');
          console.log(`   ✅ 文件已修改`);
        }
      } else {
        console.log(`   ℹ️ 文件无需修改`);
      }
    } catch (error) {
      this.stats.errors.push({ file: filePath, error: error.message });
      console.error(`   ❌ 处理文件失败: ${error.message}`);
    }
  }

  /**
   * 修改文件内容
   */
  modifyFileContent(content, filePath) {
    let modified = content;
    let hasModifications = false;

    // 1. 添加import语句（如果需要）
    if (this.needsImport(content)) {
      modified = this.addImportStatement(modified);
      hasModifications = true;
    }

    // 2. 修改@MessagePattern方法的payload类型
    const methodMatches = this.findMessagePatternMethods(modified);
    
    for (const match of methodMatches) {
      const modifiedMethod = this.modifyMethodPayload(match);
      if (modifiedMethod !== match.fullMatch) {
        modified = modified.replace(match.fullMatch, modifiedMethod);
        this.stats.methodsModified++;
        hasModifications = true;
        console.log(`     🔧 修改方法: ${match.methodName}`);
      }
    }

    return modified;
  }

  /**
   * 检查是否需要添加import
   */
  needsImport(content) {
    return !content.includes('InjectedContext') && 
           !content.includes('@common/types');
  }

  /**
   * 添加import语句
   */
  addImportStatement(content) {
    // 找到最后一个import语句的位置
    const importRegex = /import\s+.*?from\s+['"][^'"]+['"];?\s*\n/g;
    let lastImportMatch;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      lastImportMatch = match;
    }

    if (lastImportMatch) {
      const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
      const importStatement = "import { InjectedContext } from '@common/types';\n";
      
      return content.slice(0, insertPosition) + 
             importStatement + 
             content.slice(insertPosition);
    } else {
      // 如果没有找到import语句，在文件开头添加
      return "import { InjectedContext } from '@common/types';\n" + content;
    }
  }

  /**
   * 查找所有@MessagePattern方法
   */
  findMessagePatternMethods(content) {
    const methods = [];

    // 更强大的正则表达式，支持多行匹配
    const methodRegex = /@MessagePattern\([^)]+\)\s*(?:@[^(]+\([^)]*\)\s*)*async\s+(\w+)\s*\(\s*@Payload\(\)\s+(\w+):\s*([^)]+(?:\([^)]*\))*[^)]*)\)/gs;

    let match;
    while ((match = methodRegex.exec(content)) !== null) {
      methods.push({
        fullMatch: match[0],
        methodName: match[1],
        paramName: match[2],
        paramType: match[3].trim(),
        index: match.index
      });
    }

    return methods;
  }

  /**
   * 修改方法的payload类型
   */
  modifyMethodPayload(methodMatch) {
    const { fullMatch, methodName, paramName, paramType } = methodMatch;

    // 如果已经包含injectedContext，跳过
    if (paramType.includes('injectedContext')) {
      return fullMatch;
    }

    // 处理不同的类型格式
    let newParamType;

    // 清理参数类型，移除多余的空白字符
    const cleanParamType = paramType.replace(/\s+/g, ' ').trim();

    if (this.isObjectType(cleanParamType)) {
      // 对象类型处理
      newParamType = this.addInjectedContextToObjectType(cleanParamType);
    } else {
      // 其他类型（接口、联合类型等）：暂时跳过，需要手动处理
      console.log(`     ⚠️ 复杂类型需要手动处理: ${cleanParamType.substring(0, 50)}...`);
      return fullMatch;
    }

    // 替换参数类型
    return fullMatch.replace(paramType, newParamType);
  }

  /**
   * 判断是否是对象类型
   */
  isObjectType(paramType) {
    const trimmed = paramType.trim();
    return trimmed.startsWith('{') && trimmed.endsWith('}');
  }

  /**
   * 向对象类型添加injectedContext
   */
  addInjectedContextToObjectType(paramType) {
    const trimmed = paramType.trim();

    // 移除首尾的大括号
    let content = trimmed.slice(1, -1).trim();

    // 如果内容为空
    if (!content) {
      return '{ injectedContext?: InjectedContext }';
    }

    // 确保最后一个属性后面有分号
    if (!content.endsWith(';') && !content.endsWith(',')) {
      content += ';';
    }

    // 添加injectedContext
    return `{ ${content} injectedContext?: InjectedContext }`;
  }

  /**
   * 显示差异（简化版）
   */
  showDiff(original, modified) {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    console.log('     📋 变更预览:');
    
    // 简单的行对比
    for (let i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
      const origLine = originalLines[i] || '';
      const modLine = modifiedLines[i] || '';
      
      if (origLine !== modLine) {
        if (origLine) console.log(`     - ${origLine}`);
        if (modLine) console.log(`     + ${modLine}`);
      }
    }
  }

  /**
   * 打印统计信息
   */
  printSummary() {
    console.log('\n📊 迁移统计:');
    console.log(`   扫描文件: ${this.stats.filesScanned}`);
    console.log(`   修改文件: ${this.stats.filesModified}`);
    console.log(`   修改方法: ${this.stats.methodsModified}`);
    
    if (this.stats.errors.length > 0) {
      console.log(`   错误数量: ${this.stats.errors.length}`);
      console.log('\n❌ 错误详情:');
      this.stats.errors.forEach(error => {
        console.log(`   - ${error.file}: ${error.error}`);
      });
    }

    if (this.dryRun) {
      console.log('\n💡 这是预览模式，没有实际修改文件');
      console.log('   要执行实际修改，请运行: node scripts/migrate-payload-types.js');
    } else {
      console.log('\n✅ 迁移完成！');
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: args.includes('--dry-run'),
    targetPath: args.find(arg => arg.startsWith('--target='))?.split('=')[1] || 'apps'
  };

  const migrator = new PayloadTypeMigrator(options);
  await migrator.migrate();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { PayloadTypeMigrator };
