#!/usr/bin/env node

/**
 * 测试Payload类型迁移脚本
 * 
 * 功能：
 * 1. 创建测试文件
 * 2. 运行迁移脚本
 * 3. 验证结果
 * 4. 清理测试文件
 */

const fs = require('fs');
const path = require('path');
const { PayloadTypeMigrator } = require('./migrate-payload-types');

class PayloadMigrationTester {
  constructor() {
    this.testDir = path.join(__dirname, 'test-migration');
    this.testFiles = [];
  }

  /**
   * 运行测试
   */
  async runTests() {
    console.log('🧪 开始Payload迁移脚本测试...\n');

    try {
      await this.setupTestEnvironment();
      await this.runMigrationTests();
      await this.verifyResults();
      console.log('\n✅ 所有测试通过！');
    } catch (error) {
      console.error('\n❌ 测试失败:', error.message);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 设置测试环境
   */
  async setupTestEnvironment() {
    console.log('📁 设置测试环境...');
    
    // 创建测试目录
    if (!fs.existsSync(this.testDir)) {
      fs.mkdirSync(this.testDir, { recursive: true });
    }

    // 创建测试文件
    await this.createTestFiles();
  }

  /**
   * 创建测试文件
   */
  async createTestFiles() {
    const testCases = [
      {
        name: 'simple-object.controller.ts',
        content: `import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

@Controller()
export class SimpleObjectController {
  private readonly logger = new Logger(SimpleObjectController.name);

  @MessagePattern('test.simple')
  async simpleMethod(@Payload() payload: { id: string; name?: string }) {
    return { success: true };
  }

  @MessagePattern('test.complex')
  async complexMethod(@Payload() payload: { 
    heroId: string; 
    serverId?: string;
    options: { level: number; exp: number }
  }) {
    return { success: true };
  }
}`
      },
      {
        name: 'already-migrated.controller.ts',
        content: `import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { InjectedContext } from '@common/types';

@Controller()
export class AlreadyMigratedController {
  @MessagePattern('test.migrated')
  async migratedMethod(@Payload() payload: { 
    id: string; 
    injectedContext?: InjectedContext 
  }) {
    return { success: true };
  }
}`
      },
      {
        name: 'complex-types.controller.ts',
        content: `import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

interface CustomDto {
  name: string;
  value: number;
}

@Controller()
export class ComplexTypesController {
  @MessagePattern('test.interface')
  async interfaceMethod(@Payload() payload: CustomDto) {
    return { success: true };
  }

  @MessagePattern('test.union')
  async unionMethod(@Payload() payload: string | { id: string }) {
    return { success: true };
  }
}`
      }
    ];

    for (const testCase of testCases) {
      const filePath = path.join(this.testDir, testCase.name);
      fs.writeFileSync(filePath, testCase.content, 'utf8');
      this.testFiles.push(filePath);
      console.log(`   ✅ 创建测试文件: ${testCase.name}`);
    }
  }

  /**
   * 运行迁移测试
   */
  async runMigrationTests() {
    console.log('\n🔧 运行迁移测试...');

    // 先运行dry-run模式
    console.log('   📋 运行预览模式...');
    const dryRunMigrator = new PayloadTypeMigrator({
      dryRun: true,
      targetPath: this.testDir
    });
    await dryRunMigrator.migrate();

    // 再运行实际迁移
    console.log('\n   ⚡ 运行实际迁移...');
    const migrator = new PayloadTypeMigrator({
      dryRun: false,
      targetPath: this.testDir
    });
    await migrator.migrate();
  }

  /**
   * 验证结果
   */
  async verifyResults() {
    console.log('\n🔍 验证迁移结果...');

    const expectations = [
      {
        file: 'simple-object.controller.ts',
        shouldContain: [
          "import { InjectedContext } from '@common/types';",
          "{ id: string; name?: string; injectedContext?: InjectedContext }",
          "{ heroId: string; serverId?: string; options: { level: number; exp: number }; injectedContext?: InjectedContext }"
        ]
      },
      {
        file: 'already-migrated.controller.ts',
        shouldContain: [
          "injectedContext?: InjectedContext"
        ],
        shouldNotContain: [
          "injectedContext?: InjectedContext; injectedContext?: InjectedContext" // 不应该重复
        ]
      },
      {
        file: 'complex-types.controller.ts',
        shouldContain: [
          "payload: CustomDto", // 复杂类型应该保持不变
          "payload: string | { id: string }" // 联合类型应该保持不变
        ]
      }
    ];

    for (const expectation of expectations) {
      const filePath = path.join(this.testDir, expectation.file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      console.log(`   📄 验证文件: ${expectation.file}`);

      // 检查应该包含的内容
      if (expectation.shouldContain) {
        for (const expectedContent of expectation.shouldContain) {
          if (!content.includes(expectedContent)) {
            throw new Error(`文件 ${expectation.file} 应该包含: ${expectedContent}`);
          }
          console.log(`     ✅ 包含预期内容: ${expectedContent.substring(0, 50)}...`);
        }
      }

      // 检查不应该包含的内容
      if (expectation.shouldNotContain) {
        for (const unexpectedContent of expectation.shouldNotContain) {
          if (content.includes(unexpectedContent)) {
            throw new Error(`文件 ${expectation.file} 不应该包含: ${unexpectedContent}`);
          }
          console.log(`     ✅ 不包含意外内容: ${unexpectedContent.substring(0, 50)}...`);
        }
      }
    }
  }

  /**
   * 清理测试环境
   */
  async cleanup() {
    console.log('\n🧹 清理测试环境...');
    
    try {
      if (fs.existsSync(this.testDir)) {
        fs.rmSync(this.testDir, { recursive: true, force: true });
        console.log('   ✅ 测试目录已清理');
      }
    } catch (error) {
      console.warn('   ⚠️ 清理测试目录失败:', error.message);
    }
  }
}

// 主函数
async function main() {
  const tester = new PayloadMigrationTester();
  await tester.runTests();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = { PayloadMigrationTester };
