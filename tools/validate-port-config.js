#!/usr/bin/env node

/**
 * 端口配置验证工具
 * 
 * 功能：
 * 1. 验证端口配置的正确性
 * 2. 检测端口冲突
 * 3. 生成端口分配预览
 * 4. 验证所有服务的端口范围
 */

// 动态导入PortManager（支持TypeScript编译后的路径）
let PortManager;
try {
  // 尝试从编译后的路径导入
  PortManager = require('../dist/libs/common/common/src/port-manager/port-manager').PortManager;
} catch (error) {
  try {
    // 尝试从源码路径导入（开发环境）
    PortManager = require('../libs/common/src/port-manager/port-manager').PortManager;
  } catch (error2) {
    console.error('❌ 无法导入PortManager，请先编译项目：npx nest build common');
    process.exit(1);
  }
}

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * 验证端口配置
 */
function validatePortConfiguration() {
  console.log(colorize('🔍 验证端口配置...', 'cyan'));
  console.log('='.repeat(60));
  
  try {
    const result = PortManager.validatePortConfiguration();
    
    if (result.valid) {
      console.log(colorize('✅ 端口配置验证通过', 'green'));
    } else {
      console.log(colorize('❌ 端口配置验证失败', 'red'));
      
      if (result.errors.length > 0) {
        console.log(colorize('\n错误:', 'red'));
        result.errors.forEach(error => {
          console.log(colorize(`  - ${error}`, 'red'));
        });
      }
    }
    
    if (result.warnings.length > 0) {
      console.log(colorize('\n警告:', 'yellow'));
      result.warnings.forEach(warning => {
        console.log(colorize(`  - ${warning}`, 'yellow'));
      });
    }
    
    return result.valid;
  } catch (error) {
    console.error(colorize(`❌ 验证过程出错: ${error.message}`, 'red'));
    return false;
  }
}

/**
 * 生成端口分配预览
 */
function generatePortAllocationPreview() {
  console.log(colorize('\n📊 端口分配预览', 'cyan'));
  console.log('='.repeat(60));
  
  const services = ['gateway', 'auth', 'character', 'hero', 'economy', 'social', 'activity', 'match'];
  const servers = ['default', 'server_001', 'server_002', 'server_003'];
  const instances = [0, 1, 2];
  
  // 表头
  console.log(colorize('服务'.padEnd(12) + '区服'.padEnd(12) + '实例'.padEnd(8) + '端口'.padEnd(8) + 'URL', 'blue'));
  console.log('-'.repeat(60));
  
  services.forEach(serviceName => {
    servers.forEach(serverId => {
      instances.forEach(instanceId => {
        try {
          const port = PortManager.calculatePort(serviceName, serverId === 'default' ? undefined : serverId, instanceId);
          const url = `http://localhost:${port}`;
          
          console.log(
            serviceName.padEnd(12) +
            serverId.padEnd(12) +
            instanceId.toString().padEnd(8) +
            port.toString().padEnd(8) +
            url
          );
        } catch (error) {
          console.log(colorize(
            serviceName.padEnd(12) +
            serverId.padEnd(12) +
            instanceId.toString().padEnd(8) +
            'ERROR'.padEnd(8) +
            error.message, 'red'
          ));
        }
      });
    });
    console.log(); // 空行分隔不同服务
  });
}

/**
 * 检查端口范围
 */
function checkPortRanges() {
  console.log(colorize('\n📋 端口范围检查', 'cyan'));
  console.log('='.repeat(60));
  
  const services = ['gateway', 'auth', 'character', 'hero', 'economy', 'social', 'activity', 'match'];
  
  console.log(colorize('服务'.padEnd(12) + '基础端口'.padEnd(12) + '最小端口'.padEnd(12) + '最大端口'.padEnd(12) + '范围大小', 'blue'));
  console.log('-'.repeat(60));
  
  services.forEach(serviceName => {
    try {
      const basePort = PortManager.getBasePort(serviceName);
      const range = PortManager.getPortRange(serviceName);
      const rangeSize = range.max - range.min + 1;
      
      console.log(
        serviceName.padEnd(12) +
        basePort.toString().padEnd(12) +
        range.min.toString().padEnd(12) +
        range.max.toString().padEnd(12) +
        rangeSize.toString()
      );
    } catch (error) {
      console.log(colorize(
        serviceName.padEnd(12) +
        'ERROR'.padEnd(12) +
        error.message, 'red'
      ));
    }
  });
}

/**
 * 检测端口冲突
 */
function detectPortConflicts() {
  console.log(colorize('\n⚠️  端口冲突检测', 'cyan'));
  console.log('='.repeat(60));
  
  const services = ['gateway', 'auth', 'character', 'hero', 'economy', 'social', 'activity', 'match'];
  const servers = ['server_001', 'server_002', 'server_003'];
  const instances = [0, 1, 2];
  
  const portMap = new Map(); // port -> [service, serverId, instanceId]
  const conflicts = [];
  
  services.forEach(serviceName => {
    servers.forEach(serverId => {
      instances.forEach(instanceId => {
        try {
          const port = PortManager.calculatePort(serviceName, serverId, instanceId);
          const key = `${serviceName}:${serverId}:${instanceId}`;
          
          if (portMap.has(port)) {
            conflicts.push({
              port,
              existing: portMap.get(port),
              new: key,
            });
          } else {
            portMap.set(port, key);
          }
        } catch (error) {
          // 忽略计算错误
        }
      });
    });
  });
  
  if (conflicts.length === 0) {
    console.log(colorize('✅ 未检测到端口冲突', 'green'));
  } else {
    console.log(colorize(`❌ 检测到 ${conflicts.length} 个端口冲突:`, 'red'));
    conflicts.forEach(conflict => {
      console.log(colorize(`  端口 ${conflict.port}: ${conflict.existing} <-> ${conflict.new}`, 'red'));
    });
  }
  
  return conflicts.length === 0;
}

/**
 * 主函数
 */
function main() {
  console.log(colorize('🔌 端口配置验证工具', 'magenta'));
  console.log(colorize('版本: 1.0.0', 'white'));
  console.log(colorize('用途: 验证动态端口分配配置', 'white'));
  console.log();
  
  // 1. 验证端口配置
  const configValid = validatePortConfiguration();
  
  // 2. 生成端口分配预览
  generatePortAllocationPreview();
  
  // 3. 检查端口范围
  checkPortRanges();
  
  // 4. 检测端口冲突
  const noConflicts = detectPortConflicts();
  
  // 总结
  console.log(colorize('\n📋 验证总结', 'cyan'));
  console.log('='.repeat(60));
  
  if (configValid && noConflicts) {
    console.log(colorize('✅ 所有验证通过，端口配置正确', 'green'));
    process.exit(0);
  } else {
    console.log(colorize('❌ 验证失败，请检查上述错误', 'red'));
    process.exit(1);
  }
}

// 检查是否直接运行
if (require.main === module) {
  main();
}

module.exports = {
  validatePortConfiguration,
  generatePortAllocationPreview,
  checkPortRanges,
  detectPortConflicts,
};
