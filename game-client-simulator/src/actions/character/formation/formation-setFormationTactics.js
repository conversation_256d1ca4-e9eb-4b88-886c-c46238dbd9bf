/**
 * 设置阵容战术 对应old项目: setFormationTactics方法
 * 
 * 微服务: character
 * 模块: formation
 * Controller: formation
 * Pattern: formation.setFormationTactics
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.340Z
 */

const BaseAction = require('../../../core/base-action');

class FormationsetFormationTacticsAction extends BaseAction {
  static metadata = {
    name: '设置阵容战术 对应old项目: setFormationTactics方法',
    description: '设置阵容战术 对应old项目: setFormationTactics方法',
    category: 'character',
    serviceName: 'character',
    module: 'formation',
    actionName: 'formation.setFormationTactics',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "resId": {
            "type": "number",
            "required": true,
            "description": "resId参数"
      },
      "tacticsType": {
            "type": "string",
            "required": true,
            "description": "tacticsType参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, uid, resId, tacticsType, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      uid,
      resId,
      tacticsType,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '设置阵容战术 对应old项目: setFormationTactics方法成功'
      };
    } else {
      throw new Error(`设置阵容战术 对应old项目: setFormationTactics方法失败: ${response.message}`);
    }
  }
}

module.exports = FormationsetFormationTacticsAction;