# 足球经理游戏服务器环境变量配置

# 服务端口配置
GATEWAY_PORT=3000
AUTH_PORT=3001
CHARACTER_PORT=3002
GAME_PORT=3003
CLUB_PORT=3004
MATCH_PORT=3005
CARD_PORT=3006
HERO_PORT=3007
NOTIFICATION_PORT=3008
ECONOMY_PORT=3009
SOCIAL_PORT=3010
ACTIVITY_PORT=3011

# MongoDB 数据库配置
# 认证服务数据库
AUTH_MONGODB_URI=mongodb://***************:27017/auth_db

# 角色服务数据库
CHARACTER_MONGODB_URI=mongodb://***************:27017/character_db

# 球员服务数据库
HERO_MONGODB_URI=mongodb://***************:27017/hero_db

# 经济服务数据库
ECONOMY_MONGODB_URI=mongodb://***************:27017/economy_db

# 社交服务数据库
SOCIAL_MONGODB_URI=mongodb://***************:27017/social_db

# 活动服务数据库
ACTIVITY_MONGODB_URI=mongodb://***************:27017/activity_db

# 旧项目数据库（用于数据迁移）
OLD_MONGODB_URI=mongodb://***************:27017/old_game_db

# Redis 配置
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 环境配置
NODE_ENV=development
PROJECT_NAME=football-manager
SERVER_PREFIX=dev

# JWT 配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# 微服务通信配置
MICROSERVICE_SECRET=your-microservice-secret
MICROSERVICE_TIMEOUT=30000

# 日志配置
LOG_LEVEL=debug
LOG_FILE_PATH=./logs

# 游戏配置
GAME_CONFIG_PATH=./config/game
GAME_DATA_PATH=./data/game

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
