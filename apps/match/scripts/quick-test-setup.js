/**
 * 快速测试数据准备脚本
 * 专门为Match服务测试快速准备必要数据
 * 基于成功的test-match-system.js模式
 *
 * 使用方法：
 * node apps/match/scripts/quick-test-setup.js [characterId]
 *
 * 如果不提供characterId，会创建新的测试角色
 */

const MicroserviceWebSocketClient = require('../../../scripts/common/websocket-client');
const axios = require("axios");

class QuickTestSetup extends MicroserviceWebSocketClient {
  constructor() {
    super({
      GATEWAY_URL: 'http://127.0.0.1:3000',
      AUTH_URL: 'http://127.0.0.1:3100',
      TIMEOUT: 30000
    });
  }

  /**
   * 快速设置测试数据
   */
  async setup(existingUsername = null) {
    console.log('🚀 快速设置Match服务测试数据');

    try {
      // 第1步：健康检查
      console.log('\n=== 第1步：服务健康检查 ===');
      const healthChecks = [
        { name: '网关服务', url: this.config.GATEWAY_URL },
        { name: 'Auth服务', url: this.config.AUTH_URL }
      ];

      for (const check of healthChecks) {
        const isHealthy = await this.checkHttpHealth(check.name, check.url);
        if (!isHealthy) {
          throw new Error(`${check.name}健康检查失败`);
        }
      }

      // 第2.1步：账号认证
      console.log('\n=== 第2.1步：认证 ===');
      await this.getAuthToken(existingUsername);

      console.log(`测试用户: ${this.username}`);
      console.log(`测试服务器ID: ${this.serverId}`);

      // 第2.2：角色认证
      console.log('\n=== 第2.2步：创建测试角色 ===');
      await this.getCharacterToken();
      console.log(`测试角色ID: ${this.characterId}`);
      console.log(`测试服务器ID: ${this.serverId}`);

      // 第3.1步：使用角色Token连接WebSocket
      console.log('\n=== 第3步：使用角色Token连接WebSocket ===');
      await this.connectWebSocket();
      console.log('✅ WebSocket连接成功');

      // 第3.2步：character服务器登录角色
      console.log('\n=== 第3.2步：character服务器登录角色 ===');
      await this.loginCharacterInCharacterService();

      // 第4步：快速添加资源
      console.log('\n=== 第4步：添加测试资源 ===');
      await this.addTestResources(this.characterId);

      // 第5步：提升角色等级（开放球场）
      console.log('\n=== 第5步：提升角色等级和开放球场 ===');
      await this.upgradeCharacterLevel(this.characterId);

      // 第6步：创建默认阵容（解决商业赛匹配问题）
      console.log('\n=== 第6步：创建默认阵容 ===');
      await this.setupDefaultFormation(this.characterId);

      // 第7步：修复现有对手角色的球场等级和阵容
      console.log('\n=== 第7步：修复现有对手角色球场等级和阵容 ===');
      // await this.fixExistingEnemiesFieldLevel();

      // 第8步：创建对手角色
      console.log('\n=== 第8步：创建对手角色 ===');
      // await this.createQuickEnemies();

      // 第9步：验证设置
      console.log('\n=== 第9步：验证设置结果 ===');
      await this.verifySetup(this.characterId);

      console.log('\n🎉 快速测试数据设置完成！');
      console.log(`\n📋 测试角色信息:`);
      console.log(`   用户名: ${this.username}`);
      console.log(`   角色ID: ${this.characterId}`);
      console.log(`   服务器: ${this.serverId}`);
      console.log(`\n🚀 现在可以运行Match服务测试了！`);
      console.log(`   命令: node apps/match/scripts/test-match-system.js business`);

      return this.username;

    } catch (error) {
      console.error('❌ 快速设置失败:', error);
      throw error;
    } finally {
      this.disconnect();
    }
  }

  /**
   * 使用Character服务登录角色
   */
  async loginCharacterInCharacterService() {
    try {
      const response = await this.sendMessage('character.character.login', {
        loginDto: {
          userId: this.userId,
          characterId: this.characterId,
          serverId: this.serverId,
        }
      });

      if (response && response.payload && response.payload.success && response.payload.data && response.payload.data.code === 0) {
        console.log(`✅ 角色登录成功`);
      } else {
        console.log(`⚠️ 角色登录失败`);
      }
    } catch (error) {
      console.log(`⚠️ ${resource.name}添加失败: ${error.message}，继续测试`);
    }
  }

  /**
   * 快速添加测试资源 - 使用正确的Character服务API
   */
  async addTestResources(characterId) {
    const resources = [
      { type: 'cash', amount: 1000000, name: '球币' },
      { type: 'worldCoin', amount: 50000, name: '欧元' },
      { type: 'gold', amount: 100000, name: '金币' },
      { type: 'energy', amount: 1000, name: '体力' }
    ];

    for (const resource of resources) {
      try {
        // 使用正确的Character服务货币添加API（参考成功的测试脚本）
        const response = await this.sendMessage('character.character.currency.add', {
          characterId: characterId,
          serverId: this.serverId,
          currencyDto: {
            currencyType: resource.type,
            amount: resource.amount,
            reason: 'quick_test_setup'
          }
        });

        if (response && response.payload && response.payload.success && response.payload.data && response.payload.data.code === 0) {
          console.log(`✅ ${resource.name}: +${resource.amount}`);
        } else {
          console.log(`⚠️ ${resource.name}添加失败: ${response.payload?.data?.message || '未知错误'}，继续测试`);
        }
      } catch (error) {
        console.log(`⚠️ ${resource.name}添加失败: ${error.message}，继续测试`);
      }
    }
  }

  /**
   * 创建默认阵容
   * 解决商业赛匹配中"阵容数据为空"的问题
   */
  async setupDefaultFormation(characterId) {
    try {
      console.log('⚽ 检查和创建默认阵容...');

      // 1. 检查当前阵容状态
      const formationsResponse = await this.sendMessage('character.formation.getFormations', {
        characterId: characterId,
        serverId: this.serverId
      });

      if (!formationsResponse || !formationsResponse.payload || !formationsResponse.payload.success || formationsResponse.payload.data.code !== 0) {
        console.log('⚠️ 获取阵容数据失败，跳过阵容创建');
        return;
      }

      const formationsData = formationsResponse.payload.data.data;
      console.log('📊 阵容数据:',JSON.stringify(formationsData, null, 2));
      console.log(`📊 当前阵容数量: ${formationsData.teamFormations.length}`);
      console.log(`📊 当前激活阵容: ${formationsData.currTeamFormationId || '无'}`);

      // 2. 如果没有阵容，创建默认阵容
      if (formationsData.teamFormations.length === 0) {
        console.log('🏗️ 创建默认阵容 (4-4-2)...');

        const createResponse = await this.sendMessage('character.formation.createFormation', {
          characterId: characterId,
          serverId: this.serverId,
          resId: 442101, // 4-4-2阵型
          type: 1 // 普通阵容
        });

        if (createResponse && createResponse.payload && createResponse.payload.success && createResponse.payload.data.code === 0) {
          const newFormation = createResponse.payload.data.data;
          console.log(`✅ 阵容创建成功: ${newFormation.uid}`);

          // 3. 设置为当前激活阵容
          console.log('🎯 设置为激活阵容...');
          const setActiveResponse = await this.sendMessage('character.formation.setActiveFormation', {
            characterId: characterId,
            serverId: this.serverId,
            formationId: newFormation.uid
          });

          if (setActiveResponse && setActiveResponse.payload && setActiveResponse.payload.success && setActiveResponse.payload.data.code === 0) {
            console.log('✅ 激活阵容设置成功');
          } else {
            console.log('⚠️ 激活阵容设置失败:', setActiveResponse.payload?.data?.message);
          }

          // 4. 获取角色的球员列表（用于自动布阵）
          console.log('👥 获取球员列表进行自动布阵...');
          const heroesResponse = await this.sendMessage('hero.hero.getList', {
            characterId: characterId,
            serverId: this.serverId
          });

          if (heroesResponse && heroesResponse.payload && heroesResponse.payload.success && heroesResponse.payload.data.code === 0) {
            const heroes = heroesResponse.payload.data.data.heroes || [];
            console.log(`📊 可用球员数量: ${heroes.length}`);

            if (heroes.length >= 11) {
              // 5. 执行自动布阵
              console.log('⚽ 执行自动布阵...');
              const autoFormationResponse = await this.sendMessage('character.formation.autoFormation', {
                characterId: characterId,
                serverId: this.serverId,
                formationId: newFormation.uid
              });

              if (autoFormationResponse && autoFormationResponse.payload && autoFormationResponse.payload.success && autoFormationResponse.payload.data.code === 0) {
                console.log('✅ 自动布阵成功');
              } else {
                console.log('⚠️ 自动布阵失败，但阵容已创建:', autoFormationResponse.payload?.data?.message);
              }
            } else {
              console.log(`⚠️ 球员数量不足(${heroes.length}/11)，创建默认球员...`);
              await this.createDefaultHeroes(characterId);

              // 重新尝试自动布阵
              console.log('⚽ 重新执行自动布阵...');
              const autoFormationResponse = await this.sendMessage('character.formation.autoFormation', {
                characterId: characterId,
                serverId: this.serverId,
                formationId: newFormation.uid
              });

              if (autoFormationResponse && autoFormationResponse.payload && autoFormationResponse.payload.success && autoFormationResponse.payload.data.code === 0) {
                console.log('✅ 自动布阵成功');
              } else {
                console.log('⚠️ 自动布阵失败:', autoFormationResponse.payload?.data?.message);
              }
            }
          } else {
            console.log('⚠️ 获取球员列表失败，创建默认球员...');
            await this.createDefaultHeroes(characterId);

            // 重新尝试自动布阵
            console.log('⚽ 重新执行自动布阵...');
            const autoFormationResponse = await this.sendMessage('character.formation.autoFormation', {
              characterId: characterId,
              serverId: this.serverId,
              formationId: newFormation.uid
            });

            if (autoFormationResponse && autoFormationResponse.payload && autoFormationResponse.payload.success && autoFormationResponse.payload.data.code === 0) {
              console.log('✅ 自动布阵成功');
            } else {
              console.log('⚠️ 自动布阵失败:', autoFormationResponse.payload?.data?.message);
            }
          }

        } else {
          console.log('❌ 阵容创建失败:', createResponse.payload?.data?.message);
        }
      } else {
        console.log('✅ 角色已有阵容，检查阵容中的球员配置...');

        // 如果没有激活阵容，设置第一个为激活阵容
        if (!formationsData.currTeamFormationId && formationsData.teamFormations.length > 0) {
          const firstFormationId = formationsData.teamFormations[0].uid;
          console.log(`🎯 设置第一个阵容为激活阵容: ${firstFormationId}`);

          const setActiveResponse = await this.sendMessage('character.formation.setActiveFormation', {
            characterId: characterId,
            serverId: this.serverId,
            formationId: firstFormationId
          });

          if (setActiveResponse && setActiveResponse.payload && setActiveResponse.payload.success && setActiveResponse.payload.data.code === 0) {
            console.log('✅ 激活阵容设置成功');
          } else {
            console.log('⚠️ 激活阵容设置失败:', setActiveResponse.payload?.data?.message);
          }
        }

        // 检查当前激活阵容中是否有球员
        if (formationsData.currTeamFormationId) {
          const activeFormation = formationsData.teamFormations.find(f => f.uid === formationsData.currTeamFormationId);
          if (activeFormation) {
            // 检查阵容中的球员配置
            const positionToHerosObject = activeFormation.positionToHerosObject || {};
            const totalPlayers = Object.values(positionToHerosObject).reduce((sum, players) => sum + (players ? players.length : 0), 0);

            console.log(`📊 当前阵容中的球员数量: ${totalPlayers}`);

            if (totalPlayers === 0) {
              console.log('⚠️ 阵容中没有球员，需要创建球员并重新布阵...');

              // 获取球员列表
              const heroesResponse = await this.sendMessage('hero.hero.getList', {
                characterId: characterId,
                serverId: this.serverId
              });

              if (heroesResponse && heroesResponse.payload && heroesResponse.payload.success && heroesResponse.payload.data.code === 0) {
                const heroes = heroesResponse.payload.data.data.list || [];
                console.log(`📊 可用球员数量: ${heroes.length}`);

                if (heroes.length < 11) {
                  console.log('👥 球员数量不足，创建默认球员...');
                  await this.createDefaultHeroes(characterId);
                }

                // 执行自动布阵
                console.log('⚽ 执行自动布阵...');
                const autoFormationResponse = await this.sendMessage('character.formation.autoFormation', {
                  characterId: characterId,
                  serverId: this.serverId,
                  formationId: formationsData.currTeamFormationId
                });

                if (autoFormationResponse && autoFormationResponse.payload && autoFormationResponse.payload.success && autoFormationResponse.payload.data.code === 0) {
                  console.log('✅ 自动布阵成功');
                } else {
                  console.log('⚠️ 自动布阵失败:', autoFormationResponse.payload?.data?.message);
                }
              } else {
                console.log('⚠️ 获取球员列表失败，创建默认球员...');
                await this.createDefaultHeroes(characterId);

                // 重新尝试自动布阵
                console.log('⚽ 重新执行自动布阵...');
                const autoFormationResponse = await this.sendMessage('character.formation.autoFormation', {
                  characterId: characterId,
                  serverId: this.serverId,
                  formationId: formationsData.currTeamFormationId
                });

                if (autoFormationResponse && autoFormationResponse.payload && autoFormationResponse.payload.success && autoFormationResponse.payload.data.code === 0) {
                  console.log('✅ 自动布阵成功');
                } else {
                  console.log('⚠️ 自动布阵失败:', autoFormationResponse.payload?.data?.message);
                }
              }
            } else {
              console.log('✅ 阵容中已有球员配置，无需重新布阵');
            }
          }
        }
      }

    } catch (error) {
      console.log(`⚠️ 创建默认阵容失败: ${error.message}，继续测试`);
    }
  }

  /**
   * 提升角色等级和开放球场
   * 基于Character服务代码分析：球场开放基于fieldLevel而不是角色等级
   */
  async upgradeCharacterLevel(characterId) {
    try {
      console.log('📈 提升角色等级到10级...');

      // 直接更新角色等级
      const updateLevelResponse = await this.sendMessage('character.character.update', {
        characterId: characterId,
        serverId: this.serverId,
        updateDto: {
          level: 10
        }
      });

      if (updateLevelResponse && updateLevelResponse.payload && updateLevelResponse.payload.success && updateLevelResponse.payload.data && updateLevelResponse.payload.data.code === 0) {
        console.log('✅ 角色等级直接更新成功: 10级');
      } else {
        console.log('⚠️ 角色等级更新失败，继续测试');
      }

      // 关键：设置球场等级（这是球场开放的真正条件）
      console.log('🏟️ 设置球场等级（开放球场）...');

      const fieldResponse = await this.sendMessage('character.character.update', {
        characterId: characterId,
        serverId: this.serverId,
        updateDto: {
          fieldLevel: 1 // 设置球场等级为1，满足 fieldLevel > 0 的开放条件
        }
      });

      if (fieldResponse && fieldResponse.payload && fieldResponse.payload.success && fieldResponse.payload.data && fieldResponse.payload.data.code === 0) {
        console.log('✅ 球场等级设置成功: 1级（球场已开放）');
      } else {
        console.log('⚠️ 球场等级设置失败，可能影响商业赛功能');
      }

    } catch (error) {
      console.log(`⚠️ 角色等级和球场设置失败: ${error.message}，继续测试`);
    }
  }

  /**
   * 修复现有对手角色的球场等级
   * 通过搜索找到现有角色并修复它们的fieldLevel
   */
  async fixExistingEnemiesFieldLevel() {
    console.log('🔧 修复现有对手角色的球场等级...');

    // 尝试搜索一些常见的对手角色名称
    const commonEnemyNames = [
      '测试对手', 'TestEnemy01', 'TestEnemy02', 'TestEnemy03',
      'QuickTest', 'test', 'enemy', '对手', 'player'
    ];

    const foundEnemies = [];

    for (const enemyName of commonEnemyNames) {
      try {
        console.log(`🔍 搜索对手角色: ${enemyName}...`);

        const searchResponse = await this.sendMessage('character.character.searchByName', {
          name: enemyName,
          serverId: this.serverId
        });

        if (searchResponse && searchResponse.payload && searchResponse.payload.success && searchResponse.payload.data && searchResponse.payload.data.code === 0) {
          const character = searchResponse.payload.data.data;
          // 注意：searchByName返回的是单个角色对象，不是数组
          if (character && character.characterId && character.isGroundOpen === false) {
            foundEnemies.push({
              characterId: character.characterId,
              name: character.name,
              isGroundOpen: character.isGroundOpen
            });
            console.log(`   找到需要修复的角色: ${character.name} (${character.characterId}) isGroundOpen=${character.isGroundOpen}`);
          }
        }

        // 避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.log(`⚠️ 搜索 ${enemyName} 失败: ${error.message}`);
      }
    }

    if (foundEnemies.length === 0) {
      console.log('⚠️ 未找到需要修复的对手角色');
      return;
    }

    console.log(`🎯 找到 ${foundEnemies.length} 个需要修复的对手角色，开始修复...`);

    // 修复找到的对手角色（球场等级和阵容）
    for (const enemy of foundEnemies) {
      await this.setupEnemyFieldLevel(enemy.characterId, enemy.name);
      await this.setupDefaultFormation(enemy.characterId);
      // 避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  }

  /**
   * 创建快速对手角色并设置球场开放
   */
  async createQuickEnemies() {
    console.log('👥 创建对手角色并设置球场开放...');

    const enemies = ['测试对手', 'TestEnemy01', 'TestEnemy02'];
    const createdEnemies = [];

    for (const enemyName of enemies) {
      try {
        // 使用当前连接创建对手角色
        const response = await this.sendMessage('character.character.create', {
          userId: `enemy-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
          serverId: this.serverId,
          openId: `enemy-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
          name: enemyName,
          avatar: 'default_avatar.png'
        });

        if (response && response.payload && response.payload.success && response.payload.data && response.payload.data.code === 0) {
          const enemyCharacterId = response.payload.data.data.characterId;
          console.log(`✅ 对手角色创建成功: ${enemyName} (${enemyCharacterId})`);
          createdEnemies.push({ name: enemyName, characterId: enemyCharacterId });
        } else {
          console.log(`⚠️ 创建对手角色失败: ${enemyName}，继续测试`);
        }

      } catch (error) {
        console.log(`⚠️ 创建对手角色失败: ${enemyName} - ${error.message}，继续测试`);
      }
    }

    // 为所有成功创建的对手角色设置球场开放和阵容
    for (const enemy of createdEnemies) {
      await this.setupEnemyFieldLevel(enemy.characterId, enemy.name);
      await this.setupDefaultFormation(enemy.characterId);
    }
  }

  /**
   * 为对手角色设置球场等级和开放状态
   */
  async setupEnemyFieldLevel(characterId, enemyName) {
    try {
      console.log(`🏟️ 为对手 ${enemyName} 设置球场开放...`);

      // 设置对手角色等级
      const levelResponse = await this.sendMessage('character.character.update', {
        characterId: characterId,
        serverId: this.serverId,
        updateDto: {
          level: 10
        }
      });

      if (levelResponse && levelResponse.payload && levelResponse.payload.success && levelResponse.payload.data && levelResponse.payload.data.code === 0) {
        console.log(`✅ 对手 ${enemyName} 等级设置成功: 10级`);
      } else {
        console.log(`⚠️ 对手 ${enemyName} 等级设置失败`);
      }

      // 关键：设置对手角色的球场等级（开放球场）
      const fieldResponse = await this.sendMessage('character.character.update', {
        characterId: characterId,
        serverId: this.serverId,
        updateDto: {
          fieldLevel: 1 // 设置球场等级为1，满足 fieldLevel > 0 的开放条件
        }
      });

      if (fieldResponse && fieldResponse.payload && fieldResponse.payload.success && fieldResponse.payload.data && fieldResponse.payload.data.code === 0) {
        console.log(`✅ 对手 ${enemyName} 球场开放成功: fieldLevel=1`);
      } else {
        console.log(`⚠️ 对手 ${enemyName} 球场开放失败`);
      }

    } catch (error) {
      console.log(`⚠️ 对手 ${enemyName} 球场设置失败: ${error.message}`);
    }
  }

  /**
   * 验证设置结果
   */
  async verifySetup(characterId) {
    try {
      // 验证角色信息
      const infoResponse = await this.sendMessage('character.character.getInfo', {
        characterId: characterId,
        serverId: this.serverId
      });

      if (infoResponse && infoResponse.payload && infoResponse.payload.success && infoResponse.payload.data && infoResponse.payload.data.code === 0) {
        const char = infoResponse.payload.data.data;
        console.log('✅ 角色信息验证成功:');
        console.log(`   球币: ${char.cash}`);
        console.log(`   欧元: ${char.worldCoin}`);
        console.log(`   金币: ${char.gold}`);
        console.log(`   体力: ${char.energy}`);
      } else {
        console.log('⚠️ 角色信息验证失败');
      }

      // 验证对手搜索
      const searchResponse = await this.sendMessage('character.character.searchByName', {
        name: '测试对手',
        serverId: this.serverId
      });

      if (searchResponse && searchResponse.payload && searchResponse.payload.success && searchResponse.payload.data && searchResponse.payload.data.code === 0 && searchResponse.payload.data.data) {
        console.log('✅ 对手搜索验证成功');
      } else {
        console.log('⚠️ 对手搜索验证失败');
      }

    } catch (error) {
      console.log('⚠️ 验证过程出现错误:', error.message);
    }
  }

  /**
   * 创建默认球员
   * 为角色创建足够的球员进行布阵
   */
  async createDefaultHeroes(characterId) {
    try {
      console.log('👥 开始创建默认球员...');

      // 定义需要创建的球员位置和配置 - 使用标准HeroPosition格式
      const defaultHeroes = [
        // 门将 (1名)
        { position: 'GK', resId: 7, name: '门将1', quality: 2 },

        // 后卫 (4名)
        { position: 'DC', resId: 26, name: '中后卫1', quality: 2 },
        { position: 'DC', resId: 1500, name: '中后卫2', quality: 2 },
        { position: 'DL', resId: 1087, name: '左后卫', quality: 2 },
        { position: 'DR', resId: 1277, name: '右后卫', quality: 2 },

        // 中场 (4名)
        { position: 'MC', resId: 1345, name: '中场1', quality: 2 },
        { position: 'MC', resId: 2093, name: '中场2', quality: 2 },
        { position: 'ML', resId: 21, name: '左中场', quality: 2 },
        { position: 'MR', resId: 24, name: '右中场', quality: 2 },

        // 前锋 (3名)
        { position: 'ST', resId: 87, name: '前锋1', quality: 2 },
        { position: 'ST', resId: 855, name: '前锋2', quality: 2 },
        { position: 'WL', resId: 2584, name: '左边锋', quality: 2 },
      ];

      let createdCount = 0;
      for (const heroConfig of defaultHeroes) {
        try {
          const createResponse = await this.sendMessage('hero.hero.create', {
            characterId: characterId,
            serverId: this.serverId,
            resId: heroConfig.resId,
            name: heroConfig.name,
            position: heroConfig.position, // 使用字符串，Hero服务会处理枚举转换
            quality: heroConfig.quality,   // 使用数字，Hero服务会处理枚举转换
            level: 1,
            nationality: 'Unknown',
            club: 'TestClub',
            obtainType: 1
          });

          if (createResponse && createResponse.payload && createResponse.payload.success && createResponse.payload.data.code === 0) {
            createdCount++;
            console.log(`✅ 创建球员成功: ${heroConfig.name} (${heroConfig.position})`);
          } else {
            console.log(`⚠️ 创建球员失败: ${heroConfig.name}`, createResponse.payload?.data?.message);
          }

          // 避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.log(`⚠️ 创建球员异常: ${heroConfig.name}`, error.message);
        }
      }

      console.log(`✅ 默认球员创建完成: ${createdCount}/${defaultHeroes.length}`);
      return createdCount >= 11;

    } catch (error) {
      console.log(`⚠️ 创建默认球员失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 发送消息并等待响应
   */
  async sendMessage(command, payload) {
    return new Promise((resolve, reject) => {
      // 检查连接状态
      if (!this.socket || !this.socket.connected) {
        reject(new Error('WebSocket连接已断开'));
        return;
      }

      const messageId = `test_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      const timeoutId = setTimeout(() => {
        // 安全地移除监听器
        if (this.socket && typeof this.socket.off === 'function') {
          this.socket.off('message', responseHandler);
        }
        reject(new Error('消息超时'));
      }, 10000);

      const responseHandler = (message) => {
        if (message.id === messageId) {
          clearTimeout(timeoutId);
          // 安全地移除监听器
          if (this.socket && typeof this.socket.off === 'function') {
            this.socket.off('message', responseHandler);
          }
          resolve(message);
        }
      };

      this.socket.on('message', responseHandler);

      this.socket.emit('message', {
        id: messageId,
        command,
        payload: {
          ...payload,
          // 使用角色Token进行WebSocket通信（根据优化后的认证机制）
          token: this.characterToken || this.token
        }
      });
    });
  }

}

// 主执行函数
async function main() {
  const username = 'quick-test-1755179519720';//process.argv[2]; // 从命令行参数获取用户名
  
  const setup = new QuickTestSetup();
  
  try {
    const resultUsername = await setup.setup(username);
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 快速测试数据设置完成！');
    console.log('='.repeat(50));
    console.log(`测试用户: ${resultUsername}`);
    console.log(`服务器ID: server_001`);
    console.log('\n下一步：运行Match服务测试');
    console.log('命令: node apps/match/scripts/test-match-system.js business');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ 快速设置失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = QuickTestSetup;
